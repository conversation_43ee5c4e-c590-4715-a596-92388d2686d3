{"globals": {"AUTO_REFRESH_CONFIG": true, "BUSINESS_CONSTANTS": true, "CLIENT_DISPLAY_NAME_MAP": true, "CLIENT_OPERATION_TEXT_MAP": true, "CLIENT_STATUS_TEXT_MAP": true, "CLIENT_STATUS_TYPE_MAP": true, "CONDITION_IDENTIFIERS": true, "CSS_CLASSES": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "ConditionIdentifier": true, "DANGEROUS_OPERATIONS": true, "DEFAULT_CLIENT_NAME_FORMAT": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "FINANCE_PERIOD_OPTIONS": true, "GROWTH_RATE_TYPE_OPTIONS": true, "HEART_BEAT_TEST_HLZQ": true, "InjectionKey": true, "LOG_CONSTANTS": true, "LOG_LEVEL_ERROR": true, "LOG_LEVEL_ERROR_COLOR": true, "LOG_LEVEL_INFO": true, "LOG_LEVEL_INFO_COLOR": true, "LOG_QUERY_TYPE_IP_ADDRESS": true, "LOG_QUERY_TYPE_USER_NAME": true, "LOG_SYNC_DATABASE_QUERY_TYPE_DB_TYPE": true, "LOG_SYNC_DATABASE_QUERY_TYPE_PROFILE_TYPE": true, "LOG_SYNC_DATABASE_QUERY_TYPE_SCHEMA_NAME": true, "LOG_SYNC_DATABASE_QUERY_TYPE_SYNC_TYPE": true, "LOG_SYNC_DATABASE_QUERY_TYPE_TASK_ID": true, "LOG_SYNC_DATABASE_QUERY_TYPE_TASK_STATUS": true, "MARKET_DATA_TYPE_PRICE_CHANGE": true, "MESSAGES": true, "MQ_CONNECTION_STATUS_TEXT_MAP": true, "MQ_CONNECTION_STATUS_TYPE_MAP": true, "MQ_PAGE_TEXTS": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PAGE_TEXTS": true, "PropType": true, "RISK_WARNING_MAX_DAYS": true, "Ref": true, "SERVICE_OPERATION_TEXT_MAP": true, "SERVICE_STATUS_TEXT_MAP": true, "SERVICE_STATUS_TYPE_MAP": true, "STORAGE_KEYS": true, "Slot": true, "Slots": true, "VNode": true, "WritableComputedRef": true, "computed": true, "createApp": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "detailTitleMap": true, "effectScope": true, "formatDateTime": true, "formatDuration": true, "getClientDisplayName": true, "getCurrentInstance": true, "getCurrentScope": true, "getOperationText": true, "getStatusText": true, "getStatusType": true, "h": true, "inject": true, "isDangerousOperation": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "isValidConditionIdentifier": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useId": true, "useLink": true, "useModel": true, "useRoute": true, "useRouter": true, "useSlots": true, "useTemplateRef": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true}}