/**
 * DbTableResVo
 */
export type DbTableResVo = {
  /**
   * 数据库类型
   */
  dbType: null | string;
  /**
   * 主键ID
   */
  id: number | null;
  /**
   * 数据库名称
   */
  schemaName: null | string | any;
  /**
   * 表注释
   */
  tableComment: null | string;
  /**
   * 表创建时间
   */
  tableCreateTime: null | string;
  /**
   * 表名称
   */
  tableName: null | string | any;
  /**
   * 表总行数
   */
  tableRows: number | null;
  /**
   * 表状态
   */
  tableStatus: number | null;
  /**
   * 表类型
   */
  tableType: null | string;
  /**
   * 表更新时间
   */
  tableUpdateTime: null | string;
  [property: string]: any;
};
/**
 * DbTableDataControlDO
 */
export type DbTableDataControlDO = {
  /**
   * 操作名称
   */
  actionName: null | string;
  /**
   * 操作类型
   */
  actionType: ActionType;
  /**
   * 数据修改类型（多个用，分割）
   * 数据修改类型
   */
  alterType: AlterType;
  createTime: null | string;
  /**
   * 删除状态
   */
  deleteStatus: null | string;
  id: null | string;
  /**
   * 备注
   */
  remark: null | string;
  /**
   * 数据库名
   */
  schemaName: null | string;
  /**
   * 数据来源表（多个用，分割）
   */
  sourceTable: null | string;
  /**
   * 字段名
   * 字段名(多个用，分割)
   */
  tableColumn: null | string | any;
  /**
   * 表名
   */
  tableName: null | string;
  updateTime: null | string;
  [property: string]: any;
};

/**
 * 操作类型
 */
export enum ActionType {
  Mybatis = 'mybatis',
  MybatisPlus = 'mybatis_plus',
  MysqlTrigger = 'mysql_trigger',
  StoredProcedure = 'stored_procedure',
}

/**
 * 数据修改类型（多个用，分割）
 * 数据修改类型
 */
export enum AlterType {
  Delete = 'delete',
  Insert = 'insert',
  Update = 'update',
}

/**
 * 有效期单位
 */
export enum ValidityUnit {
  '天' = 'D',
  '月' = 'M',
  '年' = 'Y',
}
/**
 * 更新频率
 */
export enum updateFrequencyUnit {
  '年度更新' = '1',
  '半年度更新' = '2',
  '季度更新' = '3',
}
