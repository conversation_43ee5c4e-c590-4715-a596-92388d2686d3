/**
 * 系统管理/菜单管理相关模型
 */
/**
 * MenuDO
 */
export type MenuDO = {
  breadCrumb: boolean | null;
  /**
   * 组件路径
   */
  component: null | string;
  /**
   * 启用状态
   */
  enableStatus: number | null;
  hidden: boolean | null;
  /**
   * 菜单图标
   */
  icon: null | string;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 是否缓存页面
   */
  keepAlive: number | null;
  /**
   * 菜单id
   */
  menuId: null | string;
  /**
   * 菜单名称
   */
  name: null | string;
  /**
   * 菜单排序字段
   */
  orderNum: number | null;
  /**
   * 父菜单
   */
  parentId: null | string;
  /**
   * 菜单路径
   */
  path: null | string;
  /**
   * 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
   */
  redirect: null | string;
  /**
   * 菜单标题
   */
  title: null | string;
  [property: string]: any;
};
