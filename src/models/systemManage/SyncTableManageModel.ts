/**
 * @file SyncTableManageModel.ts
 * @description 同步表管理相关的类型定义和数据模型
 * @date 2025/01/16
 * <AUTHOR> Assistant
 */

import type { PageRequest } from '@/models/common/baseRequest';
import type { CommonStatus, ProfileType } from '@/enums/baseEnum';

/**
 * 同步表配置数据传输对象接口
 * @interface SyncTableConfigDTO
 * @description 定义同步表配置的数据结构
 */
export interface SyncTableConfigDTO {
  /** 配置ID */
  id: number;
  /** 项目ID */
  projectId: number | null;
  /** 环境类型 */
  profileType: ProfileType | null;
  /** 数据库类型 */
  dbType: string | null;
  /** 模式名称 */
  schemaName: string | null;
  /** 任务类型 */
  taskType: string | null;
  /** 启用状态 */
  enableStatus: CommonStatus;
  /** 是否全量更新 */
  ifFullUpdate: number | null;
  /** 表名 */
  tableName: string | null;
  /** 数据表名 */
  dataTableName: string | null;
  /** 数据源库名称 */
  dataSchemaName?: string | null;
  /** 表注释 */
  tableComment?: string | null;
  /** cron表达式 */
  crontab?: string | null;
  /** 表数据分组字段 */
  tableGroupByColumn?: string | null;
  /** 顺序 */
  syncOrder?: number | null;
  /** 不包含列 */
  excludeColumn?: string | null;
  /** 包含列 */
  includeColumn?: string | null;
  /** 表行数 */
  tableRows?: number | null;
  /** 每批大小 */
  batchSize?: number | null;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/**
 * 查询参数接口
 * @interface SyncTableQueryParams
 * @description 定义同步表查询的参数结构
 */
export interface SyncTableQueryParams extends PageRequest {
  /** 项目ID */
  projectId?: number | null;
  /** 环境类型 */
  profileType?: ProfileType | null;
  /** 数据库类型 */
  dbType?: string | null;
  /** 模式名称 */
  schemaName?: string | null;
  /** 任务类型 */
  taskType?: string | null;
  /** 启用状态 */
  enableStatus?: CommonStatus;
  /** 是否全量更新 */
  ifFullUpdate?: number | null;
  /** 表名 */
  tableName?: string | null;
}

/**
 * 表单缓存配置接口
 * @interface SyncTableCacheConfig
 * @description 定义表单缓存相关的配置
 */
export interface SyncTableCacheConfig {
  /** 表单数据缓存键 */
  readonly FORM_CACHE_KEY: string;
  /** 表单缓存时间戳键 */
  readonly FORM_CACHE_TIMESTAMP_KEY: string;
  /** 缓存过期时间（毫秒） */
  readonly CACHE_EXPIRE_TIME: number;
}

/**
 * 同步表缓存配置常量
 * @description 定义同步表管理页面的缓存配置
 */
export const SYNC_TABLE_CACHE_CONFIG: SyncTableCacheConfig = {
  FORM_CACHE_KEY: 'sync_table_form_cache',
  FORM_CACHE_TIMESTAMP_KEY: 'sync_table_form_cache_timestamp',
  CACHE_EXPIRE_TIME: 24 * 60 * 60 * 1000, // 24小时
} as const;

/**
 * 创建默认的同步表查询参数
 * @description 返回同步表查询的默认参数
 * @returns 默认查询参数对象
 */
export function createDefaultSyncTableQueryParams(): Partial<SyncTableQueryParams> {
  return {
    projectId: null,
    profileType: null,
    dbType: null,
    schemaName: null,
    taskType: null,
    enableStatus: 0,
    ifFullUpdate: null,
    tableName: null,
  };
}

/**
 * 创建默认的同步表配置对象
 * @description 返回新增同步表配置时的默认值
 * @returns 默认配置对象
 */
export function createDefaultSyncTableConfig(): SyncTableConfigDTO {
  return {
    id: 0,
    projectId: null,
    profileType: null,
    dbType: null,
    schemaName: null,
    taskType: null,
    enableStatus: 0 as CommonStatus,
    ifFullUpdate: 0,
    tableName: null,
    dataTableName: null,
    createTime: '',
    updateTime: '',
  };
}
