/**
 * 系统管理/用户配置相关模型
 */

/**
 * DataRangeDictDO
 */
export type DataRangeDictDO = {
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 删除状态
   */
  deleteStatus: null | string;
  /**
   * 主键
   */
  dictId: null | string;
  /**
   * 启用状态
   */
  enableStatus: null | string;
  /**
   * 数据项描述
   */
  itemDescription: null | string;
  /**
   * 数据项名称
   */
  itemName: null | string;
  /**
   * 更新时间
   */
  updateTime: null | string;
  [property: string]: any;
};

/**
 * DataRangeStandardDO
 */
export type DataRangeStandardDO = {
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 删除状态
   */
  deleteStatus: null | string;
  /**
   * 数据项字典ID
   */
  dictId: null | string;
  /**
   * 启用状态
   */
  enableStatus: null | string;
  id: null | string;
  /**
   * 备注
   */
  remark: null | string;
  /**
   * 数据区间标准名称
   */
  standardName: null | string;
  /**
   * 更新时间
   */
  updateTime: null | string;
  /**
   * 用户ID
   */
  userId: null | string;
  [property: string]: any;
};

/**
 * DataRangeDetailDO
 */
export type DataRangeDetailDO = {
  id: null | string;
  /**
   * 是否包含下限（0表示包含，1表示不包含）
   */
  inclusiveLower: null | string | number;
  /**
   * 是否包含上限（0表示包含，1表示不包含）
   */
  inclusiveUpper: null | string | number;
  /**
   * 区间下限
   */
  lowerBound: number | null;
  /**
   * 区间描述
   */
  rangeDescription: null | string;
  /**
   * 当前区间顺序
   */
  rangeOrder: number | null;
  /**
   * 区间标准ID
   */
  standardId: null | string;
  /**
   * 区间上限
   */
  upperBound: number | null;
  [property: string]: any;
};
