/**
 * @file: serviceManageModels
 * @description: 系统管理/恒生柜台T2SDK服务管理和MQ连接管理相关类型定义
 * @date: 2025/07/15
 * @author: AI Assistant
 */

// ==================== 基础类型定义 ====================

/**
 * T2SDK服务状态枚举
 * @description 定义T2SDK服务的所有可能状态
 */
export enum ServiceStatus {
  /** 运行中 */
  RUNNING = 'RUNNING',
  /** 已停止 */
  STOPPED = 'STOPPED',
  /** 启动中 */
  STARTING = 'STARTING',
  /** 停止中 */
  STOPPING = 'STOPPING',
  /** 错误状态 */
  ERROR = 'ERROR',
  /** 未配置 */
  NOT_CONFIGURED = 'NOT_CONFIGURED',
}

/**
 * 服务操作类型枚举
 * @description 定义可执行的服务操作类型
 */
export enum ServiceOperation {
  /** 启动操作 */
  START = 'start',
  /** 停止操作 */
  STOP = 'stop',
  /** 重启操作 */
  RESTART = 'restart',
}

/**
 * 客户端状态类型
 * @description 客户端状态的联合类型定义
 */
export type ClientStatus = ServiceStatus;

/**
 * 客户端操作类型
 * @description 客户端操作的联合类型定义
 */
export type ClientOperation = ServiceOperation;

// ==================== 接口定义 ====================

/**
 * T2SDK服务状态数据传输对象
 * @interface T2SdkServiceStatusDTO
 * @description 定义T2SDK服务状态的完整数据结构
 */
export interface T2SdkServiceStatusDTO {
  /**
   * 服务状态
   * @type {ServiceStatus}
   * @description 服务当前运行状态
   */
  status: ServiceStatus;

  /**
   * 状态描述
   * @type {string}
   * @description 服务状态的详细描述信息
   */
  statusDescription: string;

  /**
   * 是否启用
   * @type {boolean}
   * @description 服务是否被启用
   */
  enabled: boolean;

  /**
   * 启动时间
   * @type {string}
   * @description 服务启动的时间戳，ISO 8601 格式
   */
  startTime: string;

  /**
   * 运行时长
   * @type {number}
   * @description 服务运行的时长（毫秒）
   */
  runningDuration: number;

  /**
   * 错误信息
   * @type {string}
   * @description 如果服务出现错误，此字段包含错误详情
   */
  errorMessage: string;

  /**
   * 最后更新时间
   * @type {string}
   * @description 状态最后更新的时间戳，ISO 8601 格式
   */
  lastUpdateTime: string;
}

/**
 * T2SDK客户端状态数据传输对象
 * @interface T2SdkClientStatusDTO
 * @description 定义T2SDK客户端状态的完整数据结构
 */
export interface T2SdkClientStatusDTO {
  /**
   * 客户端名称
   * @type {string}
   * @description 客户端的唯一标识名称
   */
  clientName: string;

  /**
   * 客户端状态
   * @type {ClientStatus}
   * @description 客户端当前运行状态
   */
  status: ClientStatus;

  /**
   * 状态描述
   * @type {string}
   * @description 客户端状态的详细描述信息
   */
  statusDescription: string;

  /**
   * 是否已配置
   * @type {boolean}
   * @description 客户端是否已正确配置
   */
  configured: boolean;

  /**
   * 是否可用
   * @type {boolean}
   * @description 客户端是否可用于连接
   */
  available: boolean;

  /**
   * 启动时间
   * @type {string}
   * @description 客户端启动的时间戳，ISO 8601 格式
   */
  startTime: string;

  /**
   * 运行时长
   * @type {number}
   * @description 客户端运行的时长（毫秒）
   */
  runningDuration: number;

  /**
   * 错误信息
   * @type {string}
   * @description 如果客户端出现错误，此字段包含错误详情
   */
  errorMessage: string;

  /**
   * 最后更新时间
   * @type {string}
   * @description 状态最后更新的时间戳，ISO 8601 格式
   */
  lastUpdateTime: string;
}

// ==================== 请求参数类型 ====================

/**
 * 批量客户端操作请求参数
 * @interface BatchClientOperationRequest
 * @description 批量操作客户端时的请求参数
 */
export interface BatchClientOperationRequest {
  /**
   * 客户端名称列表
   * @type {string[]}
   * @description 要操作的客户端名称数组
   */
  clientNames: string[];

  /**
   * 操作类型
   * @type {ClientOperation}
   * @description 要执行的操作类型
   */
  operation: ClientOperation;
}

// ==================== 组件状态类型 ====================

/**
 * 服务管理组件状态
 * @interface ServiceManageState
 * @description 定义服务管理组件的内部状态结构
 */
export interface ServiceManageState {
  /**
   * 服务状态
   * @type {T2SdkServiceStatusDTO}
   * @description 当前T2SDK服务的状态信息
   */
  serviceStatus: T2SdkServiceStatusDTO;

  /**
   * 客户端状态映射
   * @type {Record<string, T2SdkClientStatusDTO>}
   * @description 以客户端名称为键的状态映射
   */
  clientsStatus: Record<string, T2SdkClientStatusDTO>;

  /**
   * 选中的客户端列表
   * @type {string[]}
   * @description 用户选中的客户端名称列表
   */
  selectedClients: string[];

  /**
   * 刷新状态
   * @type {boolean}
   * @description 是否正在刷新状态
   */
  refreshing: boolean;

  /**
   * 服务操作状态
   * @type {boolean}
   * @description 是否正在执行服务操作
   */
  serviceOperating: boolean;

  /**
   * 当前服务操作
   * @type {ServiceOperation | ''}
   * @description 当前正在执行的服务操作类型
   */
  currentOperation: ServiceOperation | '';

  /**
   * 客户端操作状态映射
   * @type {Record<string, boolean>}
   * @description 各客户端的操作状态
   */
  clientOperating: Record<string, boolean>;

  /**
   * 当前客户端操作映射
   * @type {Record<string, ClientOperation | ''>}
   * @description 各客户端当前正在执行的操作
   */
  currentClientOperation: Record<string, ClientOperation | ''>;
}

// ==================== 工厂函数 ====================

/**
 * 创建默认的服务状态对象
 * @function createDefaultServiceStatus
 * @returns {T2SdkServiceStatusDTO} 默认的服务状态对象
 */
export const createDefaultServiceStatus = (): T2SdkServiceStatusDTO => ({
  status: ServiceStatus.STOPPED,
  statusDescription: '',
  enabled: false,
  startTime: '',
  runningDuration: 0,
  errorMessage: '',
  lastUpdateTime: '',
});

/**
 * 创建默认的组件状态对象
 * @function createDefaultServiceManageState
 * @returns {ServiceManageState} 默认的组件状态对象
 */
export const createDefaultServiceManageState = (): ServiceManageState => ({
  serviceStatus: createDefaultServiceStatus(),
  clientsStatus: {},
  selectedClients: [],
  refreshing: false,
  serviceOperating: false,
  currentOperation: '',
  clientOperating: {},
  currentClientOperation: {},
});

// ==================== MQ连接管理相关类型定义 ====================

/**
 * MQ工厂状态枚举
 * @description 定义MQ工厂的所有可能状态，与后端MqFactoryStatusEnum保持一致
 */
export enum MqConnectionStatus {
  /** 未初始化 */
  NOT_INITIALIZED = 'NOT_INITIALIZED',
  /** 运行中 */
  RUNNING = 'RUNNING',
  /** 已停止 */
  STOPPED = 'STOPPED',
  /** 初始化失败 */
  INIT_FAILED = 'INIT_FAILED',
}

/**
 * MQ操作类型枚举
 * @description 定义可执行的MQ操作类型
 */
export enum MqOperation {
  /** 启动操作 */
  START = 'start',
  /** 停止操作 */
  STOP = 'stop',
  /** 重启操作 */
  RESTART = 'restart',
  /** 测试发送 */
  TEST_SEND = 'test_send',
}

/**
 * MQ工厂操作数据传输对象
 * @interface MqFactoryOperationDTO
 * @description MQ工厂操作的响应数据结构
 */
export interface MqFactoryOperationDTO {
  /**
   * 操作是否成功
   * @type {boolean}
   * @description 操作执行结果
   */
  success: boolean;

  /**
   * 操作消息
   * @type {string}
   * @description 操作结果的详细描述
   */
  message: string;

  /**
   * 操作时间戳
   * @type {string}
   * @description 操作执行的时间，ISO 8601 格式
   */
  timestamp: string;

  /**
   * 操作类型
   * @type {string}
   * @description 执行的操作类型
   */
  operationType: string;
}

/**
 * MQ工厂状态数据传输对象
 * @interface MqFactoryStatusDTO
 * @description MQ工厂状态的响应数据结构
 */
export interface MqFactoryStatusDTO {
  /**
   * 连接状态
   * @type {MqConnectionStatus}
   * @description MQ连接当前状态
   */
  status: MqConnectionStatus;

  /**
   * 状态描述
   * @type {string}
   * @description 连接状态的详细描述
   */
  statusDescription: string;

  /**
   * 是否可用
   * @type {boolean}
   * @description MQ连接是否可用
   */
  available: boolean;

  /**
   * 启动时间
   * @type {string}
   * @description MQ连接启动的时间，ISO 8601 格式
   */
  startTime: string;

  /**
   * 运行时长
   * @type {number}
   * @description 连接运行的时长（秒）
   */
  runningDuration: number;

  /**
   * 错误信息
   * @type {string}
   * @description 如果连接出现错误，此字段包含错误详情
   */
  errorMessage: string;

  /**
   * 最后更新时间
   * @type {string}
   * @description 状态最后更新的时间，ISO 8601 格式
   */
  lastUpdateTime: string;


}



/**
 * MQ工厂重启数据传输对象
 * @interface MqFactoryRestartDTO
 * @description MQ工厂重启操作的响应数据结构
 */
export interface MqFactoryRestartDTO {
  /**
   * 重启是否成功
   * @type {boolean}
   * @description 重启操作执行结果
   */
  success: boolean;

  /**
   * 重启消息
   * @type {string}
   * @description 重启结果的详细描述
   */
  message: string;

  /**
   * 重启前状态
   * @type {MqConnectionStatus}
   * @description 重启前的连接状态
   */
  previousStatus: MqConnectionStatus;

  /**
   * 重启后状态
   * @type {MqConnectionStatus}
   * @description 重启后的连接状态
   */
  currentStatus: MqConnectionStatus;

  /**
   * 重启时间戳
   * @type {string}
   * @description 重启操作执行的时间，ISO 8601 格式
   */
  restartTime: string;

  /**
   * 重启耗时
   * @type {number}
   * @description 重启操作耗时（毫秒）
   */
  restartDuration: number;
}

/**
 * MQ工厂测试数据传输对象
 * @interface MqFactoryTestDTO
 * @description MQ工厂测试发送的响应数据结构
 */
export interface MqFactoryTestDTO {
  /**
   * 测试是否成功
   * @type {boolean}
   * @description 测试发送操作执行结果
   */
  success: boolean;

  /**
   * 测试消息
   * @type {string}
   * @description 测试结果的详细描述
   */
  message: string;

  /**
   * 测试消息ID
   * @type {string}
   * @description 发送的测试消息的唯一标识
   */
  messageId: string;

  /**
   * 目标队列
   * @type {string}
   * @description 测试消息发送的目标队列
   */
  targetQueue: string;

  /**
   * 发送时间戳
   * @type {string}
   * @description 测试消息发送的时间，ISO 8601 格式
   */
  sendTime: string;

  /**
   * 响应时间
   * @type {number}
   * @description 消息发送的响应时间（毫秒）
   */
  responseTime: number;
}

/**
 * MQ管理状态接口
 * @interface MqManageState
 * @description MQ连接管理组件的状态管理接口
 */
export interface MqManageState {
  /**
   * MQ连接状态
   * @type {MqFactoryStatusDTO}
   * @description 当前MQ连接的状态信息
   */
  mqStatus: MqFactoryStatusDTO;

  /**
   * 是否正在刷新状态
   * @type {boolean}
   * @description 标识是否正在执行状态刷新操作
   */
  refreshing: boolean;

  /**
   * 是否正在执行MQ操作
   * @type {boolean}
   * @description 标识是否正在执行MQ相关操作（启动、停止、重启等）
   */
  mqOperating: boolean;

  /**
   * 当前执行的操作
   * @type {string}
   * @description 当前正在执行的操作描述
   */
  currentOperation: string;

  /**
   * 最后一次测试结果
   * @type {MqFactoryTestDTO | null}
   * @description 最后一次测试发送的结果
   */
  lastTestResult: MqFactoryTestDTO | null;

  /**
   * 操作历史记录
   * @type {MqOperationRecord[]}
   * @description MQ操作的历史记录列表
   */
  operationHistory: MqOperationRecord[];
}

/**
 * MQ操作记录接口
 * @interface MqOperationRecord
 * @description MQ操作历史记录的数据结构
 */
export interface MqOperationRecord {
  /**
   * 记录ID
   * @type {string}
   * @description 操作记录的唯一标识
   */
  id: string;

  /**
   * 操作类型
   * @type {MqOperation}
   * @description 执行的操作类型
   */
  operation: MqOperation;

  /**
   * 操作时间
   * @type {string}
   * @description 操作执行的时间，ISO 8601 格式
   */
  timestamp: string;

  /**
   * 操作结果
   * @type {boolean}
   * @description 操作是否成功
   */
  success: boolean;

  /**
   * 操作消息
   * @type {string}
   * @description 操作结果的详细描述
   */
  message: string;

  /**
   * 操作耗时
   * @type {number}
   * @description 操作执行耗时（毫秒）
   */
  duration: number;
}

// ==================== MQ管理默认值创建函数 ====================

/**
 * 创建默认的MQ连接状态对象
 * @function createDefaultMqStatus
 * @description 创建一个包含默认值的MQ连接状态对象
 * @returns {MqFactoryStatusDTO} 默认的MQ连接状态对象
 */
export const createDefaultMqStatus = (): MqFactoryStatusDTO => ({
  status: MqConnectionStatus.NOT_INITIALIZED,
  statusDescription: '未初始化',
  available: false,
  startTime: '',
  runningDuration: 0,
  errorMessage: '',
  lastUpdateTime: new Date().toISOString(),

});

/**
 * 创建默认的MQ管理状态对象
 * @function createDefaultMqManageState
 * @description 创建一个包含默认值的MQ管理状态对象
 * @returns {MqManageState} 默认的MQ管理状态对象
 */
export const createDefaultMqManageState = (): MqManageState => ({
  mqStatus: createDefaultMqStatus(),
  refreshing: false,
  mqOperating: false,
  currentOperation: '',
  lastTestResult: null,
  operationHistory: [],
});

/**
 * 创建MQ操作记录
 * @function createMqOperationRecord
 * @description 创建一个MQ操作记录对象
 * @param {MqOperation} operation - 操作类型
 * @param {boolean} success - 操作是否成功
 * @param {string} message - 操作消息
 * @param {number} duration - 操作耗时
 * @returns {MqOperationRecord} MQ操作记录对象
 */
export const createMqOperationRecord = (
  operation: MqOperation,
  success: boolean,
  message: string,
  duration: number = 0
): MqOperationRecord => ({
  id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  operation,
  timestamp: new Date().toISOString(),
  success,
  message,
  duration,
});
