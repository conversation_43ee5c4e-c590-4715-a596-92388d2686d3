/**
 * 日志管理相关数据模型
 */

/**
 * SyncDataTaskDO
 */
export type SyncDataTaskDO = {
  /**
   * 同步库类型
   */
  dbType: null | string;
  /**
   * 同步表数量
   */
  syncTableCount: number | null;
  /**
   * 同步任务日期
   */
  taskDate: null | string;
  /**
   * 任务结束时间
   */
  taskEndTime: null | string;
  /**
   * 同步数据任务id
   */
  taskId: number | null;
  /**
   * 任务开始时间
   */
  taskStartTime: null | string;
  /**
   * 同步任务执行状态：0执行成功，1执行未完成
   */
  taskStatus: number;
  [property: string]: any;
};
