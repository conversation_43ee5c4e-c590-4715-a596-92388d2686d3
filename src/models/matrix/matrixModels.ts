/**
 * @file: matrixModels
 * @description: 矩阵相关数据模型
 * @date: 2024/8/29
 * @author: <PERSON>
 */

import { TypeCountVO, ValueRangeVO } from '@/models/common/utilModels';
import { CommonStatus } from '@/enums/baseEnum';
import { SelectOption } from 'naive-ui';
import { MatrixCellSecCountVO } from '@/models/peerData/peerDataModels';

/**
 * MatrixChartItem - 矩阵图表数据项
 * @description 用于策略矩阵3D图表展示的单个矩阵数据结构
 */
export interface MatrixChartItem {
  /** 矩阵ID */
  matrixId: number | null;
  /** 矩阵名称/策略类型 */
  name: string | null;
  /** 创建用户名称 */
  userName: string | null;
  /** 股票数量 */
  number: number | null;
  /** 矩阵日期选项列表 */
  matrixDateOption?: SelectOption[];
  /** 选中的矩阵日期 */
  matrixDate?: string | null;
  /** 股票类型 */
  stockType?: string | null;
  /** X轴坐标数据（横坐标） */
  xData?: string[] | null;
  /** Y轴坐标数据（纵坐标） */
  yData?: string[] | null;
  /** 3D图表数据点数组 [纵坐标, 横坐标, 数量] */
  countAll?: [string | null, string | null, number | null][];
}

/**
 * MatrixBaseReq - 矩阵基础请求参数
 * @param {number} matrixId 矩阵id
 * @param {string} matrixDate 矩阵日期
 * @param {string} stockType 股票类型
 */
export interface MatrixBaseReq {
  matrixId?: number | null;
  matrixDate?: string | null;
  stockType?: string | null;
}

/**
 * MatrixInfoVO - 矩阵信息
 * @param {number} matrixId 矩阵id
 * @param {string} type 矩阵名称
 * @param {string} userName 创建用户
 * @param {number} number 矩阵股票数量
 * @param {MatrixCellVO[]} list 矩阵横纵坐标对应股票数量
 */
export interface MatrixInfoVO {
  /**
   * 矩阵横纵坐标对应股票数量
   */
  list: MatrixCellSecCountVO[] | null;
  /**
   * 矩阵id
   */
  matrixId: number | null;
  /**
   * 矩阵股票数量
   */
  number: number | null;
  /**
   * 矩阵名称
   */
  type: null | string;
  /**
   * 创建用户
   */
  userName: null | string;

  /**
   * 矩阵可选日期字符串
   */
  matrixDateStr: null | string;

  [property: string]: any;
}

/**
 * MatrixCellVO - 矩阵单元格信息
 * @param {string} across 横坐标
 * @param {number} count 股票数量
 * @param {number} flag 是否包含当前股票
 * @param {number} labels 标签组合数量
 * @param {string} vertical 纵坐标
 */
export interface MatrixCellVO {
  /**
   * 横坐标
   */
  across?: null | string;
  /**
   * 股票数量
   */
  count?: number | null;
  /**
   * 是否包含当前股票
   */
  flag?: number | null;
  /**
   * 标签组合数量
   */
  labels?: number | null;
  /**
   * 纵坐标
   */
  vertical?: null | string;

  [property: string]: any;
}

/**
 * MatrixCellAnalysisVO - 矩阵单元格分析
 * @param {ValueRangeVO} financeScore 财务评分区间
 * @param {ValueRangeVO} income 营业收入区间
 * @param {MatrixCellTableVO[]} info 证券表格详情
 * @param {MatrixLabelCombineDO[]} labelList 标签覆盖度列表
 * @param {TypeCountVO[]} level 评级区间
 * @param {ValueRangeVO} overallScore 综合评分区间
 * @param {TypeCountVO[]} ttmAnalysis 市值区间
 */
export interface MatrixCellAnalysisVO {
  /**
   * 财务评分区间
   */
  financialScore: ValueRangeVO;
  /**
   * 营业收入区间
   */
  income: ValueRangeVO;
  /**
   * 证券表格详情
   */
  info: MatrixCellTableVO[];
  /**
   * 标签覆盖度列表
   */
  labelList: MatrixLabelCombineDO[];
  /**
   * 评级区间
   */
  level: TypeCountVO[];
  /**
   * 综合评分区间
   */
  overallScore: ValueRangeVO;
  /**
   * 市值区间
   */
  ttmAnalysis: TypeCountVO[];
}

/**
 * MatrixCellTableVO - 矩阵单元格表格详情
 * @param {number} closePrice 最新股价(元)
 * @param {number} grossMarginSub 3年-5年营收增速差(%)
 * @param {number} highRiskNum 高风险标签数量
 * @param {number} incomeDebtSub 3年-5年营收负债增速差(%)
 * @param {string} industryName 一级行业名称
 * @param {number} kfNetProfit 扣非净利润(亿元)
 * @param {number} kfSub 3年-5年扣非净利润增速差(%)
 * @param {string} level 系统评级
 * @param {number} marginBalance 市场融资负债(万元)
 * @param {number} netAssetValuePerShare 每股净资产(元/股)
 * @param {string} secCode 证券代码
 * @param {string} secName 证券名称
 * @param {number} totalAmount 最新市值(亿元)
 */
export interface MatrixCellTableVO {
  /**
   * 最新股价(元)
   */
  closePrice?: number | null;
  /**
   * 3年-5年毛利率增速差(%)
   */
  grossMarginSub: number | null;
  /**
   * 高风险标签数量
   */
  highRiskNum?: number | null;
  /**
   * 3年-5年营收负债增速差(%)
   */
  incomeDebtSub: number | null;
  /**
   * 一级行业名称
   */
  industryName?: null | string;
  /**
   * 扣非净利润(亿元)
   */
  kfNetProfit: number | null;
  /**
   * 3年-5年扣非净利润增速差(%)
   */
  kfSub: number | null;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 市场融资负债(万元)
   */
  marginBalance: number | null;
  /**
   * 每股净资产(元/股)
   */
  netAssetValuePerShare: number | null;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 证券名称
   */
  secName?: null | string;
  /**
   * 最新市值(亿元)
   */
  totalAmount?: number | null;
}

/**
 * MatrixLabelCombineDO - 标签覆盖度
 * @param {string} across 横坐标
 * @param {number} configId 配置表id
 * @param {number} coverNum 标签覆盖股票数量
 * @param {string} labelCombine 标签组合，用，分隔
 * @param {number} matrixId 矩阵id
 * @param {number} ratio 覆盖股票比例阈值
 * @param {string} vertical 纵坐标
 * @param {number} value 覆盖股票数量阈值
 */
export interface MatrixLabelCombineDO {
  /**
   * 矩阵横坐标
   */
  across?: null | string;
  /**
   * 配置表id
   */
  configId?: number | null;
  /**
   * 标签覆盖股票数量
   */
  coverNum?: number | null;
  /**
   * 标签组合，用，分隔
   */
  labelCombine: null | string;
  /**
   * 矩阵id
   */
  matrixId?: number | null;
  ratio?: number | null;
  /**
   * 覆盖股票比例阈值
   */
  value?: number | null;
  /**
   * 矩阵纵坐标
   */
  vertical?: null | string;

  [property: string]: any;
}

/**
 * MatrixConfigDO - 矩阵配置
 * @param {string} acrossItem 横向维度
 * @param {number} id 主键ID
 * @param {number} matrixOrder 矩阵排序
 * @param {number} status 矩阵启用状态
 * @param {string} type 矩阵名称
 * @param {CommonStatus} enableStatus 矩阵启用状态
 * @param {string} userId 创建用户ID
 * @param {string} userName 创建用户名称
 * @param {string} verticalItem 纵向维度
 */
export interface MatrixConfigDO {
  /**
   * 横向维度
   */
  acrossItem: null | string;
  /**
   * 主键ID
   */
  id: number | null;
  /**
   * 矩阵排序
   */
  matrixOrder: number | null;
  status: number | null;
  /**
   * 矩阵名称
   */
  type: null | string;
  /**
   * 矩阵启用状态
   */
  enableStatus: CommonStatus;
  /**
   * 创建用户ID
   */
  userId: null | string;
  userName: null | string;
  /**
   * 纵向维度
   */
  verticalItem: null | string;

  [property: string]: any;
}

/**
 * MatrixOptionVO - 矩阵选项
 * @interface MatrixOptionVO
 * @property {MatrixOptionListed[]} itemList - 返回给前端的矩阵选项列表
 * @property {null|string} type - 返回给前端的矩阵选项类型
 */
export interface MatrixOptionVO {
  /**
   * 返回给前端的矩阵选项列表
   */
  itemList: MatrixOptionListed[] | null;
  /**
   * 返回给前端的矩阵选项类型
   */
  type: null | string;

  [property: string]: any;
}

/**
 * MatrixOptionListed
 */
export interface MatrixOptionListed {
  /**
   * 可选计算选项对应名称
   */
  item: null | string;
  /**
   * 可选计算选项
   */
  itemName: null | string;
  /**
   * 横纵坐标类型
   */
  itemType: null | string;
  /**
   * 启用状态:0-启用，1-禁用
   */
  status: CommonStatus;
  /**
   * 选项对应表名
   */
  tableName: null | string;
  /**
   * 显示在前端的类型
   */
  type: null | number;
  /**
   * 计算选项的单位
   */
  unit: null | string;

  [property: string]: any;
}

/**
 * MatrixAcrossVerticalVO - 矩阵对应的横纵坐标的返回类
 * @interface MatrixAcrossVerticalVO
 * @property {GridDictVO[]} across - 横坐标列表
 * @property {null|string} acrossItem - 横坐标名称
 * @property {null|string} type - 矩阵名称
 * @property {null|string} verticalItem - 纵坐标名称
 * @property {GridDictVO[]} vertigo - 纵坐标列表
 */
export interface MatrixAcrossVerticalVO {
  /**
   * 横坐标名称列表
   */
  across: GridDictVO[] | null;
  /**
   * 横坐标名称
   */
  acrossItem: null | string;
  /**
   * 矩阵名称
   */
  type: null | string;
  /**
   * 纵坐标名称
   */
  verticalItem: null | string;
  /**
   * 纵坐标名称列表
   */
  vertigo: GridDictVO[] | null;

  [property: string]: any;
}

/**
 * GridDictVO - 横纵坐标字典，用于显示表格的所有横纵坐标
 * @interface GridDictVO
 * @property {null|string} across - 横坐标
 * @property {number} id - id顺序
 * @property {null|string} vertical - 纵坐标
 */
export interface GridDictVO {
  /**
   * 横坐标
   */
  across: string;
  /**
   * id顺序
   */
  id: number;
  /**
   * 纵坐标
   */
  vertical: string;

  [property: string]: any;
}
