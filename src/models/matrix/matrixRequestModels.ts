/**
 * @file: matrixRequestModels
 * @description: 矩阵请求相关的数据模型
 * @date: 2024/8/30
 * @author: <PERSON> Ye
 */

/**
 * MatrixCellAnaReq - 矩阵单元格分析请求数据模型
 * @interface MatrixCellAnaReq
 * @property {null|string} across - 横坐标
 * @property {null|string} actualLevel - 策略评级
 * @property {null|string} matrixDate - 矩阵日期
 * @property {number|null} matrixId - 矩阵id
 * @property {null|string} stockType - 证券板块类型
 * @property {number|null} strategyId - 评级策略ID
 * @property {null|string} vertical - 纵坐标
 */
export interface MatrixCellAnaReq {
  /**
   * 横坐标
   */
  across?: null | string;
  /**
   * 策略评级
   */
  actualLevel?: null | string;
  /**
   * 矩阵日期
   */
  matrixDate?: null | string;
  /**
   * 矩阵id
   */
  matrixId?: number | null;
  /**
   * 证券板块类型
   */
  stockType?: null | string;
  /**
   * 评级策略ID
   */
  strategyId?: number | null;
  /**
   * 纵坐标
   */
  vertical?: null | string;

  /**
   * 标签组合
   */
  labelCombo?: string | null;

  /**
   * 区间详情
   */
  rangeDetail?: string | null;
  [property: string]: any;
}
