/**
 * 压力测试相关模型
 */
/**
 * StressTestTaskDTO
 */
export type StressTestTaskDTO = {
  /**
   * 关注线
   */
  attentionLine: number | null;
  /**
   * 计算状态：0-计算中，1-未在计算中
   */
  calculateStatus: number;
  /**
   * 压测客户范围
   */
  clientRange: number;
  /**
   * 平仓线
   */
  closingLine: number | null;
  createTime: null | string;
  /**
   * 自定义压测的客户列表
   */
  customClientList: StressTestCustomClientDO[] | null;
  /**
   * 最近压测报告
   */
  lastReport: null | string;
  /**
   * 最近压测报告总览
   */
  lastReportOverviewSummary: null | StressTestReportOverviewSummaryVO;
  /**
   * 涨跌幅设置方式
   */
  priceChangeType: PriceChangeType;
  /**
   * 压力测试任务用户
   */
  taskAuthor: null | string;
  /**
   * 压力测试摘要
   */
  taskDigest: null | string;
  /**
   * 任务id
   */
  taskId: number | null;
  /**
   * 压力测试任务名称
   */
  taskName: null | string;
  /**
   * 定时调度任务
   */
  taskSchedule: null | StressTestTaskScheduleDO;
  /**
   * 压力测试任务计算设置
   */
  taskSettings: StressTestTaskSettingDTO[] | null;
  /**
   * 压测任务类型
   */
  taskType: number;
  /**
   * 压力测试条件类型
   */
  testType: number;
  /**
   * 警戒线
   */
  warningLine: number | null;
  [property: string]: any;
};

/**
 * StressTestCustomClientDO
 */
export type StressTestCustomClientDO = {
  /**
   * 客户代码
   */
  clientId: null | string;
  /**
   * 资金账号
   */
  fundAccount: null | string;
  id: number | null;
  /**
   * 压测任务id
   */
  taskId: number | null;
  [property: string]: any;
};

/**
 * 最近压测报告总览
 *
 * StressTestReportOverviewSummaryVO
 */
export type StressTestReportOverviewSummaryVO = {
  /**
   * 坏账金额(万元)
   */
  badDebtAmount: null | StressTestConditionDTO;
  /**
   * 坏账客户数
   */
  badDebtClientCount: null | StressTestConditionDTO;
  /**
   * 低于关注线金额(万元)
   */
  belowAttentionLineAmount: null | StressTestConditionDTO;
  /**
   * 低于关注线客户数
   */
  belowAttentionLineClientCount: null | StressTestConditionDTO;
  /**
   * 需平仓金额(万元)
   */
  belowCloseLineAmount: null | StressTestConditionDTO;
  /**
   * 需平仓客户数
   */
  belowCloseLineClientCount: null | StressTestConditionDTO;
  /**
   * 需追保金额(万元)
   */
  belowMarginCallLineAmount: null | StressTestConditionDTO;
  /**
   * 需追保客户数
   */
  belowMarginCallLineClientCount: null | StressTestConditionDTO;
  [property: string]: any;
};

/**
 * 坏账金额(万元)
 *
 * StressTestConditionDTO
 *
 * 坏账客户数
 *
 * 低于关注线金额(万元)
 *
 * 低于关注线客户数
 *
 * 需平仓金额(万元)
 *
 * 需平仓客户数
 *
 * 需追保金额(万元)
 *
 * 需追保客户数
 */
export type StressTestConditionDTO = {
  /**
   * 压测前
   */
  beforeTest: null | string;
  /**
   * 场景一
   */
  conditionOne: null | string;
  /**
   * 场景三
   */
  conditionThree: null | string;
  /**
   * 场景二
   */
  conditionTwo: null | string;
  [property: string]: any;
};

/**
 * 涨跌幅设置方式
 */
export enum PriceChangeType {
  涨跌停个数 = '涨跌停个数',
  百分比 = '百分比',
}

/**
 * 定时调度任务
 *
 * StressTestTaskScheduleDO
 */
export type StressTestTaskScheduleDO = {
  /**
   * 定时调度cron表达式
   */
  crontab: null | string;
  /**
   * 数据使用策略
   */
  dataUse: null | string;
  /**
   * 是否启用定时调度
   */
  scheduled: number;
  /**
   * 定时调度日期策略
   */
  scheduleDate: null | string;
  /**
   * 定时调度状态：0调度中，1未在调度中
   */
  scheduledStatus: number;
  /**
   * 定时调度id
   */
  scheduleId: number | null;
  /**
   * 压测任务id
   */
  taskId: number | null;
  [property: string]: any;
};

/**
 * StressTestTaskSettingDTO
 */
export type StressTestTaskSettingDTO = {
  /**
   * 场景一
   */
  conditionOne: number | null;
  /**
   * 场景三
   */
  conditionThree: number | null;
  /**
   * 场景二
   */
  conditionTwo: number | null;
  id: number | null;
  /**
   * 计算项目（如果是2.涨跌条件压测，则为拼接条件；3.策略压测，则为策略id;4.标签压测，则为标签名称）
   */
  item: null | string;
  /**
   * 计算项目2，用作前端显示和计算处理
   */
  item2: null | string;
  /**
   * 压力测试任务id
   */
  taskId: number | null;
  /**
   * 测试类型
   */
  testType: number;
  /**
   * 二级压测类型(如有)
   */
  testTypeTwo: null | string;
  [property: string]: any;
};

/**
 * StressTestReportDTO
 */
export type StressTestReportDTO = {
  /**
   * 关注线
   */
  attentionLine: number | null;
  /**
   * 压测日期
   */
  calculateDate: null | string;
  /**
   * 压测结束时间
   */
  calculateEndTime: null | string;
  /**
   * 压测开始时间
   */
  calculateStartTime: null | string;
  /**
   * 客户范围
   */
  clientRange: number;
  /**
   * 平仓线
   */
  closingLine: number | null;
  /**
   * 全部信用客户数量
   */
  creditClientCount: number | null;
  /**
   * 删除状态
   */
  deleteStatus: number;
  /**
   * 执行类型：0手动执行，1定时自动执行
   */
  executeType: number;
  /**
   * 涨跌幅设置方式
   */
  priceChangeType: null | string;
  /**
   * 压测报告ID
   */
  reportId: number | null;
  /**
   * 压测报告名称
   */
  reportName: null | string;
  /**
   * 压测任务用户
   */
  taskAuthor: null | string;
  /**
   * 压测任务摘要
   */
  taskDigest: null | string;
  /**
   * 压测任务ID
   */
  taskId: number | null;
  /**
   * 压测任务名称
   */
  taskName: null | string;
  /**
   * 压测报告计算设置
   */
  taskSettings: StressTestReportTaskSettingDTO[] | null;
  /**
   * 压测基准
   */
  testBenchmark: null | string;
  /**
   * 压测客户数量
   */
  testClientCount: number | null;
  /**
   * 压测客户占信用客户数量比例
   */
  testClientRatio: number | null;
  /**
   * 压测条件类型
   */
  testType: number;
  /**
   * 压测发起用户id
   */
  testUserId: null | string;
  /**
   * 压测发起用户名称
   */
  testUserName: null | string;
  /**
   * 压测任务用户id
   */
  userId: null | string;
  /**
   * 警戒线
   */
  warningLine: number | null;
  [property: string]: any;
};

/**
 * StressTestReportTaskSettingDTO
 */
export type StressTestReportTaskSettingDTO = {
  /**
   * 场景一
   */
  conditionOne: number | null;
  /**
   * 场景三
   */
  conditionThree: number | null;
  /**
   * 场景二
   */
  conditionTwo: number | null;
  id: number | null;
  /**
   * 计算项目（如果是2.涨跌条件压测，则为拼接条件；3.策略压测，则为策略id;4.标签压测，则为标签名称）
   */
  item: null | string;
  /**
   * 计算项目2，用作前端显示和计算处理
   */
  item2: null | string;
  /**
   * 压测报告id
   */
  reportId: number | null;
  /**
   * 压力测试任务id
   */
  taskId: number | null;
  /**
   * 测试类型
   */
  testType: number;
  /**
   * 二级压测类型(如有)
   */
  testTypeTwo: null | string;
  [property: string]: any;
};

/**
 * StressTestReportOverviewClientDetailVO
 */
export type StressTestReportOverviewClientDetailVO = {
  /**
   * 压测后坏账金额(万元)
   */
  badDebtAmountAfterTest: number | null;
  /**
   * 压测前坏账金额(万元)
   */
  badDebtAmountBeforeTest: number | null;
  /**
   * 压测后低于关注线金额(万元)
   */
  belowAttentionLineAmountAfterTest: number | null;
  /**
   * 压测前低于关注线金额(万元)
   */
  belowAttentionLineAmountBeforeTest: number | null;
  /**
   * 客户代码
   */
  clientId: null | string;
  /**
   * 客户名称
   */
  clientName: null | string;
  /**
   * 压测后融资余额(万元)
   */
  financingBalanceAfterTest: number | null;
  /**
   * 压测前融资余额(万元)
   */
  financingBalanceBeforeTest: number | null;
  /**
   * 资金账号
   */
  fundAccount: null | string;
  /**
   * 压测后需平仓金额(万元)
   */
  liquidationAmountAfterTest: number | null;
  /**
   * 压测前需平仓金额(万元)
   */
  liquidationAmountBeforeTest: number | null;
  /**
   * 压测后维保比例(%)
   */
  maintainMarginRatioAfterTest: number | null;
  /**
   * 压测前维保比例(%)
   */
  maintainMarginRatioBeforeTest: number | null;
  /**
   * 压测后需追保金额(万元)
   */
  marginCallAmountAfterTest: number | null;
  /**
   * 压测前需追保金额(万元)
   */
  marginCallAmountBeforeTest: number | null;
  /**
   * 压测后融券余额(万元)
   */
  shortSellBalanceAfterTest: number | null;
  /**
   * 压测前融券余额(万元)
   */
  shortSellBalanceBeforeTest: number | null;
  /**
   * 压测后总资产(万元)
   */
  totalAssetAfterTest: number | null;
  /**
   * 压测前总资产(万元)
   */
  totalAssetBeforeTest: number | null;
  /**
   * 压测后总负债(万元)
   */
  totalDebtAfterTest: number | null;
  /**
   * 压测前总负债(万元)
   */
  totalDebtBeforeTest: number | null;
  [property: string]: any;
};
