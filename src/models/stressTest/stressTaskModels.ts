/**
 * 压力测试相关模型
 */

/**
 * StressTestTaskDTO
 */
export type StressTestTaskDTO = {
  /**
   * 关注线
   */
  attentionLine: number | null;
  /**
   * 计算状态：0-计算中，1-未在计算中
   */
  calculateStatus: number;
  /**
   * 压测客户范围
   */
  clientRange: number;
  /**
   * 平仓线
   */
  closingLine: number | null;
  createTime: null | string;
  /**
   * 自定义压测的客户列表
   */
  customClientList: StressTestCustomClientDO[] | null;
  /**
   * 删除状态
   */
  deleteStatus: number;
  /**
   * 清偿平仓线
   */
  liquidationLine: number | null;
  /**
   * 涨跌幅设置方式
   */
  priceChangeType: PriceChangeType;
  /**
   * 压力测试任务用户
   */
  taskAuthor: null | string;
  /**
   * 压力测试摘要
   */
  taskDigest: null | string;
  /**
   * 任务id
   */
  taskId: number | null;
  /**
   * 压力测试任务名称
   */
  taskName: null | string;
  /**
   * 定时调度任务
   */
  taskSchedule: null | StressTestTaskScheduleDO;
  /**
   * 压力测试任务计算设置
   */
  taskSettings: StressTestTaskSettingDTO[] | null;
  /**
   * 压测任务类型
   */
  taskType: number;
  /**
   * 压力测试条件类型
   */
  testType: number;
  /**
   * 穿仓户违约率
   */
  underMarginDefaultRate: number | null;
  /**
   * 压力测试任务用户ID
   */
  userId: null | string;
  /**
   * 警戒线
   */
  warningLine: number | null;
  [property: string]: any;
};

/**
 * StressTestCustomClientDO
 */
export type StressTestCustomClientDO = {
  /**
   * 客户代码
   */
  clientId: null | string;
  /**
   * 资金账号
   */
  fundAccount: null | string;
  id: number | null;
  /**
   * 压测任务id
   */
  taskId: number | null;
  [property: string]: any;
};

/**
 * 涨跌幅设置方式
 */
export enum PriceChangeType {
  涨跌停个数 = '涨跌停个数',
  百分比 = '百分比',
}

/**
 * 定时调度任务
 *
 * StressTestTaskScheduleDO
 */
export type StressTestTaskScheduleDO = {
  /**
   * 定时调度cron表达式
   */
  crontab: null | string;
  /**
   * 数据使用策略
   */
  dataUse: null | string;
  /**
   * 是否启用定时调度
   */
  scheduled: number;
  /**
   * 定时调度日期策略
   */
  scheduleDate: null | string;
  /**
   * 定时调度状态：0调度中，1未在调度中
   */
  scheduledStatus: number;
  /**
   * 定时调度id
   */
  scheduleId: number | null;
  /**
   * 压测任务id
   */
  taskId: number | null;
  [property: string]: any;
};

/**
 * StressTestTaskSettingDTO
 */
export type StressTestTaskSettingDTO = {
  /**
   * 场景一
   */
  conditionOne: number | null;
  /**
   * 场景三
   */
  conditionThree: number | null;
  /**
   * 场景二
   */
  conditionTwo: number | null;
  /**
   * 删除状态
   */
  deleteStatus: number;
  id: number | null;
  /**
   * 计算项目（如果是2.涨跌条件压测，则为拼接条件；3.策略压测，则为策略id;4.标签压测，则为标签名称）
   */
  item: null | string;
  /**
   * 计算项目2，用作前端显示和计算处理
   */
  item2: null | string;
  /**
   * 最大跌幅
   */
  largestDrop: number | null;
  /**
   * 压力测试任务id
   */
  taskId: number | null;
  /**
   * 测试类型
   */
  testType: number;
  /**
   * 二级压测类型(如有)
   */
  testTypeTwo: null | string;
  [property: string]: any;
};
