/**
 * @file: chart.ts
 * @description: 图表相关的通用 TypeScript 类型定义
 * @date: 2025/07/04
 */
// 导入现有类型
import type { ChartAnalysisVO, TypeCountVO } from '@/models/common/utilModels';

// 重新导出现有类型，保持向后兼容
export type { TypeCountVO, ChartAnalysisVO } from '@/models/common/utilModels';
export type { BarChartType } from '@/enums/chartEnum';

/**
 * 通用图表数据接口 - 支持泛型的图表数据结构
 * @template TLabel - 标签数据类型（通常为 string 或 number）
 * @template TValue - 数值数据类型（通常为 number）
 */
export interface ChartDataSet<TLabel = string, TValue = number> {
  /** 横轴数据（标签） */
  labels: TLabel[];
  /** 纵轴数据（数值） */
  values: TValue[];
  /** 数据集名称 */
  name?: string;
  /** 数据集颜色 */
  color?: string;
}

/**
 * 多个图表数据分析汇总接口
 */
export interface ChartAnalysisData {
  /** 塔金评级数据 */
  levelData: ChartDataSet;
  /** 股价分布数据 */
  closePriceData: ChartDataSet;
  /** 板块分布数据 */
  stockTypeData: ChartDataSet;
  /** 市值分布数据 */
  totalAmountData: ChartDataSet;
  /** 行业分布数据 */
  industryDistributionData: ChartDataSet;
}

/**
 * 图表数据转换工具类型
 */
export interface ChartDataTransformer {
  /**
   * 将 TypeCountVO 数组转换为 ChartDataSet
   * @param data - TypeCountVO 数组
   * @param name - 数据集名称
   * @returns ChartDataSet
   */
  fromTypeCountVO: (data: TypeCountVO[], name?: string) => ChartDataSet<string, number>;

  /**
   * 将 ChartAnalysisVO 转换为 图表数据结构 的 函数接口
   * @param analysis - ChartAnalysisVO 数据
   * @returns ChartAnalysisData
   */
  fromChartAnalysisVO: (analysis: ChartAnalysisVO) => ChartAnalysisData;
}

/**
 * 饼图数据项接口
 */
export interface PieChartDataItem {
  /** 数据项名称 */
  name: string;
  /** 数据项值 */
  value: number;
  /** 数据项颜色 */
  color?: string;
  /** 是否选中 */
  selected?: boolean;
}

/**
 * 饼图数据接口
 */
export interface PieChartData {
  /** 饼图数据项 */
  data: PieChartDataItem[];
  /** 图表标题 */
  title?: string;
  /** 图例位置 */
  legendPosition?: 'top' | 'bottom' | 'left' | 'right';
}
