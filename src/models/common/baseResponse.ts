/**
 * @file: baseResponse
 * @description: 基础响应模型
 * @date: 2024/8/27
 * @author: <PERSON> Ye
 */

/**
 * 基础响应模型，用于所有非分页API接口的响应
 *
 * @template T - 泛型参数，用于定义响应数据的类型。
 */
export interface BasicResponseModel<T = any> {
  /**
   * 状态码
   * @type {number}
   * @description API响应的状态码，通常用于指示请求的成功或失败状态（例如200表示成功）。
   */
  code: number;

  /**
   * 状态信息
   * @type {string}
   * @description API响应的简短描述信息，例如错误信息或成功消息。与msg字段意义相同，通常不同时使用。
   */
  message: string;

  /**
   * 状态信息
   * @type {string}
   * @description 与message字段类似，用于返回API响应的描述信息。
   */
  msg: string;

  /**
   * 数据
   * @type {T}
   * @description API响应的实际数据部分。可以是任何类型，通常是一个数据对象。
   */
  data: T;
}

/**
 * 分页响应模型，用于包含分页信息的API接口的响应
 *
 * @template T - 泛型参数，用于定义响应数据的类型。
 */
export type PaginatedResponseModel<T = any> = BasicResponseModel<PageInfo<T>>;

/**
 * 数据响应模型，包含分页信息和数据
 *
 * @template T - 泛型参数，用于定义分页数据的类型。
 */
export interface PageInfo<T = any> {
  /**
   * 当前页码
   * @type {number}
   * @description 当前返回数据所在的页码。通常用于分页查询。
   */
  current: number;

  /**
   * 每页数据大小
   * @type {number}
   * @description 每页显示的数据数量。用于分页时确定每页的大小。
   */
  size: number;

  /**
   * 数据总量
   * @type {number}
   * @description 数据的总数量。通常用于显示分页的总数据量。
   */
  total: number;

  /**
   * 数据记录
   * @type {T[]}
   * @description 实际的数据记录，是一个数据对象的数组。
   */
  records: T[];
}

/**
 * ConcentraGroupModelRuleDTO
 */
export type ConcentraGroupModelRuleDTO = {
  /**
   * 计算类型：0正常，1特殊
   */
  calculateType: number | null;
  /**
   * 创建用户
   */
  createUser: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券分类,多个用/分隔
   */
  secCategory: null | string;
  /**
   * 证券品种
   */
  secType: number;
  /**
   * 特殊规则配置列表
   */
  specialRuleConfigList: ConcentraGroupModelRuleRemarkDO[] | null;
  /**
   * 目标集中度分类
   */
  targetCategory: null | string;
  [property: string]: any;
};

/**
 * ConcentraGroupModelRuleRemarkDO
 */
export type ConcentraGroupModelRuleRemarkDO = {
  /**
   * 条件范围
   */
  conditionRange: null | string;
  /**
   * 创建用户
   */
  createUser: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 关系符号
   */
  relation: RelationEnum;
  /**
   * 证券分类,多个用/分隔
   */
  secCategory: null | string;
  /**
   * 证券品种
   */
  secType: number;
  /**
   * 特殊分类
   */
  specialCategory: null | string;
  /**
   * 目标分类
   */
  targetCategory: null | string;
  [property: string]: any;
};

/**
 * 关系符号
 */
export enum RelationEnum {
  And = 'and',
  Or = 'or',
}

/**
 * CounterSecurityCategoryDictDO
 */
export type CounterSecurityCategoryDictDO = {
  id: number | null;
  /**
   * 证券分组名称
   */
  stockgroupName: null | string;
  /**
   * 证券分组编号
   */
  stockgroupNo: number | null;
  [property: string]: any;
};
