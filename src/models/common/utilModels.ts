/**
 * @file: utilModels
 * @description: 工具类相关数据模型
 * @date: 2024/8/28
 * @author: <PERSON>
 */

import { MatrixInfoVO } from '@/models/matrix/matrixModels';

/**
 * ChartAnalysisVO - 图表分析数据模型
 * 数组参数类型的值，后端不会返回null，而是空数组[]
 *
 * @property {TypeCountVO[]}closes 最新价分析
 * @property {TypeCountVO[]}industry 行业分析
 * @property {TypeCountVO[]}level 评级分析
 * @property {MatrixInfoVO[]}matrix 矩阵分析
 * @property {TypeCountVO[]}peerLevel 同业券商分析
 * @property {TypeCountVO[]}region 地区分析
 * @property {TypeCountVO[]}score 评分分析
 * @property {TypeCountVO[]}stockType 证券类型分析
 * @property {TypeCountVO[]}ttmAnalysis 市值分析
 * @property {any} [property] 其他属性
 */
export interface ChartAnalysisVO {
  /**
   * 最新价分析
   */
  closes: TypeCountVO[];
  /**
   * 行业分析
   */
  industry: TypeCountVO[];
  /**
   * 评级分析
   */
  level: TypeCountVO[];
  /**
   * 矩阵分析
   */
  matrix: MatrixInfoVO[];
  /**
   * 同业券商分析
   */
  peerLevel: TypeCountVO[];
  /**
   * 地区分析
   */
  region: TypeCountVO[];
  /**
   * 评分分析
   */
  score: TypeCountVO[];
  /**
   * 证券类型分析
   */
  stockType: TypeCountVO[];
  /**
   * 市值分析
   */
  ttmAnalysis: TypeCountVO[];
  [property: string]: any;
}

/**
 * TypeCountVO - 类型数量数据模型
 * @property {number}count 类型数量
 * @property {string}typeName 类型名称
 */
export interface TypeCountVO {
  /**
   * 类型数量
   */
  count: number;
  /**
   * 类型名称
   */
  typeName: string;
  [property: string]: any;
}

/**
 * TypeCountTwoVO - 类型数量数据模型
 * @property {number}count 类型数量
 * @property {string}typeName 类型名称
 */
export interface TypeCountTwoVO {
  /**
   * 类型数量2
   */
  countTwo: number | null;
  /**
   * 类型数量
   */
  count: number | null;
  /**
   * 类型名称
   */
  typeName: string;
  [property: string]: any;
}

/**
 * MatrixCellVo - 矩阵单元格数据模型
 * @property across 横坐标
 * @property count 股票数量
 * @property flag 是否包含当前股票
 * @property labels 标签组合数量
 * @property vertical 纵坐标
 */
export interface MatrixCellVo {
  /**
   * 横坐标
   */
  across?: null | string;
  /**
   * 股票数量
   */
  count?: number | null;
  /**
   * 是否包含当前股票
   */
  flag?: number | null;
  /**
   * 标签组合数量
   */
  labels?: number | null;
  /**
   * 纵坐标
   */
  vertical?: null | string;
  [property: string]: any;
}

/**
 * StockTableInfoVo - 股票表格数据模型
 * @interface StockTableInfoVO
 * @property {string | null} [afterLevel] - 策略评级，可能为空
 * @property {number | null} [closes] - 股价，可能为空
 * @property {number | null} [count] - 标签数量，可能为空
 * @property {number | null} [financialScore] - 财务评分，可能为空
 * @property {number | null} [grossMarginSub] - 毛利率3年增速与毛利率5年增速的差值，可能为空
 * @property {number | null} [grossMarginTrendFiveYear] - 毛利率5年增速，可能为空
 * @property {number | null} [grossMarginTrendThreeYear] - 毛利率3年增速，可能为空
 * @property {number | null} [highRiskLabelCount] - 高风险标签数量，可能为空
 * @property {number | null} [incomeDebtSubFiveYear] - 5年营收负债增速差，可能为空
 * @property {number | null} [incomeDebtSubThreeYear] - 3年营收负债增速差，可能为空
 * @property {number | null} [incomeSub] - 营业收入3年增速与营业收入5年增速的差值，可能为空
 * @property {number | null} [incomeTrendFiveYear] - 营业收入5年增速，可能为空
 * @property {number | null} [incomeTrendThreeYear] - 营业收入3年增速，可能为空
 * @property {string | null} [industryName] - 行业名称，可能为空
 * @property {string | null} [industryNameTwo] - 行业名称二级，可能为空
 * @property {number | null} [kfnetprofit] - 扣非净利润，可能为空
 * @property {number | null} [kfnetprofitsub] - 扣非净利润3年增速与5年增速的差值，可能为空
 * @property {number | null} [kfnetprofittrendfiveyear] - 扣非净利润5年增速，可能为空
 * @property {number | null} [kfnetprofittrendthreeyear] - 扣非净利润3年增速，可能为空
 * @property {string | null} [labels] - 标签内容，可能为空
 * @property {string | null} [level] - 塔金评级，可能为空
 * @property {string | null} [levelRange] - 同业分类区间，可能为空
 * @property {number | null} [losses] - 5年亏损次数，可能为空
 * @property {number | null} [midPb] - 市净率行业中位数，可能为空
 * @property {number | null} [midPe] - 市盈率行业中位数，可能为空
 * @property {string | null} [name] - 系统用户对应策略名称，可能为空
 * @property {number | null} [netAssetValuePerShare] - 每股净资产，可能为空
 * @property {string | null} [newReason] - 新评级理由，可能为空
 * @property {string | null} [oldReason] - 旧评级理由，可能为空
 * @property {number | null} [pb] - 市净率，可能为空
 * @property {number | null} [pe] - 市盈率，可能为空
 * @property {string | null} [stockId] - 股票代码，可能为空
 * @property {string | null} [stockName] - 股票名称，可能为空
 * @property {number | null} [totalAmount] - 市值，可能为空
 * @property {number | null} [totalScore] - 综合评分，可能为空
 * @property {string | null} [userLevel] - 用户评级，可能为空
 * @property {string | null} [userPolicyName] - 用户对应策略名称，可能为空
 * @property {any} [property] - 其他动态属性
 */
export interface StockTableInfoVO {
  /**
   * 策略评级
   */
  afterLevel: null | string;
  /**
   * 股价
   */
  closes: number | null;
  /**
   * 标签数量
   */
  count: number | null;
  /**
   * 财务评分
   */
  financialScore: number | null;
  /**
   * 毛利率3年增速-毛利率5年增速
   */
  grossMarginSub: number | null;
  /**
   * 毛利率5年增速
   */
  grossMarginTrendFiveYear: number | null;
  /**
   * 毛利率3年增速
   */
  grossMarginTrendThreeYear: number | null;
  /**
   * 高风险标签数量
   */
  highRiskLabelCount: number | null;
  /**
   * 5年营收负债增速差
   */
  incomeDebtSubFiveYear: number | null;
  /**
   * 3年营收负债增速差
   */
  incomeDebtSubThreeYear: number | null;
  /**
   * 营业收入3年增速-营业收入5年增速
   */
  incomeSub: number | null;
  /**
   * 营业收入5年增速
   */
  incomeTrendFiveYear: number | null;
  /**
   * 营业收入3年增速
   */
  incomeTrendThreeYear: number | null;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 行业名称二级
   */
  industryNameTwo: null | string;
  /**
   * 扣非净利润
   */
  kfnetprofit: number | null;
  /**
   * 扣非净利润3年增速-扣非净利润5年增速
   */
  kfnetprofitsub: number | null;
  /**
   * 扣非净利润5年增速
   */
  kfnetprofittrendfiveyear: number | null;
  /**
   * 扣非净利润3年增速
   */
  kfnetprofittrendthreeyear: number | null;
  /**
   * 标签内容
   */
  labels?: null | string;
  /**
   * 塔金评级
   */
  level?: null | string;
  /**
   * 同业分类区间
   */
  levelRange: null | string;
  /**
   * 5年亏损次数
   */
  losses: number | null;
  /**
   * 市净率行业中位数
   */
  midPb: number | null;
  /**
   * 市盈率行业中位数
   */
  midPe: number | null;
  /**
   * 系统用户对应策略名称
   */
  name?: null | string;
  /**
   * 每股净资产(元/股)
   */
  netAssetValuePerShare: number | null;
  /**
   * 新评级理由
   */
  newReason?: null | string;
  /**
   * 旧评级理由
   */
  oldReason?: null | string;
  /**
   * 市净率
   */
  pb?: number | null;
  /**
   * 市盈率
   */
  pe?: number | null;
  /**
   * 股票代码
   */
  stockId: null | string;
  /**
   * 股票名称
   */
  stockName: null | string;
  /**
   * 市值
   */
  totalAmount?: number | null;
  /**
   * 综合评分
   */
  totalScore?: number | null;
  /**
   * 用户评级
   */
  userLevel?: null | string;
  /**
   * 用户对应策略名称
   */
  userPolicyName?: null | string;
  [property: string]: any;
}

/**
 *
 * ValueRangeVO - 区间值
 * @param {number} maxValue 最大值
 * @param {number} minValue 最小值
 */
export interface ValueRangeVO {
  /**
   * 最大值
   */
  maxValue: number | null;
  /**
   * 最小值
   */
  minValue: number | null;
}

/**
 * Column - 表格列的数据模型
 * @param {string} title 列标题
 * @param {'left' | 'center' | 'right'} align 列对齐方式
 * @param {string} width 列宽度
 * @param {keyof T} key 列的键名，对应数据对象中的字段
 * @param {boolean} [sorter] 是否启用排序
 * @param {(row: T) => any} [render] 自定义渲染函数
 */
export interface Column<T> {
  /**
   * 列的固定位置
   */
  fixed?: 'left' | 'right';
  /**
   * 列标题
   */
  title: string;

  /**
   * 列对齐方式
   */
  align?: 'left' | 'center' | 'right';

  /**
   * 列宽度
   */
  width?: string;

  /**
   * 列的键名，对应数据对象中的字段
   */
  key: keyof T;

  /**
   * 是否启用排序
   */
  sorter?: boolean;

  /**
   * 自定义渲染函数
   */
  render?: (row: T) => any;

  /**
   * 类型
   */
  type?: string;

  /**
   * 是否启用省略号及相关配置
   * 可以包含任意键值对
   */
  ellipsis?: { [key: string]: any };
}

/**
 * StockInfoVO - 股票信息数据模型
 * @property {number | null} [closes] - 最新收盘价，可能为空
 * @property {null | string} [date] - 日期，可能为空
 * @property {number | null} [debtRatio] - 负债率，可能为空
 * @property {number | null} [indMidDebtRatio] - 行业负债率中位数，可能为空
 * @property {number | null} [indMidNetProfit] - 行业净利润中位数，可能为空
 * @property {null | string} [industryId] - 行业id，可能为空
 * @property {null | string} [industryIdTwo] - 二级行业id，可能为空
 * @property {null | string} [industryName] - 行业名称，可能为空
 * @property {null | string} [level] - 系统评级，可能为空
 * @property {number | null} [netProfit] - 净利润，可能为空
 * @property {number | null} [pb] - 市净率，可能为空
 * @property {number | null} [pe] - 市盈率，可能为空
 * @property {null | string} [peerConcentrationRange] - 同业集中度区间，可能为空
 * @property {null | string} [peerHaircutRange] - 同业折算率区间，可能为空
 * @property {null | string} [peerLevelRange] - 同业评级区间，可能为空
 * @property {null | string} [stockId] - 股票代码，可能为空
 * @property {null | string} [stockName] - 股票名称，可能为空
 * @property {number | null} [totalAmount] - 市值，可能为空
 * @property {null | string} [userLevel] - 用户评级，可能为空
 * @property {any} [property] - 其他动态属性
 */
export interface StockInfoVO {
  /**
   * 最新收盘价
   */
  closes?: number | null;
  /**
   * 日期
   */
  date?: null | string;
  /**
   * 负债率
   */
  debtRatio?: number | null;
  /**
   * 毛利率
   */
  grossMargin?: number | null;
  /**
   * 行业负债率中位数
   */
  indMidDebtRatio?: number | null;
  /**
   * 行业毛利率
   */
  indMidGrossMargin?: number | null;
  /**
   * 行业净利润
   */
  indMidNetProfit?: number | null;
  /**
   * 行业id
   */
  industryId?: null | string;
  /**
   * 二级行业id
   */
  industryIdTwo?: null | string;
  /**
   * 行业名称
   */
  industryName?: null | string;
  /**
   * 系统评级
   */
  level?: null | string;
  /**
   * 净利润
   */
  netProfit?: number | null;
  /**
   * 市净率
   */
  pb?: number | null;
  /**
   * 市盈率
   */
  pe?: number | null;
  /**
   * 同业集中度区间
   */
  peerConcentrationRange?: null | string;
  /**
   * 同业折算率区间
   */
  peerHaircutRange?: null | string;
  /**
   * 同业评级区间
   */
  peerLevelRange?: null | string;
  /**
   * 股票代码
   */
  stockId?: null | string;
  /**
   * 股票名称
   */
  stockName?: null | string;
  /**
   * 市值
   */
  totalAmount?: number | null;
  /**
   * 用户评级
   */
  userLevel?: null | string;
  [property: string]: any;
}
