/**
 * @file: roleModels
 * @description: 角色相关数据模型
 * @date: 2024/8/29
 * @author: <PERSON> Ye
 */

import { CommonStatus } from '@/enums/baseEnum';

/**
 * RoleDO 角色数据模型
 * @interface RoleDO
 * @property {number} authorityScope 权限范围0：系统管理员  100:默认，普通角色
 * @property {null|string} createUser 创建人uid
 * @property {null|string} isValid 该条数据是否可用，默认为Y:可用 N:不可用
 * @property {null|string} remark 角色备注
 * @property {null|string} roleId 角色id
 * @property {null|string} roleName 角色名称，有唯一约束
 * @property {null|string} updateUser 更新人的uid
 * @property {any} [property: string] 其他属性
 */
export interface RoleDO {
  /**
   * 权限范围0：系统管理员  100:默认，普通角色
   */
  authorityScope?: number | null;
  /**
   * 创建人uid
   */
  createUser?: null | string;
  /**
   * 启用状态 0:启用 1:禁用
   */
  enableStatus?: number | null;
  /**
   * 该条数据是否可用，默认为Y:可用 N:不可用
   */
  isValid?: null | string;
  /**
   * 关联用户名称列表
   */
  relatedUserNameList?: string[] | null;
  /**
   * 角色备注
   */
  remark?: null | string;
  roleId?: null | string;
  /**
   * 角色名称，有唯一约束
   */
  roleName?: null | string;
  /**
   * 更新人的uid
   */
  updateUser?: null | string;
  /**
   * 创建时间
   */
  createTime?: null | string;
  /**
   * 更新时间
   */
  updateTime?: null | string;
  [property: string]: any;
}

/**
 * FunctionDictDO 功能字典数据模型
 * @interface FunctionDictDO
 * @property {CommonStatus} enableStatus 启用状态
 * @property {null|string} functionName 功能名称
 */
export interface FunctionDictDO {
  /**
   * 启用状态
   */
  enableStatus: CommonStatus;
  /**
   * 功能名称
   */
  functionName: null | string;
  id: number | null;
  [property: string]: any;
}
