/**
 * @file: userModels
 * @description: 用户相关数据模型
 * @date: 2024/8/27
 * @author: <PERSON> Ye
 */

import { CommonEnum } from '@/enums/baseEnum';
import { WarningType } from '@/enums/labelEnum';
import { FirstLoginStatus, UserValidStatus, UserSourceType } from '@/enums/base/userEnum';

/**
 * LevelCriteriaDetailDO - 用户评级标准明细数据模型
 * @interface LevelCriteriaDetailDO
 *
 * @property {number} [criteriaId] - 评级标准ID
 * @property {CommonEnum} [enableStatus] - 是否启用
 * @property {number} [id] - 主键
 * @property {CommonEnum} [ifChs] - 是否中文字符
 * @property {null | string} [level] - 评级
 * @property {number} [levelOrder] - 评级高低顺序
 * @property {number} [strategyCalculateOrder] - 策略计算顺序
 * @property {any} [property] - 其他动态属性
 */
export interface LevelCriteriaDetailDO {
  /**
   * 评级标准ID
   */
  criteriaId?: number | null;
  /**
   * 是否启用
   */
  enableStatus?: CommonEnum;
  /**
   * 主键
   */
  id?: number | null;
  /**
   * 是否中文字符
   */
  ifChs?: CommonEnum;
  /**
   * 评级
   */
  level?: null | string;
  /**
   * 评级高低顺序
   */
  levelOrder?: number | null;
  /**
   * 策略计算顺序
   */
  strategyCalculateOrder?: number | null;
  [property: string]: any;
}

/**
 * 用户评级标准数据模型
 * @interface LevelCriteriaDO
 *
 * @property {string} [createUser] - 创建用户
 * @property {string} [criteriaName] - 评级标准名称
 * @property {number} [enableStatus] - 启用状态
 * @property {number} [id] - 唯一标识符
 * @property {any} [property] - 其他动态属性
 */
export interface LevelCriteriaDO {
  /**
   * 创建用户
   */
  createUser?: string | null;

  /**
   * 评级标准名称
   */
  criteriaName?: string | null;

  /**
   * 启用状态
   */
  enableStatus?: CommonEnum;

  /**
   * 主键ID
   */
  id?: number | null;

  /**
   * 其他动态属性
   */
  [property: string]: any;
}

/**
 * UserDO - 用户数据模型
 * @interface UserDO
 *
 * @property {null | string} [companyName] - 公司名称
 * @property {null | string} [createUser] - 创建人uid
 * @property {null | string} [deptName] - 部门名称
 * @property {UserValidStatus | null} [isValid] - 该条数据是否可用，默认为Y:可用 N:不可用
 * @property {null | string} [jobName] - 职位名称
 * @property {null | string} [password] - 用户密码
 * @property {FirstLoginStatus | null} [firstLogin] - 是否首次登录，Y:首次登录 N:非首次登录
 * @property {null | string} [token] - 接口校验令牌，除了登录接口，其他接口均需要由client端携带给server端
 * @property {null | string} [updateUser] - 更新人uid
 * @property {null | string} [userAccount] - 用户账户，登录使用，有唯一约束
 * @property {number} [userCriteriaId] - 用户评级标准表关联ID
 * @property {null | string} [userEmail] - 用户邮箱，选填
 * @property {null | string} [userId] - 主键ID，自动生成
 * @property {null | string} [userMobile] - 用户11位手机号，选填
 * @property {null | string} [userName] - 用户昵称，可重复
 * @property {UserSourceType | null} [userSource] - 用户来源 0:默认，真实用户 1：虚拟用户，例如管理员admin之类的账户
 * @property {WarningType} [warningType] - 预警类型
 */
export interface UserDO {
  /**
   * 公司名称
   */
  companyName?: null | string;
  /**
   * 创建人uid
   */
  createUser?: null | string;
  /**
   * 部门名称
   */
  deptName?: null | string;
  /**
   * 该条数据是否可用，默认为Y:可用 N:不可用
   */
  isValid?: UserValidStatus | null;
  /**
   * 职位名称
   */
  jobName?: null | string;
  /**
   * 用户密码
   */
  password?: null | string;
  /**
   * 是否首次登录，Y:首次登录 N:非首次登录
   */
  firstLogin?: FirstLoginStatus | null;
  /**
   * 接口校验令牌，除了登录接口，其他接口均需要由client端携带给server端
   */
  token: null | string;
  /**
   * 更新人uid
   */
  updateUser?: null | string;
  /**
   * 用户账户，登录使用，有唯一约束
   */
  userAccount?: null | string;
  /**
   * 用户评级标准表关联ID
   */
  userCriteriaId?: number | null;
  /**
   * 用户邮箱，选填
   */
  userEmail?: null | string;
  /**
   * 主键ID，自动生成
   */
  userId: null | string;
  /**
   * 用户11位手机号，选填
   */
  userMobile?: null | string;
  /**
   * 用户昵称，可重复
   */
  userName?: null | string;
  /**
   * 用户来源 0:默认，真实用户 1：虚拟用户，例如管理员admin之类的账户
   */
  userSource?: UserSourceType | null;
  /**
   * 预警类型
   */
  warningType?: WarningType;
  [property: string]: any;
}
