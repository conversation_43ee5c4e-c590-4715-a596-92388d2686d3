/**
 * @file store.ts
 * @description Store 状态管理相关的类型定义文件
 * @date 2024/12/25
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import type { RouteRecordRaw } from 'vue-router';

/**
 * 异步路由状态管理接口
 * @description 定义异步路由模块的状态结构，用于管理动态路由、菜单、组件缓存等核心功能
 *
 * **主要功能：**
 * - 动态路由管理：根据用户权限动态生成和管理路由
 * - 菜单状态维护：存储用户可访问的菜单列表
 * - 组件缓存控制：管理需要保持活跃状态的组件
 * - 路由状态追踪：跟踪动态路由的添加状态，避免重复操作
 *
 * **使用场景：**
 * - 用户登录后的权限路由初始化
 * - 菜单渲染和导航控制
 * - 页面组件的缓存策略管理
 * - 路由守卫中的状态检查
 *
 * **设计原则：**
 * - 状态集中管理，避免数据分散
 * - 类型安全，确保数据结构的一致性
 * - 性能优化，支持组件缓存和路由复用
 *
 * @interface IAsyncRouteState
 * @since 1.0.0
 * <AUTHOR>
 * @version 1.2.0
 * @see {@link useAsyncRouteStore} 对应的状态管理器
 * @see {@link RouteRecordRaw} Vue Router 路由记录类型
 *
 * @example
 * ```typescript
 * // 使用示例
 * const asyncRouteStore = useAsyncRouteStore();
 * const state: IAsyncRouteState = {
 *   menus: [],
 *   keepAliveComponents: [],
 *   isDynamicRouteAdded: false
 * };
 * ```
 */
export interface IAsyncRouteState {
  /**
   * 用户可访问的菜单列表
   * @description 存储根据用户权限过滤后的菜单路由配置，用于渲染侧边栏导航菜单
   *
   * **数据来源：**
   * - 后端权限接口返回的菜单数据
   * - 前端路由配置经过权限过滤的结果
   *
   * **数据特点：**
   * - 包含完整的路由元信息（meta）
   * - 支持嵌套菜单结构（children）
   * - 已经过权限验证和可见性过滤
   *
   * **使用场景：**
   * - 侧边栏菜单组件渲染
   * - 面包屑导航生成
   * - 权限验证和路由跳转
   *
   * @type {RouteRecordRaw[]}
   * @default []
   * @since 1.0.0
   *
   * @example
   * ```typescript
   * // 菜单数据结构示例
   * const menus = [
   *   {
   *     path: '/dashboard',
   *     name: 'Dashboard',
   *     component: () => import('@/views/dashboard/index.vue'),
   *     meta: {
   *       title: '仪表板',
   *       icon: 'dashboard',
   *       permissions: ['dashboard:view']
   *     }
   *   }
   * ];
   * ```
   */
  menus: RouteRecordRaw[];

  /**
   * 需要保持活跃状态的组件名称列表
   * @description 管理 Vue 组件的缓存策略，控制哪些组件在路由切换时保持状态不被销毁
   *
   * **缓存机制：**
   * - 基于 Vue 的 `<keep-alive>` 组件实现
   * - 通过组件的 `name` 属性进行匹配
   * - 支持动态添加和移除缓存组件
   *
   * **缓存策略：**
   * - 添加条件：路由 meta.keepAlive 为 true
   * - 移除条件：路由 meta.keepAlive 为 false 或组件卸载
   * - 特殊处理：重定向页面（Redirect）不进行缓存
   *
   * **性能影响：**
   * - ✅ 优势：提高页面切换速度，保持用户操作状态
   * - ⚠️ 注意：过多缓存会占用内存，需要合理控制
   *
   * **使用场景：**
   * - 表单填写页面：避免用户数据丢失
   * - 列表页面：保持筛选条件和滚动位置
   * - 复杂计算页面：避免重复计算
   *
   * @type {string[]}
   * @default []
   * @since 1.0.0
   * @see {@link setKeepAliveComponents} 设置缓存组件的方法
   * @see {@link getKeepAliveComponents} 获取缓存组件列表的方法
   *
   * @example
   * ```typescript
   * // 组件缓存配置示例
   * const keepAliveComponents = [
   *   'UserList',      // 用户列表页
   *   'OrderForm',     // 订单表单页
   *   'Dashboard'      // 仪表板页
   * ];
   *
   * // 在模板中使用
   * <keep-alive :include="keepAliveComponents">
   *   <router-view />
   * </keep-alive>
   * ```
   */
  keepAliveComponents: string[];

  /**
   * 动态路由添加状态标识
   * @description 标识动态路由是否已经完成添加，用于避免重复添加路由和优化性能
   *
   * **状态流转：**
   * ```
   * false (初始) → 生成路由 → 添加路由 → true (完成)
   *   ↓                                      ↑
   * 用户登录                              路由守卫检查
   * ```
   *
   * **核心作用：**
   * - 🚫 防止重复添加：避免路由重复注册导致的错误
   * - ⚡ 性能优化：跳过已完成的路由生成过程
   * - 🔄 状态同步：确保路由状态与应用状态一致
   *
   * **使用时机：**
   * - 路由守卫检查：`if (isDynamicRouteAdded) return next()`
   * - 路由重置：用户登出时重置为 false
   * - 权限变更：权限更新时重置为 false，触发重新生成
   *
   * **注意事项：**
   * - 必须在所有动态路由添加完成后才能设置为 true
   * - 用户登出或权限变更时需要重置为 false
   * - 与路由守卫的执行时机密切相关
   *
   * @type {boolean}
   * @default false
   * @since 1.0.0
   * @see {@link setDynamicRouteAdded} 设置动态路由状态的方法
   * @see {@link getIsDynamicRouteAdded} 获取动态路由状态的方法
   *
   * @example
   * ```typescript
   * // 路由守卫中的使用示例
   * router.beforeEach(async (to, from, next) => {
   *   // 检查动态路由是否已添加
   *   if (asyncRouteStore.getIsDynamicRouteAdded) {
   *     next(); // 直接通过，不重复添加
   *     return;
   *   }
   *
   *   // 生成并添加动态路由
   *   const routes = await asyncRouteStore.generateRoutes(userInfo);
   *   routes.forEach(route => router.addRoute(route));
   *
   *   // 标记为已添加
   *   asyncRouteStore.setDynamicRouteAdded(true);
   *   next();
   * });
   * ```
   */
  isDynamicRouteAdded: boolean;
}
