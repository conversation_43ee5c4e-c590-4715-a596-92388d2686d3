/**
 * @file: baseRequest
 * @description: 基础请求相关数据模型
 * @date: 2024/8/27
 * @author: <PERSON> Ye
 */

/**
 * 分页请求参数
 * @interface PageRequest
 * @property {string} [ascOrDesc] 排序方式
 * @property {number} current 当前页码
 * @property {string} [orderBy] 排序字段
 * @property {number} size 每页记录数
 */
export interface PageRequest {
  /**
   * 排序方式
   */
  ascOrDesc?: string | null;
  /**
   * 当前页码
   */
  current: number;
  /**
   * 排序字段
   */
  orderBy?: string | null;
  /**
   * 每页记录数
   */
  size: number;

  /**
   * 证券代码
   */
  secCode?: string | null;
  [property: string]: any;
}
