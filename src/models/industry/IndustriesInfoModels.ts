/**
 * 行业相关模型
 */
/**
 * IndustriesInfoVO
 */
export interface IndustriesInfoVO {
  /**
   * 自定义值
   */
  customValue: number | null;
  /**
   * 行业ID
   */
  industryId: string;
  /**
   * 行业名称
   */
  industryName: string;
  /**
   * 股票数量
   */
  stockNumber: number | null;
  /**
   * 次级行业列表
   */
  tierTwoIndustry: TierTwoIndustryElement[] | null;
  [property: string]: any;
}

/**
 * IndustriesInfoVO
 */
export type TierTwoIndustryElement = {
  /**
   * 自定义值
   */
  customValue: number | null;
  /**
   * 行业ID
   */
  industryId: null | string;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 股票数量
   */
  stockNumber: number | null;
  /**
   * 次级行业列表
   */
  tierTwoIndustry: TierTwoIndustryElement[] | null;
  [property: string]: any;
};

/**
 * IndustryTrackTreeVO
 */
export type IndustryTrackTreeVO = {
  /**
   * 值变化
   */
  changes: number | null;
  /**
   * 子行业
   */
  children: ChildElement[] | null;
  /**
   * 行业证券数量
   */
  count: number | null;
  /**
   * 行业ID
   */
  industryId: null | string;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 数值
   */
  value: number | null;
  [property: string]: any;
};

/**
 * IndustryTrackTreeVO
 */
export type ChildElement = {
  /**
   * 值变化
   */
  changes: number | null;
  /**
   * 子行业
   */
  children: ChildElement[] | null;
  /**
   * 行业证券数量
   */
  count: number | null;
  /**
   * 行业ID
   */
  industryId: null | string;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 数值
   */
  value: number | null;
  [property: string]: any;
};

/**
 * IndustryTrackInfoVO
 */
export type IndustryTrackInfoVO = {
  /**
   * 市值涨跌幅(%)
   */
  changes: number | null;
  /**
   * 最新收盘价(元)
   */
  closes: number | null;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 当前市值(亿元)
   */
  newTotalAmount: number | null;
  /**
   * 原有市值(亿元)
   */
  oldTotalAmount: number | null;
  /**
   * 同业评级区间
   */
  peerLevelRange: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 用户评级
   */
  userLevel: null | string;
  [property: string]: any;
};
/**
 * IndustryProfileDO
 */
export type IndustryProfileDO = {
  /**
   * 月内涨幅异常个股数量
   */
  abIncreaseNum: number | null;
  /**
   * 月内平均涨幅
   */
  averageIncrease: number | null;
  /**
   * 日期
   */
  date: null | string;
  /**
   * 行业ID
   */
  industryId: null | string;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 月内评级变化数量
   */
  levelChangeNum: number | null;
  /**
   * PB异常证券数量
   */
  pbAbnormalNum: number | null;
  /**
   * PE异常证券数量
   */
  peAbnormalNum: number | null;
  /**
   * 证券数量
   */
  secNum: number | null;
  /**
   * 月内预警数量
   */
  warningNum: number | null;
  [property: string]: any;
};

/**
 * IndFileChartVO
 */
export type IndFileChartVO = {
  /**
   * 财务评分分布图
   */
  financialScore: IndFileChartValueVO[] | null;
  /**
   * 评级分布图
   */
  level: IndFileChartValueVO[] | null;
  /**
   * 融资余额变化趋势图
   */
  rzyeChange: IndFileChartValueVO[] | null;
  /**
   * 月内预警趋势图
   */
  warning: IndFileChartValueVO[] | null;
  [property: string]: any;
};

/**
 * IndFileChartValueVO
 */
export type IndFileChartValueVO = {
  /**
   * 行业ID
   */
  industryId: null | string;
  /**
   * 行业数值
   */
  value: TypeNumVO[] | null;
  [property: string]: any;
};

/**
 * TypeNumVO
 */
export type TypeNumVO = {
  /**
   * 类型数量
   */
  count: number | null;
  /**
   * 类型名称
   */
  typeName: null | string;
  [property: string]: any;
};
