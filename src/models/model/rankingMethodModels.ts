/**
 * RankModelConfigDO
 */
export type RankModelConfigDO = {
  createTime: null | string;
  /**
   * 创建用户
   */
  createUserId: null | string;
  /**
   * 创建用户名称
   */
  createUserName: null | string;
  /**
   * 删除状态：0未删除，1已删除
   */
  deleteStatus: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型名称
   */
  modelName: null | string;
  updateTime: null | string;
  /**
   * 更新用户
   */
  updateUserId: null | string;
  /**
   * 是否有编辑权限：0有权限，1无权限
   */
  ifHasEditPermission?: number | null;
  [property: string]: any;
};

/**
 * RankModelLevelGroupConfigDO
 */
export type RankModelLevelGroupConfigDO = {
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型评级分组
   */
  modelLevel: null | string;
  /**
   * 得分排位比例左区间
   */
  scoreRankRatioLeftRange: number | null;
  /**
   * 得分排位比例右区间
   */
  scoreRankRatioRightRange: number | null;
  [property: string]: any;
};

/**
 * RankModelCalculateTypeDictVO
 */
export type RankModelCalculateTypeDictVO = {
  /**
   * 计算指标列表
   */
  calculateMetricDictList: RankModelCalculateMetricDictDO[] | null;
  /**
   * 计算类型名称
   */
  calculateTypeName: null | string;
  /**
   * 计算类型顺序
   */
  calculateTypeOrder: number | null;
  /**
   * 计算类型字典id
   */
  dictId: number | null;
  [property: string]: any;
};

/**
 * RankModelCalculateMetricDictDO
 */
export type RankModelCalculateMetricDictDO = {
  /**
   * 计算维度类型id
   */
  dictId: number | null;
  /**
   * 是否给出指定可选项：0是，1否（如会计审计意见计算指标，限定审计意见类型选项）
   */
  ifGivenOption: number | null;
  /**
   * 计算指标id
   */
  metricId: number | null;
  /**
   * 计算指标名称
   */
  metricName: null | string;
  [property: string]: any;
};

/**
 * RankModelCalculateMetricConfigVO
 */
export type RankModelCalculateMetricConfigVO = {
  /**
   * 计算日期（若为空，则默认为最新财报日期或最新交易日期）
   */
  calculateDate: null | string;
  /**
   * 计算维度类型id
   */
  dictId: number | null;
  /**
   * 分组得分配置列表
   */
  groupScoreConfigList: RankModelMetricGroupScoreConfigDO[] | null;
  /**
   * 是否数值与得分是正向关系（0：数值越大，得分越高；1：数值越大，得分越低）
   */
  ifValueScorePositive: number | null;
  /**
   * 模型计算指标配置id
   */
  metricConfigId: number | null;
  /**
   * 计算指标id
   */
  metricId: number | null;
  /**
   * 计算指标名称
   */
  metricName: null | string;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 时间范围
   */
  timeDuration: number | null;
  /**
   * 具体值对应得分配置
   */
  valueScoreConfigList: RankModelMetricValueScoreConfigDO[] | null;
  [property: string]: any;
};

/**
 * RankModelMetricGroupScoreConfigDO
 */
export type RankModelMetricGroupScoreConfigDO = {
  /**
   * 组别顺序（第x组）
   */
  groupOrder: number | null;
  /**
   * 组别得分
   */
  groupScore: number | null;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 模型计算指标配置id
   */
  metricConfigId: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  [property: string]: any;
};

/**
 * RankModelMetricValueScoreConfigDO
 */
export type RankModelMetricValueScoreConfigDO = {
  /**
   * 计算方向
   */
  calculateDirection: CalculateDirection;
  id: number | null;
  /**
   * 计算指标的值（非数值类型）
   */
  itemValue: null | string;
  /**
   * 左区间值
   */
  leftValue: number | null;
  /**
   * 计算指标配置id
   */
  metricConfigId: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 右区间值
   */
  rightValue: number | null;
  /**
   * 对应当前证券的最终得分
   */
  targetFinalScore: number | null;
  /**
   * 对应当前计算指标得分
   */
  targetMetricScore: number | null;
  [property: string]: any;
};

/**
 * 计算方向
 */
export enum CalculateDirection {
  /** > */
  GT = '>',
  /** >= */
  GE = '>=',
  /** = */
  EQ = '=',
  /** < */
  LT = '<',
  /** <= */
  LE = '<=',
  /** 区间 */
  RANGE = '区间',
  /** - */
  SUBTRACT = '-',
  /** + */
  ADD = '+',
}
