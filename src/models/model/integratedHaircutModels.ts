/**
 * IntegratedHaircutModelConfigDTO
 */
export type IntegratedHaircutModelConfigDTO = {
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 删除状态：0未删除，1已删除
   */
  deleteStatus: number;
  /**
   * 模型状态：0启用，1停用
   */
  mainStatus: number;
  /**
   * 折算率模型id
   */
  modelId: number | null;
  /**
   * 折算率模型名称
   */
  modelName: null | string;
  /**
   * 折算率模型备注
   */
  modelRemark: null | string;
  /**
   * 折算率模型类型
   */
  modelType: ModelType | null;
  /**
   * 关联模型id
   */
  relatedModelId: number | null;
  /**
   * 更新时间
   */
  updateTime: null | string;
  /**
   * 模型创建用户id
   */
  userId: null | string;
  /**
   * 模型创建用户名称
   */
  userName: null | string;
  /**
   * 是否有修改权限：0有权限，1无权限
   */
  ifHasEditPermission?: number;
  [property: string]: any;
};

/**
 * 折算率模型类型
 */
export enum ModelType {
  EventDriven = 'eventDriven',
  MatrixMethod = 'matrixMethod',
  RankingMethod = 'rankingMethod',
}
