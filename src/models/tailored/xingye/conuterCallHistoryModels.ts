/**
 * CounterApiCallHistoryDO
 */
export type CounterApiCallHistoryDO = {
  /**
   * 调用日期
   */
  date: null | string;
  /**
   * 调用时间
   */
  dateTime: null | string;
  /**
   * 错误信息
   */
  errorInfo: null | string;
  /**
   * 错误编号
   */
  errorNo: null | string;
  /**
   * 交易类别
   */
  exchangeType: null | string;
  /**
   * 接口功能号
   */
  functionNumber: null | string;
  /**
   * 接口功能类型
   */
  functionType: null | string;
  id: number | null;
  /**
   * 调用状态
   */
  status: number;
  /**
   * 证券代码
   */
  stockCode: null | string;
  [property: string]: any;
};

/**
 * KeyValueVO
 */
export type KeyValueVO = {
  /**
   * key
   */
  key: null | string;
  /**
   * value
   */
  value: null | string;
  [property: string]: any;
};

/**
 * CounterInteractHistoryDO
 */
export type CounterInteractHistoryDO = {
  /**
   * 业务类型
   */
  businessType: null | string;
  createTime: null | string;
  /**
   * 调用日期
   */
  date: null | string;
  /**
   * 调用时间
   */
  dateTime: null | string;
  /**
   * 调用失败记录数
   */
  errorNumber: number | null;
  /**
   * 接口功能号
   */
  functionNumber: null | string;
  /**
   * 接口功能类型
   */
  functionType: null | string;
  id: number | null;
  /**
   * 调用状态：0调用中，1调用结束
   */
  status: number;
  /**
   * 调用成功记录数
   */
  successNumber: number | null;
  updateTime: null | string;
  [property: string]: any;
};
