/**
 * 柜台数据相关模型
 */

/**
 * CollateralSecurityBaseInfoVO
 */
export type CollateralSecurityBaseInfoVO = {
  /**
   * 担保折算率
   */
  assureRatio: number | null;
  /**
   * 主模型担保率
   */
  mainModelAssureRatio: number | null;
  /**
   * 交易所阈值
   */
  exchangeAssureRatioThreshold: number | null;
  /**
   * 担保品状态,0-可担保,1-暂停,2-作废
   */
  assureStatus: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 系统折算率
   */
  systemAssureRatio: number | null;
  [property: string]: any;
};

/**
 * UnderlyingSecurityBaseInfoVO
 */
export type UnderlyingSecurityBaseInfoVO = {
  /**
   * 融资保证金比例
   */
  finRatio: number | null;
  /**
   * 融资保证金比例阈值
   */
  exchangeFinRatioLowerThreshold: number | null;
  /**
   * 融卷保证金比例阈值
   */
  exchangeSloRatioLowerThreshold: number | null;
  /**
   * 融资状态
   */
  finStatus: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 融券保证金比例
   */
  sloRatio: number | null;
  /**
   * 融券状态
   */
  sloStatus: null | string;
  /**
   * 系统融资保证金比例
   */
  systemFinRatio: number | null;

  mainModelFinRatio: number | null;
  mainModelSloRatio: number | null;
  manualAdjustFinRatioSum: number | null;
  manualAdjustSloRatioSum: number | null;
  /**
   * 系统融券保证金比例
   */
  systemSloRatio: number | null;
  [property: string]: any;
};

/**
 * SecurityCategoryBaseInfoVO
 */
export type SecurityCategoryBaseInfoVO = {
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 集中度分组名称
   */
  stockgroupName: null | string;
  /**
   * 系统集中度分组
   */
  systemStockgroupName: null | string;
  [property: string]: any;
};
