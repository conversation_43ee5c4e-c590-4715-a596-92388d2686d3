/**
 * 折算率模型相关模型
 */
/**
 * XyzqModelConfigDTO
 */
export type XyzqModelConfigDTO = {
  /**
   * 计算权重
   */
  calculateWeight: number | null;
  /**
   * 条件四
   */
  conditionFour: null | string;
  /**
   * 条件一
   */
  conditionOne: null | string;
  /**
   * 条件三
   */
  conditionThree: null | string;
  /**
   * 条件二
   */
  conditionTwo: null | string;
  /**
   * 额外规则列表
   */
  extraRuleList: XyzqModelExtraRuleDO[] | null;
  id: number | null;
  /**
   * 是否额外规则
   */
  ifExtra: number;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 计算得分配置列表
   */
  scoreConfigList: XyzqModelScoreConfigDO[] | null;
  /**
   * 得分类型
   */
  scoreType: ScoreType;
  [property: string]: any;
};

/**
 * XyzqModelExtraRuleDO
 */
export type XyzqModelExtraRuleDO = {
  /**
   * 计算项目
   */
  calculateItem: null | string;
  /**
   * 计算权重
   */
  calculateWeight: number | null;
  /**
   * 计算类型
   * 1:行情日期
   * 2:财报日期
   */
  calculateType: number | null;
  /**
   * 条件一
   */
  conditionOne: null | string;
  /**
   * 规则描述
   */
  description: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 得分类型
   */
  scoreType: ScoreType;
  [property: string]: any;
};

/**
 * 得分类型
 */
export enum ScoreType {
  VaR得分 = 'VAR得分',
  基本面得分 = '基本面得分',
  流动性得分 = '流动性得分',
  涨跌幅得分 = '涨跌幅得分',
}

/**
 * XyzqModelScoreConfigDO
 */
export type XyzqModelScoreConfigDO = {
  /**
   * 组别
   */
  groupType: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 得分
   */
  score: number | null;
  /**
   * 得分类型
   */
  scoreType: ScoreType;
  [property: string]: any;
};

/**
 * XyzqModelScoreGroupConfigDO
 */
export type XyzqModelScoreGroupConfigDO = {
  id: number | null;
  /**
   * 综合得分左区间
   */
  leftRange: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 综合得分右区间，左闭右开
   */
  rightRange: number | null;
  /**
   * 证券分类
   */
  secCategory: null | string;
  [property: string]: any;
};

/**
 * XyzqModelSecResultDO
 */
export type XyzqModelSecResultDO = {
  /**
   * 计算日期
   */
  date: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 得分分组
   */
  modelSecCategory: null | string;
  /**
   * 最终分组
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 策略名称
   */
  strategyName: null | string;
  [property: string]: any;
};

/**
 * XyzqModelSecScoreDO
 */
export type XyzqModelSecScoreDO = {
  /**
   * 计算日期
   */
  date: null | string;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券得分
   */
  score: number | null;
  /**
   * 得分排名
   */
  scoreRanking: number | null;
  /**
   * 得分对应的证券分组
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * XyzqModelSecScoreDetailDO
 */
export type XyzqModelSecScoreDetailDO = {
  /**
   * 计算项目
   */
  calculateItem: null | string;
  /**
   * 计算日期
   */
  date: null | string;
  /**
   * 组别类型
   */
  groupType: null | string;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券得分
   */
  score: number | null;
  /**
   * 得分排名
   */
  scoreRanking: number | null;
  /**
   * 得分类型
   */
  scoreType: ScoreType;
  /**
   * 当期计算项得分对应的真实的数值
   * 得分对应值
   */
  scoreValue: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * XyzqModelSecCategoryConfigDO
 */
export type XyzqModelSecCategoryConfigDO = {
  createTime: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券分组
   */
  secCategory: null | string;
  /**
   * 证券分组顺序
   */
  secCategoryOrder: number | null;
  updateTime: null | string;
  [property: string]: any;
};
/**
 * XyzqModelCategoryToHaircutConfigDO
 */
export type XyzqModelCategoryToHaircutConfigDO = {
  /**
   * 板块类型
   */
  blockType: null | string;
  createTime: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型证券分组结果
   */
  secCategory: null | string;
  /**
   * 对应的折算率
   */
  targetHaircut: number | null;
  updateTime: null | string;
  [property: string]: any;
};
