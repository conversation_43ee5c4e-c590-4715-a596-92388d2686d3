/**
 * XyzqHaircutModelFundConfigDO
 */
export type XyzqHaircutModelFundConfigDO = {
  /**
   * 计算规则类型：1.按照基金类别，2.按照基金规模大小 3.按照基金规模排位百分位
   */
  calculateRuleType: number;
  /**
   * 满足当前规则对应的指定折算率
   */
  designateHaircut: number | null;
  /**
   * 基金计算类型
   */
  fundCalculateType: null | string;
  /**
   * 基金规模计算方向
   */
  fundScaleCalculateDirection: null | string;
  /**
   * 基金规模（亿元）左区间
   */
  fundScaleLeftRange: number | null;
  /**
   * 基金规模（亿元）左区间
   */
  fundScaleRightRange: number | null;
  /**
   * 基金类型：ETF基金/其他基金
   */
  fundType: null | string;
  id: number | null;
  /**
   * 满足当前规则，是否调出担保证券
   */
  ifAdjustOut: number;
  /**
   * 折算率模型id
   */
  modelId: number | null;
  /**
   * 百分位排名左区间
   */
  percentileRankLeftRange: number | null;
  /**
   * 百分位排名右区间
   */
  percentileRankRightRange: number | null;
  /**
   * 指定基金类别
   */
  targetFundType: null | string;
  [property: string]: any;
};

/**
 * XyzqConcentraGroupModelFundConfigDO
 */
export type XyzqConcentraGroupModelFundConfigDO = {
  /**
   * 计算规则类型：1.按照基金类别，2.按照基金规模大小 3.按照基金规模排位百分位
   */
  calculateRuleType: number;
  createTime: null | string;
  /**
   * 满足当前规则对应的指定集中度分组
   */
  designateConcentraGroup: null | string;
  /**
   * 基金计算类型
   */
  fundCalculateType: null | string;
  /**
   * 基金规模计算方向
   */
  fundScaleCalculateDirection: null | string;
  /**
   * 基金规模（亿元）左区间
   */
  fundScaleLeftRange: number | null;
  /**
   * 基金规模（亿元）左区间
   */
  fundScaleRightRange: number | null;
  /**
   * 基金类型：ETF基金/其他基金
   */
  fundType: null | string;
  id: number | null;
  /**
   * 满足当前规则，是否调出担保证券
   */
  ifAdjustOut: number;
  /**
   * 集中度分组模型id
   */
  modelId: number | null;
  /**
   * 百分位排名左区间
   */
  percentileRankLeftRange: number | null;
  /**
   * 百分位排名右区间
   */
  percentileRankRightRange: number | null;
  /**
   * 指定基金类别
   */
  targetFundType: null | string;
  updateTime: null | string;
  [property: string]: any;
};
