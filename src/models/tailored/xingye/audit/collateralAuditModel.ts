/**
 * @file: collateralAudit.tsModels
 * @description: 担保品审核相关数据模型
 * @date: 2024/12/25
 * @author: AI Assistant
 */

import { BasicResponseModel } from '@/models/common/baseResponse';

/**
 * CollateralAdjustCensorInfoVO
 */
export interface CollateralAdjustCensorInfoVO {
  /**
   * 调整日期
   */
  adjustDate: null | string;
  /**
   * 调整记录id
   */
  adjustId: number | null;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 担保品调整信息
   */
  collateralAdjustInfo: null | CollateralAdjustDTO;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
}

/**
 * 担保品调整信息
 *
 * CollateralAdjustDTO
 */
export interface CollateralAdjustDTO {
  /**
   * 调整类型
   */
  adjustType: number;
  /**
   * 调整后折算率
   */
  afterHaircut: number | null;
  /**
   * 调整前折算率
   */
  beforeHaircut: number | null;
  [property: string]: any;
}

/**
 * UserDesignateHaircutDO
 */
export type UserDesignateHaircutDO = {
  /**
   * 上调或下调的折算率值
   */
  adjustHaircut: number | null;
  createTime: null | string;
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 创建用户名称
   */
  createUserName: null | string;
  /**
   * 指定担保品折算率
   */
  designateHaircut: number | null;
  id: number | null;
  /**
   * 修改日期
   */
  modifyDate: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  updateTime: null | string;
  /**
   * 更新用户
   */
  updateUser: null | string;
  /**
   * 删除状态
   */
  deleteStatus: null | number;
  /**
   * 更新用户名称
   */
  updateUserName: null | string;
  [property: string]: any;
};

// ==================== getWaitCensorCount 接口相关类型定义 ====================

/**
 * 审核数据类型枚举
 * @description 用于指定查询的审核数据类型
 */
export enum AuditDataType {
  /** 担保品 */
  COLLATERAL = 'collateral',
  /** 标的证券 */
  UNDERLYING = 'underlying',
  /** 证券分类 */
  CATEGORY = 'category',
}

/**
 * 获取待审核任务数量请求参数
 * @interface GetWaitCensorCountRequest
 */
export interface GetWaitCensorCountRequest {
  /**
   * 开始时间
   * @type {string}
   * @description 查询开始时间，格式：YYYY-MM-DD HH:mm:ss
   * @example "2024-12-24 15:00:00"
   */
  startTime?: string;

  /**
   * 结束时间
   * @type {string}
   * @description 查询结束时间，格式：YYYY-MM-DD HH:mm:ss
   * @example "2024-12-25 15:00:00"
   */
  endTime?: string;

  /**
   * 数据类型
   * @type {AuditDataType | string}
   * @description 审核数据类型：collateral(担保品) | underlying(标的证券) | category(证券分类)
   */
  dataType?: AuditDataType | string;
}
