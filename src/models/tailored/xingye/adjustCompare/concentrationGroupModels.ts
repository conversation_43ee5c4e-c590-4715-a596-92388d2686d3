/**
 * 集中度分组调整列表
 */
/**
 * ConcentrationGroupAdjustCompareInfoVO
 */
export interface ConcentrationGroupAdjustCompareInfoVO {
  /**
   * 调整后集中度分组
   */
  afterConcentrationGroup: null | string;
  /**
   * 调整前集中度分组
   */
  beforeConcentrationGroup: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 交易日期
   */
  tradeDate: null | string;
  [property: string]: any;
}

/**

 * ConcentrationGroupAdjustRecordDO
 */
export type ConcentrationGroupAdjustRecordDO = {
  /**
   * 调整日期
   */
  adjustDate: null | string;
  /**
   * 调整记录id
   */
  adjustId: number | null;
  /**
   * 调整后集中度分组
   */
  afterConcentrationGroup: null | string;
  /**
   * 调整前集中度分组
   */
  beforeConcentrationGroup: null | string;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 审核用户id
   */
  censorUserId: null | string;
  /**
   * 提交柜台状态：0-已提交，1-未提交
   */
  commitCounterStatus: number | null;
  /**
   * 提交柜台时间
   */
  commitCounterTime: null | string;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 申请用户id
   */
  commitUserId: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * UserDesignateConcentraGroupDO
 */
export type UserDesignateConcentraGroupDO = {
  createTime: null | string;
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 创建用户名称
   */
  createUserName: null | string;

  deleteStatus: null | number;
  /**
   * 指定担保品集中度分组
   */
  designateConcentraGroup: null | string;
  id: number | null;
  /**
   * 修改日期
   */
  modifyDate: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  updateTime: null | string;
  /**
   * 更新用户
   */
  updateUser: null | string;
  /**
   * 更新用户名称
   */
  updateUserName: null | string;
  [property: string]: any;
};
