/**
 * 公允价格调整相关模型
 */
/**
 * FairPriceAdjustCompareVO
 */
export type FairPriceAdjustCompareVO = {
  /**
   * 担保品状态,0-可担保,1-暂停,2-作废
   */
  assureStatus: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 交易类别,2:深交所，1:上交所，9:北交所
   */
  exchangeType: null | string;
  /**
   * 公允价格
   */
  fairPrice: number | null;
  /**
   * 调整后的动态公允价格启用标志：0取消动态公允价，1启用动态公允价
   */
  dynaFairPriceFlag: number | null;
  /**
   * 公允价格启用标志
   */
  fairPriceFlag: null | string;
  /**
   * 风险内容
   */
  riskContent: null | string;
  /**
   * 风险类型
   * 风险事件类型
   */
  riskType: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * AdjustRecordFairPriceDO
 */
export type AdjustRecordFairPriceDO = {
  adjustId: number | null;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 审核用户id
   */
  censorUserId: null | string;
  /**
   * 提交柜台状态：0-已提交，1-未提交
   */
  commitCounterStatus: number | null;
  /**
   * 提交柜台时间
   */
  commitCounterTime: null | string;
  /**
   * 申请日期
   */
  commitDate: null | string;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 申请用户id
   */
  commitUserId: null | string;
  /**
   * 调整后的公允价格
   */
  fairPrice: number | null;
  /**
   * 调整后的公允价格启用标志
   */
  fairPriceFlag: null | string;
  /**
   * 调整后的公允折算率
   */
  fairRatio: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};
