/**
 * 调整比较  担保品相关模型
 */
/**
 * CollateralAdjustCompareInfoVO
 */
export interface CollateralAdjustCompareInfoVO {
  /**
   * 模型折算率结果
   */
  collateralHaircut: number | null;
  /**
   * 我司担保品状态
   */
  collateralStatus: number;
  /**
   * 交易所折算率
   * 交易所担保品余额
   */
  exchangeCollateralHaircut: number | null;
  /**
   * 交易所担保品状态
   */
  exchangeCollateralStatus: number;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 交易日期
   */
  tradeDate: null | string;
  [property: string]: any;
}
