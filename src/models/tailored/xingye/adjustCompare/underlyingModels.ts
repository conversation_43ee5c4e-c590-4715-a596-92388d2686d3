/**
 * 调整比较  标的证券相关模型
 */
/**
 * UnderlyingAdjustCompareInfoVO
 */
export interface UnderlyingAdjustCompareInfoVO {
  /**
   * 交易所融资保证金比例
   * 模型融资保证金比例
   */
  exchangeFinancingMarginRatio: number | null;
  /**
   * 交易所融资标的状态
   */
  exchangeFinancingStatus: number;
  /**
   * 交易所融券保证金比例
   * 模型融券保证金比例
   */
  exchangeShortMarginRatio: number | null;
  /**
   * 交易所融券标的状态
   */
  exchangeShortSellingStatus: number;
  /**
   * 模型融资保证金比例
   */
  financingMarginRatio: number | null;
  /**
   * 我司融资标的状态
   */
  financingStatus: number;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 模型融券保证金比例
   */
  shortMarginRatio: number | null;
  /**
   * 我司融券标的状态
   */
  shortSellingStatus: number;
  /**
   * 交易日期
   */
  tradeDate: null | string;
  [property: string]: any;
}

/**
 * UserDesignateMarginRatioDO
 */
export type UserDesignateMarginRatioDO = {
  /**
   * 上调或下调的融资保证金比例值
   */
  adjustFinanceMarginRatio: number | null;
  /**
   * 上调或下调的融券保证金比例值
   */
  adjustShortMarginRatio: number | null;
  createTime: null | string;
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 创建用户名称
   */
  createUserName: null | string;
  /**
   * 指定融资保证金比例
   */
  designateFinanceMarginRatio: number | null;
  /**
   * 指定融券保证金比例
   */
  designateShortMarginRatio: number | null;
  id: number | null;
  /**
   * 修改日期
   */
  modifyDate: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  updateTime: null | string;
  /**
   * 更新用户
   */
  updateUser: null | string;
  /**
   * 是否删除
   */
  deleteStatus: null | number;
  /**
   * 更新用户名称
   */
  updateUserName: null | string;
  [property: string]: any;
};
