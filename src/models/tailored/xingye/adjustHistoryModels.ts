/**
 * 调整历史相关模型
 */

/**
 * CollateralAdjustRecordDO
 */
export type CollateralAdjustRecordDO = {
  /**
   * 调整日期
   */
  adjustDate: null | string;
  /**
   * 调整记录id
   */
  adjustId: number | null;
  /**
   * 调整类型
   */
  adjustType: number;
  /**
   * 调整后折算率
   */
  afterHaircut: number | null;
  /**
   * 调整前折算率
   */
  beforeHaircut: number | null;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 审核用户id
   */
  censorUserId: null | string;
  /**
   * 提交柜台状态：0-已提交，1-未提交
   */
  commitCounterStatus: number | null;
  /**
   * 提交柜台时间
   */
  commitCounterTime: null | string;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 申请用户id
   */
  commitUserId: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * UnderlyingAdjustRecordDO
 */
export type UnderlyingAdjustRecordDO = {
  /**
   * 调整日期
   */
  adjustDate: null | string;
  /**
   * 调整记录id
   */
  adjustId: number | null;
  /**
   * 调整后融资保证金比例
   */
  afterFinanceMarginRatio: number | null;
  /**
   * 调整后融券保证金比例
   */
  afterShortMarginRatio: number | null;
  /**
   * 调整前融资保证金比例
   */
  beforeFinanceMarginRatio: number | null;
  /**
   * 调整前融券保证金比例
   */
  beforeShortMarginRatio: number | null;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 审核用户id
   */
  censorUserId: null | string;
  /**
   * 提交柜台状态：0-已提交，1-未提交
   */
  commitCounterStatus: number | null;
  /**
   * 提交柜台时间
   */
  commitCounterTime: null | string;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 申请用户id
   */
  commitUserId: null | string;
  /**
   * 融资标的调整类型
   */
  financeAdjustType: number;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 融券标的调整类型
   */
  shortAdjustType: number;
  [property: string]: any;
};

/**
 * ConcentraGroupAdjustRecordDO
 */
export type ConcentraGroupAdjustRecordDO = {
  /**
   * 调整日期
   */
  adjustDate: null | string;
  /**
   * 调整记录id
   */
  adjustId: number | null;
  /**
   * 调整后集中度分组
   */
  afterConcentrationGroup: null | string;
  /**
   * 调整前集中度分组
   */
  beforeConcentrationGroup: null | string;
  /**
   * 审核用户
   */
  censorPerson: null | string;
  /**
   * 审核理由
   */
  censorReason: null | string;
  /**
   * 审核状态
   */
  censorStatus: number;
  /**
   * 审核时间
   */
  censorTime: null | string;
  /**
   * 审核用户id
   */
  censorUserId: null | string;
  /**
   * 提交柜台状态：0-已提交，1-未提交
   */
  commitCounterStatus: number | null;
  /**
   * 提交柜台时间
   */
  commitCounterTime: null | string;
  /**
   * 申请用户
   */
  commitPerson: null | string;
  /**
   * 申请理由
   */
  commitReason: null | string;
  /**
   * 申请时间
   */
  commitTime: null | string;
  /**
   * 申请用户id
   */
  commitUserId: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};
