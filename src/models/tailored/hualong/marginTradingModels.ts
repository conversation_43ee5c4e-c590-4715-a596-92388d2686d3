/**
 * @file: labelManageModels.ts
 * @description: 标签管理数据模型
 */

/**
 * MyPageInfo«HuaLongExchangeCollateralAdjust»
 */
export type MyPageInfoHuaLongExchangeCollateralAdjust = {
  /**
   * 当前页码
   */
  current: number | null;
  /**
   * 记录列表
   */
  records: HuaLongExchangeCollateralAdjust[] | null;
  /**
   * 每页记录数
   */
  size: number | null;
  /**
   * 总记录数
   */
  total: number | null;
  [property: string]: any;
};

/**
 * HuaLongExchangeCollateralAdjust 交易所担保品调整记录列表信息
 */
export type HuaLongExchangeCollateralAdjust = {
  /**
   * 操作ID
   */
  actionId: number | null;
  /**
   * 参考变化原因
   */
  adjustReason: null | string;
  /**
   * 变动类型
   */
  adjustType: null | string;
  /**
   * 变动日期
   */
  date: null | string;
  id: number | null;
  /**
   * 是否注册制上市
   */
  ifRegister: null | string;
  /**
   * 所属指数
   */
  index: null | string;
  /**
   * 上期折算率
   * 上期折算率(%)
   */
  lastHaircut: number | null;
  /**
   * 上周最后一个交易日静态市盈率
   */
  lastTradingDayStaticPe: number | null;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 最新折算率
   * 最新折算率(%)
   */
  newestHaircut: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券简称
   */
  secName: null | string;
  /**
   * 证券品种
   */
  secType: null | string;
  [property: string]: any;
};

/**
 * SecTypeHaircutConfigDO 获取不同证券类型的折算率默认配置信息
 */
export type SecTypeHaircutConfigDO = {
  /**
   * 操作id
   */
  actionId: number | null;
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 折算率
   */
  haircut: number | null;
  /**
   * 证券类型
   */
  secType: null | string;
  /**
   * 修改用户
   */
  updateUser: null | string;
  [property: string]: any;
};
/**
 * MyPageInfo«HuaLongCollateralHaircutExport»
 */
export type MyPageInfoHuaLongCollateralHaircutExport = {
  /**
   * 当前页码
   */
  current: number | null;
  /**
   * 记录列表
   */
  records: HuaLongCollateralHaircutExport[] | null;
  /**
   * 每页记录数
   */
  size: number | null;
  /**
   * 总记录数
   */
  total: number | null;
  [property: string]: any;
};
/**
 * HuaLongCollateralHaircutExport
 */
export type HuaLongCollateralHaircutExport = {
  /**
   * 操作id
   */
  actionId: number | null;
  /**
   * 实际折算率
   */
  actualHaircut: number | null;
  /**
   * 删除标志
   */
  delFlag: null | string;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};
