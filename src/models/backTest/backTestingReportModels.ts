/**
 * 模型回测相关模型
 */

/**
 * BackTestingReport
 */
export type BackTestingReport = {
  backTestingId: null | string;
  chosenStocks: null | string;
  coordinatesForKs: null | string;
  coordinatesForLevelStockAvgMarketValue: null | string;
  coordinatesForLevelStockCount: null | string;
  coordinatesForRoc: null | string;
  createTime: null | string;
  date: null | string;
  falseNegative: number | null;
  falsePositive: number | null;
  howGood: null | string;
  industry: null | string;
  isReady: number | null;
  jsonString: null | string;
  last: null | string;
  modelId: number | null;
  modelName: null | string;
  reportName: null | string;
  sector: null | string;
  stocksNumber: number | null;
  trueNegative: number | null;
  truePositive: number | null;
  updateTime: null | string;
  userId: null | string;
  userName: null | string;
  [property: string]: any;
};

/**
 * ChartCoordinates
 */
export type ChartCoordinates = {
  count: number | null;
  median: number | null;
  typeName: null | string;
  [property: string]: any;
};

/**
 * BackTestingDetails
 */
export type BackTestingDetails = {
  /**
   * 模型预测值
   */
  actualLevel: number | null;
  /**
   * 回测方案id，由uuid生成
   */
  backTestingId: null | string;
  /**
   * //模型id
   */
  code: null | string;
  createTime: null | string;
  /**
   * 记录id
   */
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型名称
   */
  modelName: null | string;
  /**
   * 报告名称
   */
  reportName: null | string;
  /**
   * TP、FP、TN、FN
   */
  result: null | string;
  resultCount: number | null;
  /**
   * 股票代码
   */
  stockId: null | string;
  /**
   * 股票名称
   */
  stockName: null | string;
  /**
   * 阈值
   */
  threshold: number | null;
  /**
   * B为坏样本，G为好样本，L为标签样本
   */
  type: null | string;
  updateTime: null | string;
  [property: string]: any;
};

/**
 * BackTestSecSampleDO
 */
export type BackTestSecSampleDO = {
  /**
   * 回测日期
   */
  date: null | string;
  /**
   * 样本类型
   */
  sampleType: SampleType;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * 样本类型
 */
export enum SampleType {
  FP = 'FP',
  Fn = 'FN',
  Tn = 'TN',
  Tp = 'TP',
}

/**
 * BackTestReportDTO
 */
export type BackTestReportDTO = {
  /**
   * 模型回测报告的id
   */
  backTestId: null | string;
  /**
   * 选择的指数
   */
  chosenIndex: null | string;
  /**
   * 自定义股票范围
   */
  chosenStocks: null | string;
  /**
   * KS曲线的坐标点
   */
  coordinatesForKs: null | string;
  /**
   * ROC曲线的坐标点
   */
  coordinatesForRoc: null | string;
  createTime: null | string;
  /**
   * 创建日期
   */
  date: null | string;
  /**
   * 假阴性的数量
   */
  falseNegative: number | null;
  /**
   * 假阳性的数量
   */
  falsePositive: number | null;
  /**
   * 文字描述模型效果
   */
  howGood: null | string;
  /**
   * 行业id
   */
  industry: null | string;
  /**
   * 报告是否生成完毕，完成为1，否则为0，2为特殊情况，意味成生成失败
   */
  isReady: number | null;
  /**
   * 用户传过来的json的序列化
   */
  jsonString: null | string;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型名称
   */
  modelName: null | string;
  /**
   * 模型预测条件阈值
   */
  modelPredictThreshold: null | BackTestModelPredictThresholdDO;
  /**
   * 回测报告的名称
   */
  reportName: null | string;
  /**
   * 风险事件阈值列表
   */
  riskEventThresholdList: BackTestRiskEventThresholdDO[] | null;
  /**
   * 风险事件类型
   */
  riskEventType: RiskEventType;
  /**
   * 证券板块
   */
  sector: null | string;
  /**
   * 模型回测涉及的时间跨度
   */
  startEndDates: null | string;
  /**
   * 真阴性的数量
   */
  trueNegative: number | null;
  /**
   * 真阳性的数量
   */
  truePositive: number | null;
  updateTime: null | string;
  /**
   * 创建用户的id
   */
  userId: null | string;
  /**
   * 用户名称
   */
  userName: null | string;
  [property: string]: any;
};

/**
 * 模型预测条件阈值
 *
 * BackTestModelPredictThresholdDO
 */
export type BackTestModelPredictThresholdDO = {
  /**
   * 平均评级
   */
  avgLevel: null | string;
  /**
   * 回测报告id
   */
  backTestId: null | string;
  /**
   * 风险事件前X个交易日
   */
  daysBeforeRiskEvent: number | null;
  /**
   * 出现的评级
   */
  levelCombo: null | string;
  /**
   * 评级变动向下调整x级
   */
  levelDownAdjustment: number | null;
  /**
   * 评级出现次数大于等于x次
   */
  levelFrequency: number | null;
  /**
   * 计算评级出现频率的评级范围，用逗号分隔
   */
  levelFrequencyRange: null | string;
  /**
   * 关系符号
   */
  relation: number;
  /**
   * 阈值文字描述
   */
  thresholdDescription: null | string;
  thresholdId: number | null;
  [property: string]: any;
};

/**
 * BackTestRiskEventThresholdDO
 */
export type BackTestRiskEventThresholdDO = {
  /**
   * 回测报告id
   */
  backTestId: null | string;
  /**
   * 流通市值(亿元)
   */
  circulationMarketValue: number | null;
  /**
   * 收盘股价
   */
  closePrice: number | null;
  /**
   * 连续涨跌停次数
   */
  consecutiveTimes: number | null;
  /**
   * 是涨还是跌，涨为1，跌为0
   */
  ifUp: number | null;
  /**
   * 标签组合
   */
  labelCombo: null | string;
  /**
   * 过去x天
   */
  pastDays: number | null;
  /**
   * 累计涨跌幅
   */
  range: number | null;
  /**
   * 阈值条件文字描述
   */
  thresholdDescription: null | string;
  thresholdId: number | null;
  /**
   * 总市值(亿元)
   */
  totalMarketValue: number | null;
  /**
   * 成交量（万股）
   */
  tradingVolume: number | null;
  [property: string]: any;
};

/**
 * 风险事件类型
 */
export enum RiskEventType {
  总市值 = '总市值',
  成交量 = '成交量',
  流通市值 = '流通市值',
  累计涨跌幅 = '累计涨跌幅',
  股价 = '股价',
  连续涨跌停 = '连续涨跌停',
  风险标签 = '风险标签',
}

/**
 * ThresholdSampleResultDTO
 */
export type ThresholdSampleResultDTO = {
  /**
   * 假阴性数量
   */
  falseNegativeCount: number | null;
  /**
   * 假阳性数量
   */
  falsePositiveCount: number | null;
  /**
   * 预测精准率：（真阳性） / （真阳性 + 假阳性）
   * 预测精准率
   */
  predictPrecisionRate: number | null;
  /**
   * 预测成功率：（真阴性 + 真阳性） / 全部样本数量
   * 预测成功率
   */
  predictSuccessRate: number | null;
  /**
   * 风险事件阈值文字描述
   */
  thresholdDescription: null | string;
  /**
   * 风险事件阈值id
   */
  thresholdId: number | null;
  /**
   * 真阴性数量
   */
  trueNegativeCount: number | null;
  /**
   * 真阳性数量
   */
  truePositiveCount: number | null;
  [property: string]: any;
};
