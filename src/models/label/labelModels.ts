/**
 * @file: labelModels
 * @description: 标签相关数据模型
 * @date: 2024/9/3
 * @author: <PERSON> Ye
 */

/**
 * LabelInfoVO - 标签信息
 * @interface LabelInfoVO
 * @property {string} date - 标签日期
 * @property {string} infoDescribe - 标签详情
 * @property {string} labelName - 标签名称
 */
export interface LabelInfoVO {
  /**
   * 标签日期
   */
  date: null | string;
  /**
   * 标签详情
   */
  infoDescribe: null | string;
  /**
   * 标签名称
   */
  labelName: null | string;
  /**
   * 风险级别
   */
  riskLevel: number | null;
  [property: string]: any;
}

/**
 * SecLabelYearVO - 证券标签年度数据
 * @interface SecLabelYearVO
 * @property {LabelInfoVO[]} labelInfoList - 标签信息列表
 * @property {number} year - 年份
 */
export interface SecLabelYearVO {
  /**
   * 标签信息列表
   */
  labelInfoList: LabelInfoVO[];
  /**
   * 年份
   */
  year: number | null;
  [property: string]: any;
}

/**
 * LabelConfigVO - 标签配置信息
 * @interface LabelConfigVO
 * @property {number} count - 标签对应证券数量
 * @property {string} labelDescription - 标签描述
 * @property {string} labelName - 标签名称
 * @property {string} userName - 用户名称
 */
export type LabelConfigVO = {
  /**
   * 标签对应证券数量
   */
  count: number | null;
  /**
   * 标签描述
   */
  labelDescription: null | string;
  /**
   * 标签名称
   */
  labelName: null | string;
  /**
   * 用户名称
   */
  userName: null | string;
  [property: string]: any;
};

/**
 * LabelOverlapDO
 */
export type LabelOverlapDO = {
  createTime: null | string;
  id: number | null;
  /**
   * 标签名称
   */
  labelName: null | string;
  /**
   * 重合数量
   */
  overlapCount: number | null;
  /**
   * 重合比例
   */
  overlapRatio: number | null;
  /**
   * 目标标签名称
   */
  targetLabelName: null | string;
  updateTime: null | string;
  [property: string]: any;
};

/**
 * StockLabelVO
 */
export type StockLabelVO = {
  /**
   * 收盘价
   */
  closes: number | null;
  /**
   * 行业名称
   */
  industryName: null | string;
  /**
   * 标签列表
   */
  labels: string[] | null;
  /**
   * 评级
   */
  level: null | string;
  /**
   * 股票代码
   */
  stockId: null | string;
  /**
   * 股票名称
   */
  stockName: null | string;
  /**
   * 市值
   */
  totalAmount: number | null;
  /**
   * 用户评级
   */
  userLevel: null | string;
  [property: string]: any;
};
