/**
 * @file: labelManageModels.ts
 * @description: 标签管理数据模型
 */

import { TypeCountVO } from '@/models/common/utilModels';

/**
 * SecLabelHistoryVO
 */
export type SecLabelHistoryVO = {
  createTime: null | string;
  /**
   * 发生日期
   */
  date: null | string;
  /**
   * 标签状态
   */
  enableStatus: null | string;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 是否策略计算
   */
  ifStrategyCal: null | string;
  /**
   * 标签信息描述
   */
  infoDescribe: null | string;
  /**
   * 标签名称
   */
  labelName: null | string;
  /**
   * 标签类型
   */
  labelType: null | string;
  /**
   * 发生日期
   */
  occurrenceDate: null | string;
  /**
   * 证券代码
   */
  stockId: null | string;
  /**
   * 证券名称
   */
  stockName: null | string;
  updateTime: null | string;
  /**
   * 标签值
   */
  value: number | null;
  [property: string]: any;
};
/**
 * TypeCountListVO
 */
export type TypeCountListVO = {
  /**
   * 类型
   */
  type: null | string;
  /**
   * 类型统计列表
   */
  typeCountList: TypeCountVO[] | null;
  [property: string]: any;
};

/** 标签管理/标签配置
 * LabelConfigDTO
 */
export interface LabelConfigDTO {
  /**
   * 记录数量
   */
  count: number | null;
  /**
   * 标签是否启用
   * 标签是否启用:0启用，1禁用
   */
  enableStatus: null | string;
  /**
   * 是否高风险标签
   */
  highRisk: null | string;
  /**
   * 主键
   */
  id: null | string;
  /**
   * 是否加入财务分析
   * 是否加入财务分析：0是，1否
   */
  isFinancial: null | string;
  /**
   * 标签描述
   */
  labelDescription: null | string;
  /**
   * 标签名称
   */
  labelName: null | string;
  /**
   * 标签类型
   */
  labelType: null | string;
  /**
   * 关联表，用；分隔
   */
  relateTable: null | string;
  /**
   * 风险等级
   */
  riskLevel: null | string;
  /**
   * 更新频率
   */
  updateFrequency: null | string;
  /**
   * 有效期时长
   */
  validityLength: null | string;
  /**
   * 有效期单位
   */
  validityUnit: ValidityUnit;
  /**
   * 是否加入预警列表
   * 是否加入预警列表：0是，1否
   */
  warning: null | string;

  [property: string]: any;
}

/**
 * 有效期单位
 */
export enum ValidityUnit {
  D = 'D',
  M = 'M',
  Y = 'Y',
}
