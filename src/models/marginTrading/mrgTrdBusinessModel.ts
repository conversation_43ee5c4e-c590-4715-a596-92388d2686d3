/**
 * @file 融资融券业务相关ts类型定义
 * @description: 融资融券业务相关ts类型定义
 * @date: 2025/07/05
 * @author: <PERSON> Ye
 */

/**
 * 交易所外规阈值配置相关模型
 */
/**
 * ExchangeRuleThresholdConfigDO
 */
export interface ExchangeRuleThresholdConfigDO {
  id: number | null;
  /**
   * 融资融券业务类型
   */
  mrdTrdType: MrdTrdType;
  /**
   * 证券类型
   */
  secType: number;
  /**
   * 阈值
   */
  thresholdValue: number | null;
  /**
   * 类型名称
   */
  typeName: null | string;

  [property: string]: any;
}

/**
 * 融资融券业务类型
 */
export enum MrdTrdType {
  Category = 'category',
  Collateral = 'collateral',
  Underlying = 'underlying',
}
