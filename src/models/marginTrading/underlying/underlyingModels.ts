/**
 * MarginRatioModelConfigDO
 */
export type MarginRatioModelConfigDO = {
  /**
   * 模型计算时间
   */
  calculateTime: null | string;
  createTime: null | string;
  /**
   * 删除标志
   */
  deleteStatus: number;
  /**
   * 启用状态
   */
  enableStatus: number;
  /**
   * 主备状态
   */
  mainStatus: number;
  /**
   * 保证金比例模型id
   */
  modelId: number | null;
  /**
   * 保证金比例模型名称
   */
  modelName: null | string;
  /**
   * 备注
   */
  remark: null | string;
  updateTime: null | string;
  /**
   * 用户id
   */
  userId: null | string;
  /**
   * 用户名称
   */
  userName: null | string;
  /**
   * 是否有修改权限：0有权限，1无权限
   */
  ifHasEditPermission?: number;
  [property: string]: any;
};

/**
 * ModelMarginRatioRuleDO
 */
export type ModelMarginRatioRuleDO = {
  id: number | null;
  /**
   * 左区间是否开放
   */
  ifLeftOpen: number;
  /**
   * 右区间是否开放
   */
  ifRightOpen: number;
  /**
   * 条件下限值
   */
  lowerBound: null | string;
  /**
   * 保证金比例类型：0代表融资保证金比例，1代表融券保证金比例
   */
  marginRatioType: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 规则类型：0指定绝对值，1比较最大值或最小值
   */
  ruleType: number | null;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券品种
   */
  secType: number;
  /**
   * 目标保证金比例
   */
  targetMarginRatio: null | string;
  /**
   * 条件上限值
   */
  upperBound: null | string;
  [property: string]: any;
};

/**
 * ModelSecMarginRatioHistoryDO
 */
export type ModelSecMarginRatioHistoryDO = {
  createTime: null | string;
  /**
   * 模型计算日期
   */
  date: null | string;
  /**
   * 融资保证金比例
   */
  financeMarginRatio: number | null;
  /**
   * 融资标的状态：0可融资，1不可融资
   */
  financeStatus: number;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型用户id
   */
  modelUserId: null | string;
  /**
   * 证券代码带后缀
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 融券保证金比例
   */
  shortMarginRatio: number | null;
  /**
   * 融券标的状态：0可融券，1不可融券
   */
  shortStatus: number;
  updateTime: null | string;
  [property: string]: any;
};

/**
 * MarginRatioModelResultCompareVO - 保证金比例模型结果历史比较
 */
export type MarginRatioModelResultCompareVO = {
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券类型
   */
  secType: number;
  /**
   * 新融资保证金比例
   */
  newFinanceMarginRatio: number;
  /**
   * 旧融资保证金比例
   */
  oldFinanceMarginRatio: number;
  /**
   * 新融券保证金比例
   */
  newShortMarginRatio: number;
  /**
   * 旧融券保证金比例
   */
  oldShortMarginRatio: number;
  /**
   * 新融资状态
   */
  newFinanceStatus: number;
  /**
   * 旧融资状态
   */
  oldFinanceStatus: number;
  /**
   * 新融券状态
   */
  newShortStatus: number;
  /**
   * 旧融券状态
   */
  oldShortStatus: number;
  /**
   * 新日期
   */
  newDate: null | string;
  /**
   * 旧日期
   */
  oldDate: null | string;
  [property: string]: any;
};
