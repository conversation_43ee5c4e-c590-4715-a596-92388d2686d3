/**
 * ConcentrationModelConfigDO
 */
export type ConcentrationModelConfigDO = {
  createTime: null | string;
  /**
   * 删除标志
   */
  deleteStatus: null | number;
  relatedLevelStrategyModelId: null | number;
  /**
   * 启用状态
   */
  enableStatus: number;
  /**
   * 主备状态
   */
  mainStatus: number;
  /**
   * 集中度模型id
   */
  modelId: number | null;
  /**
   * 集中度模型名称
   */
  modelName: null | string;
  /**
   * 备注
   */
  remark: null | string;
  updateTime: null | string;
  /**
   * 用户id
   */
  userId: null | string;
  /**
   * 是否有编辑权限：0有权限，1无权限
   */
  ifModifyPermission?: number | null;
  /**
   * 创建用户名称
   */
  userName?: null | string;
  /**
   * 创建用户
   */
  createUser?: null | string;
  /**
   * 关联评级策略模型名称
   */
  relatedLevelStrategyModelName?: null | string;
  /**
   * 是否正在计算：0正在计算，1计算完成
   */
  ifCalculating?: number | null;
  /**
   * 计算时间
   */
  calculateTime?: null | string;
  [property: string]: any;
};

/**
 * TableCellVO
 */
export type TableCellVO = {
  /**
   * 横坐标
   */
  across: null | string;
  /**
   * 状态
   */
  flag: number | null;
  /**
   * 纵坐标
   */
  vertical: null | string;
  [property: string]: any;
};

/**
 * LevelMaintenanceConcentrationVO
 */
export type LevelMaintenanceConcentrationVO = {
  /**
   * 集中度数值
   */
  concentration: number | null;
  /**
   * 主键
   */
  id: number | null;
  /**
   * 左区间是否开放
   */
  ifLeftOpen: boolean | null;
  /**
   * 是否注册制：0否，1是
   */
  ifRegi: number | null;
  /**
   * 右区间是否开放
   */
  ifRightOpen: boolean | null;
  /**
   * 当前评级
   */
  level: null | string;
  /**
   * 下限值
   */
  lowerBound: number | null;
  /**
   * 上限值
   */
  upperBound: number | null;
  [property: string]: any;
};
