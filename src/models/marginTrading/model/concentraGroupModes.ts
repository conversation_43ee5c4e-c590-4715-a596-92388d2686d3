/*
集中度分组模型相关模型
 */
/**
 * ModelSecConcentraGroupHistoryDO
 */
export type ModelSecConcentraGroupHistoryDO = {
  /**
   * 担保证券状态：0可担保，1不可担保
   */
  collateralStatus: number;
  /**
   * 集中度分组
   */
  concentrationGroup: null | string;
  /**
   * 计算日期
   */
  date: null | string;
  id: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券代码带后缀
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 模型用户id
   */
  userId: null | string;
  [property: string]: any;
};
/**
 * LevelSrtModelConcentraGroupMappingVO
 */
export type LevelSrtModelConcentraGroupMappingVO = {
  /**
   * 映射的集中度分组
   */
  concentrationGroup: null | string;
  /**
   * 分类评级模型结果
   */
  levelSrtModelLevel: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  [property: string]: any;
};

/**
 * ConcentraGroupModelResultCompareVO - 集中度分组模型结果比较
 */
export type ConcentraGroupModelResultCompareVO = {
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券类型
   */
  secType: number;
  /**
   * 新模型结果
   */
  newModelResult: null | string;
  /**
   * 旧模型结果
   */
  oldModelResult: null | string;
  /**
   * 新日期
   */
  newDate: null | string;
  /**
   * 旧日期
   */
  oldDate: null | string;
  [property: string]: any;
};
