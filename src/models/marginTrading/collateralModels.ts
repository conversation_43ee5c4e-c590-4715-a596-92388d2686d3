/**
 * @file: collateralModels
 * @description: 担保品业务相关模型
 * @date: 2024/9/3
 * @author: <PERSON> Ye
 */

import { LabelInfoVO } from '@/models/label/labelModels';
import { CommonStatus } from '@/enums/baseEnum';
import { AdmissionCensorType } from '@/enums/marginTrading/collateralEnum';

/**
 * CollateralInfoVO - 担保品信息
 * @interface CollateralInfoVO
 * @property {number} closePrice 最新收盘价(元)
 * @property {number} collateralHaircut 担保品折算率
 * @property {null|string} date 最新日期
 * @property {CommonStatus} enableStatus 担保品类型
 * @property {null|string} industryName 一级行业
 * @property {null|string} level 系统评级
 * @property {null|string} secCode 证券代码
 * @property {null|string} secName 证券名称
 * @property {null|string} stockType 证券板块
 * @property {number} totalAmount 最新市值(亿元)
 * @property {LabelInfoVO[]} warningList 预警信息
 */
export interface CollateralInfoVO {
  /**
   * 最新收盘价(元)
   */
  closePrice?: number | null;
  /**
   * 担保品折算率
   */
  collateralHaircut?: number | null;
  /**
   * 用户担保品折算率
   */
  userCollateralHaircut?: number | null;
  /**
   * 最新日期
   */
  date?: null | string;
  /**
   * 担保品类型
   */
  enableStatus?: CommonStatus | null;
  /**
   * 一级行业
   */
  industryName?: null | string;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 用户评级
   */
  userLevel: null | string;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 证券代码
   */
  secName?: null | string;
  /**
   * 证券板块
   */
  stockType?: null | string;
  /**
   * 最新市值(亿元)
   */
  totalAmount?: number | null;
  /**
   * 预警信息
   */
  warningList?: LabelInfoVO[] | null;

  [property: string]: any;
}

/**
 * CollAdmissionCensorVO - 担保品准入审核信息
 * @interface CollAdmissionCensorVO
 * @property {null|string} adjustType 调整类型
 * @property {null|string} censorPerson 审核用户
 * @property {AdmissionCensorType} censorStatus 审核状态
 * @property {number} closePrice 最新收盘价(元)
 * @property {null|string} comment 申请理由
 * @property {null|string} commitPerson 申请用户
 * @property {null|string} createTime 申请时间
 * @property {null|string} date 日期
 * @property {null|string} level 系统评级
 * @property {null|string} secCode 证券代码
 * @property {null|string} secName 证券名称
 * @property {null|string} stockType 证券板块
 * @property {number} totalAmount 最新市值(亿元)
 * @property {null|string} updateTime 审核时间
 */
export interface CollAdmissionCensorVO {
  /**
   * 调整类型
   */
  adjustType?: null | string;
  /**
   * 审核用户
   */
  censorPerson?: null | string;
  /**
   * 审核状态
   */
  censorStatus?: AdmissionCensorType;
  /**
   * 最新收盘价(元)
   */
  closePrice?: number | null;
  /**
   * 申请理由
   */
  comment?: null | string;
  /**
   * 申请用户
   */
  commitPerson?: null | string;
  /**
   * 申请时间
   */
  createTime?: null | string;
  /**
   * 日期
   */
  date?: null | string;
  /**
   * 系统评级
   */
  level?: null | string;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 证券名称
   */
  secName?: null | string;
  /**
   * 证券板块
   */
  stockType?: null | string;
  /**
   * 最新市值(亿元)
   */
  totalAmount?: number | null;
  /**
   * 审核时间
   */
  updateTime?: null | string;

  [property: string]: any;
}

/**
 * MrgTrdAdmissionDO - 担保品准入申请信息
 * @interface MrgTrdAdmissionDO
 * @property {null|string} censorPerson 审核人
 * @property {AdmissionCensorType} censorStatus 审核状态
 * @property {null|string} comment 申请理由
 * @property {null|string} commitPerson 申请人
 * @property {null|string} date 日期
 * @property {number} id 主键
 * @property {null|string} secCode 证券代码
 * @property {AdjustType} type 调整类型
 */
export interface MrgTrdAdmissionDO {
  /**
   * 审核人
   */
  censorPerson?: null | string;
  /**
   * 审核状态
   */
  censorStatus?: AdmissionCensorType;
  /**
   * 申请理由
   */
  comment?: null | string;
  /**
   * 申请人
   */
  commitPerson?: null | string;
  /**
   * 日期
   */
  date?: null | string;
  /**
   * 主键
   */
  id?: number | null;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 调整类型
   */
  type?: AdjustType;

  [property: string]: any;
}

/**
 * 调整类型
 */
export enum AdjustType {
  CollateralIn = 'COLLATERAL_IN',
  CollateralOut = 'COLLATERAL_OUT',
}

/**
 * CollateralExportModel - 担保品导出模型
 * @interface CollateralExportModel
 * @property {number} closePrice 收盘价(亿元)
 * @property {number} collateralHaircut 我司折算率
 * @property {null|string} date 最新日期
 * @property {CommonStatus} enableStatus 是否担保品
 * @property {null|string} industryName 一级行业名称
 * @property {null|string} level 系统评级
 * @property {null|string} peerHaircutRange 同业折算率区间
 * @property {number} rollingPe 滚动市盈率
 * @property {null|string} secCode 证券代码
 */
export interface CollateralExportModel {
  /**
   * 收盘价(亿元)
   */
  closePrice: number | null;
  /**
   * 我司折算率
   */
  collateralHaircut: number | null;
  /**
   * 最新日期
   */
  date: null | string;
  /**
   * 是否担保品
   */
  enableStatus: CommonStatus;
  /**
   * 一级行业名称
   */
  industryName: null | string;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 同业折算率区间
   */
  peerHaircutRange: null | string;
  /**
   * 滚动市盈率
   */
  rollingPe: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券板块
   */
  stockType: null | string;
  /**
   * 市值(亿元)
   */
  totalAmount: number | null;
  /**
   * 当前用户评级
   */
  userLevel: null | string;

  /**
   * 当前用户折算率
   */
  userCollateralHaircut: number | null;

  [property: string]: any;
}

/**
 * CollateralHaircutItemDTO - 担保品折算率子事项字典DTO
 * @interface CollateralHaircutItemDTO
 * @property {CollateralHaircutItemDTO[]} children 子事项列表
 * @property {number} collateralHaircut 担保品折算率
 * @property {CommonStatus} enableStatus 担保品类型
 * @property {null|string} frequency 监控频率
 * @property {number} id 主键
 * @property {null|string} itemId 事项id
 * @property {null|string} itemName 事项名称
 * @property {number} manualStatus 人工状态
 * @property {null|string} parentId 父事项id
 * @property {null|string} template 模板
 */
export interface CollateralHaircutItemDTO {
  /**
   * 子事项列表
   */
  children: CollateralHaircutItemDTO[];
  /**
   * 担保品折算率
   */
  collateralHaircut: number | null;
  enableStatus: CommonStatus;
  /**
   * 监控频率
   */
  frequency: null | string;
  /**
   * 主键
   */
  id: number | null;
  /**
   * 事项id
   */
  itemId: null | string;
  /**
   * 模型id
   */
  modelId: null | string;
  /**
   * 是否用户
   */
  ifUser: null | boolean;
  /**
   * 事项名称
   */
  itemName: null | string;
  manualStatus: number | null;
  /**
   * 父事项id
   */
  parentId: null | string;
  template: null | string;

  [property: string]: any;
}

/**
 * createCollateralHaircutItemDTO - 创建担保品折算率子事项字典DTO
 * @function createCollateralHaircutItemDTO
 * @returns {CollateralHaircutItemDTO}
 */
export function createCollateralHaircutItemDTO(): CollateralHaircutItemDTO {
  return {
    children: [],
    collateralHaircut: null,
    enableStatus: CommonStatus.ENABLE,
    frequency: null,
    id: null,
    itemId: null,
    itemName: null,
    manualStatus: null,
    modelId: null,
    parentId: null,
    template: null,
    ifUser: null,
  };
}

/**
 * CollateralHaircutItemDO - 担保品折算率子事项字典DO
 * @interface CollateralHaircutItemDO
 * @property {number} calculateValue 计算值
 * @property {number} collateralHaircut 担保品折算率
 * @property {CommonStatus} editStatus 编辑状态
 * @property {CommonStatus} enableStatus 启用状态
 * @property {number} id 主键id
 * @property {null|string} itemId 事项id
 * @property {null|string} itemName 事项名称
 * @property {CommonStatus} showStatus 是否显示
 * @property {null|string} value 值
 */
export interface CollateralHaircutItemDO {
  /**
   * 计算值
   */
  calculateValue: number | null;
  /**
   * 担保品折算率
   */
  collateralHaircut: number | null;
  /**
   * 编辑状态
   */
  editStatus: CommonStatus;
  /**
   * 启用状态
   */
  enableStatus: CommonStatus;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 事项id
   */
  itemId: null | string;
  /**
   * 事项名称
   */
  itemName: null | string;
  /**
   * 是否显示
   */
  showStatus: CommonStatus;
  /**
   * 值
   */
  value: null | string;

  [property: string]: any;
}

/**
 * createCollateralHaircutItemDO - 创建担保品折算率子事项字典DO
 * @function createCollateralHaircutItemDO
 * @returns {CollateralHaircutItemDO}
 */
export function createCollateralHaircutItemDO(): CollateralHaircutItemDO {
  return {
    calculateValue: null,
    collateralHaircut: null,
    editStatus: CommonStatus.ENABLE,
    enableStatus: CommonStatus.ENABLE,
    id: null,
    itemId: null,
    itemName: null,
    showStatus: CommonStatus.ENABLE,
    value: null,
  };
}

/**
 * CollateralHaircutCeilSettingDO - 担保品折算率上限设置
 * @interface CollateralHaircutCeilSettingDO
 * @property {number} collateralHaircut 折算率上限
 * @property {CommonStatus} enableStatus 启用状态
 * @property {number} id 主键id
 * @property {null|string} secLevel 证券评级
 * @property {null|string} secType 证券类型
 * @property {number} settingType 设置类型
 */
export interface CollateralHaircutCeilSettingDO {
  /**
   * 折算率上限
   */
  collateralHaircut: number | null;
  /**
   * 启用状态
   */
  enableStatus: CommonStatus;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 证券评级
   */
  secLevel: null | string;
  /**
   * 证券类型
   */
  secType: null | string;
  /**
   * 设置类型
   */
  settingType: number | null;

  [property: string]: any;
}

/**
 * createCollateralHaircutCeilSettingDO - 创建担保品折算率上限设置
 * @function createCollateralHaircutCeilSettingDO
 * @returns {CollateralHaircutCeilSettingDO}
 */
export function createCollateralHaircutCeilSettingDO(): CollateralHaircutCeilSettingDO {
  return {
    collateralHaircut: null,
    enableStatus: CommonStatus.ENABLE,
    id: null,
    secLevel: null,
    secType: null,
    settingType: null,
  };
}

/**
 * CollHaircutCeilSettingVO - 担保品折算率上限设置
 * @interface CollHaircutCeilSettingVO
 * @property {string[]} across 横坐标列表
 * @property {string[]} vertical 纵坐标列表
 */
export interface CollHaircutCeilSettingVO {
  /**
   * 横坐标列表
   */
  across: string[] | null;
  /**
   * 纵坐标列表
   */
  vertical: string[] | null;

  [property: string]: any;
}

/**
 * CollHaircutAdjustWarningVO - 担保品折算率调整预警VO
 * @interface CollHaircutAdjustWarningVO
 * @property {number} closePrice 最新收盘价
 * @property {number} collateralHaircut 系统折算率
 * @property {null|string} date 日期
 * @property {CommonStatus} enableStatus 担保品类型
 * @property {null|string} infoDescribe 预警描述
 * @property {null|string} level 系统评级
 * @property {null|string} popUp 弹出详情
 * @property {null|string} secCode 证券代码
 * @property {null|string} secName 证券名称
 * @property {number} totalAmount 总市值
 * @property {null|string} url url链接
 * @property {number} userCollateralHaircut 用户折算率
 * @property {null|string} userLevel 用户评级
 * @property {null|string} warningType 预警类型
 */
export interface CollHaircutAdjustWarningVO {
  /**
   * 最新收盘价
   */
  closePrice: number | null;
  /**
   * 系统折算率
   */
  collateralHaircut: number | null;
  /**
   * 日期
   */
  date: null | string;
  /**
   * 担保品类型
   */
  enableStatus: CommonStatus;
  /**
   * 预警描述
   */
  infoDescribe: null | string;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 弹出详情
   */
  popUp: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 总市值
   */
  totalAmount: number | null;
  /**
   * url链接
   */
  url: null | string;
  /**
   * 用户折算率
   */
  userCollateralHaircut: number | null;
  /**
   * 用户评级
   */
  userLevel: null | string;
  /**
   * 预警类型
   */
  warningType: null | string;

  [property: string]: any;
}

/**
 * CollHaircutManualAdjustDO - 担保品手动调整折算率DO
 * @interface CollHaircutManualAdjustDO
 * @property {null|string} adjustReason 调整原因
 * @property {number} collateralHaircut 手动调整的折算率
 * @property {null|string} createTime 创建时间
 * @property {null|string} createUser 创建用户
 * @property {null|string} date 日期
 * @property {CommonStatus} deleteStatus 删除状态
 * @property {CommonStatus} enableStatus 启用状态
 * @property {null|string} endDate 结束日期
 * @property {null|string} eventReference 事件简述
 * @property {number} id 主键id
 * @property {null|string} secCode 证券代码
 * @property {null|string} secName 证券名称
 * @property {null|string} startDate 开始日期
 * @property {null|string} targetUser 目标用户
 * @property {null|string} updateTime 更新时间
 * @property {null|string} updateUser 更新用户
 */
export interface CollHaircutManualAdjustDO {
  /**
   * 调整原因
   */
  adjustReason: null | string;
  /**
   * 手动调整的折算率
   */
  collateralHaircut: number | null;
  createTime: null | string;
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 日期
   */
  date: null | string;
  /**
   * 删除状态
   */
  deleteStatus: CommonStatus;
  /**
   * 启用状态
   */
  enableStatus: CommonStatus;
  /**
   * 结束日期
   */
  endDate: null | string;
  /**
   * 事件简述
   */
  eventReference: null | string;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 开始日期
   */
  startDate: null | string;
  /**
   * 目标用户
   */
  targetUser: null | string;
  updateTime: null | string;
  /**
   * 更新用户
   */
  updateUser: null | string;
  [property: string]: any;
}

// 新的接口，继承 CollHaircutManualAdjustDO 并添加新属性
export interface CollHaircutManualAdjustVO extends CollHaircutManualAdjustDO {
  /**
   * 目标用户名称
   */
  targetUserName: null | string;
  /**
   * 创建用户名称
   */
  createUserName: null | string;
  /**
   * 更新用户名称
   */
  updateUserName: null | string;
}

/**
 * CollateralHaircutModelDO
 * 担保品折算率模型配置表格
 */
export type CollateralHaircutModelDO = {
  /**
   * 删除标志
   */
  deleteStatus: null | string;
  /**
   * 启用状态
   */
  enableStatus: null | number;
  /**
   * 主备状态
   */
  mainStatus: null | number;
  /**
   * 担保品折算率模型id
   */
  modelId: null | string;
  /**
   * 担保品折算率模型名称
   */
  modelName: null | string;
  /**
   * 备注
   */
  remark: null | string;
  /**
   * 用户id
   */
  userId: null | string;
  [property: string]: any;
};

/**
 * HaircutModelCompareVO - 折算率模型结果历史比较
 */
export type HaircutModelCompareVO = {
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券类型
   */
  secType: number;
  /**
   * 新折算率
   */
  newHaircut: number;
  /**
   * 旧折算率
   */
  oldHaircut: number;
  /**
   * 新担保证券状态
   */
  newCollateralStatus: number;
  /**
   * 旧担保证券状态
   */
  oldCollateralStatus: number;
  /**
   * 新日期
   */
  newDate: null | string;
  /**
   * 旧日期
   */
  oldDate: null | string;
  [property: string]: any;
};
