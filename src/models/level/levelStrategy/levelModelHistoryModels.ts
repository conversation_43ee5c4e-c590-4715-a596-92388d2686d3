/**
 * LevelSrtModelConfigDO
 */
export type LevelSrtModelConfigDO = {
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 启用状态
   */
  enableStatus: number;
  /**
   * 矩阵id
   */
  matrixId: number | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型名称
   */
  modelName: null | string;
  /**
   * 模型备注
   */
  modelRemark: null | string;
  /**
   * 更数时间
   */
  updateTime: null | string;
  /**
   * 用户名称
   */
  userName: null | string;
  [property: string]: any;
};

/**
 * ModelSecLevelHistoryDO
 */
export type ModelSecLevelHistoryDO = {
  /**
   * 评级日期
   */
  date: null | string;
  /**
   * 评级
   */
  level: null | string;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 策略id
   */
  strategyId: number | null;
  /**
   * 策略名称
   */
  strategyName: null | string;
  /**
   * 用户id
   */
  userId: null | string;
  [property: string]: any;
};
