/**
 * LevelSrtModelLevelMappingDO
 */
export type LevelSrtModelLevelMappingDO = {
  createTime: null | string;
  /**
   * 创建用户id
   */
  createUser: null | string;
  /**
   * 主键ID
   */
  mappingId: number | null;
  /**
   * 映射对应评级
   */
  mappingLevel: null | string;
  /**
   * 策略评级模型id
   */
  modelId: number | null;
  /**
   * 模型评级
   */
  modelLevel: null | string;
  updateTime: null | string;
  /**
   * 更新用户id
   */
  updateUser: null | string;
  [property: string]: any;
};

/**
 * LevelSrtModelMappingDictDO
 */
export type LevelSrtModelMappingDictDO = {
  /**
   * 创建用户
   */
  createUser: null | string;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 映射评级选项
   */
  mappingLevel: null | string;
  /**
   * 映射评级顺序
   */
  mappingLevelOrder: number | null;
  /**
   * 策略评级模型id
   */
  modelId: number | null;
  /**
   * 更新用户
   */
  updateUser: null | string;
  [property: string]: any;
};

/**
 * ModelLevelPeerCompareVO
 */
export type ModelLevelPeerCompareVO = {
  /**
   * 最新收盘价（元）
   */
  closePrice: number | null;
  /**
   * 一级行业名称
   */
  industryName: null | string;
  /**
   * 二级行业名称
   */
  industryNameTwo: null | string;
  /**
   * 当前模型评级
   */
  modelLevel: null | string;
  /**
   * 同业券商集中度分组
   */
  peerConcentraGroup: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 最新总市值（亿元）
   */
  totalAmount: number | null;
  [property: string]: any;
};

/**
 * 评级映射配置 - 用于集中度分组等配置
 */
export interface LevelMappingConfig {
  /** 模型评级 */
  modelLevel: string;
  /** 目标集中度分组 */
  targetConcentraGroup: string | null;
}
