/**
 * @file: secLevelModels
 * @description:  证券评级相关模型
 * @date: 2024/9/9
 * @author: <PERSON> Ye
 */

/**
 * LevelChangeVO - 证券评级变化模型
 * @interface LevelChangeVO
 * @property {string} date - 评级变化日期
 * @property {number} levelChange - 评级变化级数
 * @property {LevelChangeDirection} levelChangeDirection - 评级调整类型
 * @property {string} newLevel - 新评级
 * @property {number} newManualAdjustId - 新手动调整记录ID
 * @property {string} newReason - 新评级理由
 * @property {string} oldLevel - 原评级
 * @property {number} oldManualAdjustId - 旧手动调整记录ID
 * @property {string} oldReason - 旧评级理由
 * @property {string} secCode - 证券代码
 * @property {string} secName - 证券名称
 */
export interface LevelChangeVO {
  /**
   * 评级变化日期
   */
  date: null | string;
  /**
   * 评级变化级数
   */
  levelChange: number | null;
  /**
   * 评级调整类型
   */
  levelChangeDirection?: LevelChangeDirection;
  /**
   * 新评级
   */
  newLevel: null | string;
  /**
   * 新手动调整记录ID
   */
  newManualAdjustId: number | null;
  /**
   * 新评级理由
   */
  newReason: null | string;
  /**
   * 原评级
   */
  oldLevel: null | string;
  /**
   * 旧手动调整记录ID
   */
  oldManualAdjustId: number | null;
  /**
   * 旧评级理由
   */
  oldReason: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;

  [property: string]: any;
}

/**
 * 评级调整类型
 */
export enum LevelChangeDirection {
  Down = 'down',
  Up = 'up',
}
