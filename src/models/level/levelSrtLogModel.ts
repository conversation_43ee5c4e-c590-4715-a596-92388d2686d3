/**
 * @file: levelSrtLogModel.ts
 * @description: 评级策略日志相关数据模型
 * @date: 2025/07/13
 * @author: <PERSON> Ye
 */

import { LevelConditionDO, LevelConditionOrderDO } from '@/models/level/levelSrtModel';
import { LevelStrategyAdjustHisLogTypeEnum } from '@/enums/levelStrategy/levelStrategyEnum';

/**
 * 评级策略基本信息调整历史日志DTO
 */
export interface LevelStrategyAdjustHisLogDTO {
  /**
   * 调整日志id
   */
  logId: number | null;

  /**
   * 评级策略id
   */
  strategyId: number | null;

  /**
   * 评级策略名称
   */
  strategyName: string | null;

  /**
   * 旧的评级计算条件列表
   */
  oldLevelConditionList: LevelStrategyConditionAdjustHisLogDO[] | null;
  /**
   * 新的评级计算条件列表
   */
  newLevelConditionList: LevelStrategyConditionAdjustHisLogDO[] | null;
  /**
   * 旧的评级计算条件顺序列表
   */
  oldLevelConditionOrderList: LevelStrategyConditionOrderAdjustHisLogDO[] | null;
  /**
   * 新的评级计算条件顺序列表
   */
  newLevelConditionOrderList: LevelStrategyConditionOrderAdjustHisLogDO[] | null;
}

/**
 * 评级策略条件修改日志表
 */
export interface LevelStrategyConditionAdjustHisLogDO extends LevelConditionDO {
  /**
   * 调整日志id
   */
  logId: number | null;

  /**
   * 日志记录类型
   */
  logType: LevelStrategyAdjustHisLogTypeEnum | null;
}

/**
 * 评级策略条件顺序调整日志
 */
export interface LevelStrategyConditionOrderAdjustHisLogDO extends LevelConditionOrderDO {
  /**
   * 调整日志id
   */
  logId: number | null;

  /**
   * 日志记录类型
   */
  logType: LevelStrategyAdjustHisLogTypeEnum | null;
}

/**
 * 创建 LevelStrategyAdjustHisLogDTO 的初始化工厂函数
 *
 * 用于创建评级策略调整历史日志DTO对象，支持传入部分属性覆盖默认值。
 *
 * @param overrides 可选的覆盖属性，会覆盖默认值
 * @returns 初始化的 LevelStrategyAdjustHisLogDTO 对象
 */
export function createLevelStrategyAdjustHisLogDTO(
  overrides: Partial<LevelStrategyAdjustHisLogDTO> = {}
): LevelStrategyAdjustHisLogDTO {
  const defaultValues: LevelStrategyAdjustHisLogDTO = {
    logId: null,
    strategyId: null,
    strategyName: null,
    oldLevelConditionList: null,
    newLevelConditionList: null,
    oldLevelConditionOrderList: null,
    newLevelConditionOrderList: null,
  };

  return {
    ...defaultValues,
    ...overrides,
  };
}
