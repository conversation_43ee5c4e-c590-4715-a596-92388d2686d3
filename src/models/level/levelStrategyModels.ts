/**
 * @description: 评级策略多模型相关类型定义
 *
 */

import { TypeCountVO } from '@/models/common/utilModels';

/**
 * LevelStrategyModelConfig
 */
export type LevelStrategyModelConfig = {
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 启用状态
   */
  enableStatus: number;
  /**
   * 是否有编辑权限 (1: 无法修改, 0: 可以修改)
   */
  ifHasEditPermission?: number;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 模型名称
   */
  modelName: null | string;
  /**
   * 模型备注
   */
  modelRemark: null | string;
  /**
   * 模型计算时间
   */
  calculateTime: null | string;
  /**
   * 更数时间
   */
  updateTime: null | string;
  [property: string]: any;
};

/**
 * TypeCountListVO
 */
export type TypeCountListVO = {
  /**
   * 类型
   */
  type: null | string;
  /**
   * 类型统计列表
   */
  typeCountList: TypeCountVO[] | null;
  [property: string]: any;
};

/**
 * ModelLevelOverallDiffVO
 */
export type ModelLevelOverallDiffVO = {
  /**
   * 评级
   */
  level: null | string;
  /**
   * 当前评级
   */
  currentLevel: null | string;
  /**
   * 比较模型存在，但是基准模型中不存在的证券的分布信息
   */
  extraSecCodeDistribution: TypeCountVO[] | null;
  /**
   * 基准模型存在，但是比较模型中不存在的证券的分布信息
   */
  missingSecCodeDistribution: TypeCountVO[] | null;
  [property: string]: any;
};
