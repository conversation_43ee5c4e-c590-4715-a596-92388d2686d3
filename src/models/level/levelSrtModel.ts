/**
 * @file: levelSrtModels
 * @description: 评级策略相关数据模型
 * @date: 2024/8/28
 * @author: <PERSON> Ye
 */

import { CalculateDirectionType, CommonStatus } from '@/enums/baseEnum';
import { RelationEnum } from '@/models/common/baseResponse';

/**
 * UserLevelStrategyDO - 用户评级策略数据模型
 * @interface UserLevelStrategyDO
 * @property {number} id - 主键ID
 * @property {CommonStatus} isHidden - 是否隐藏
 * @property {null|string} level - 策略等级
 * @property {number} policyType - 策略类型
 * @property {number} strategyId - 策略ID
 * @property {UserLevelStrategyDO[]} strategyList - 策略列表
 * @property {number} strategyOrder - 策略顺序
 * @property {null|string} userId - 用户ID
 */
export interface UserLevelStrategyDO {
  /**
   * 主键ID
   */
  id?: number | null;
  /**
   * 是否隐藏
   */
  isHidden?: CommonStatus | null;
  /**
   * 策略等级
   */
  level?: null | string;
  policyType?: number | null;
  /**
   * 策略ID
   */
  strategyId?: number | null;
  strategyList?: UserLevelStrategyDO[] | null;
  /**
   * 策略顺序
   */
  strategyOrder?: number | null;
  /**
   * 用户ID
   */
  userId?: null | string;

  [property: string]: any;
}

/**
 * LevelSrtProgressVO - 评级进度数据模型
 * @interface LevelSrtProgressVO
 * @property {number} leveled - 已评级数量
 * @property {number} total - 全部数量
 */
export interface LevelSrtProgressVO {
  /**
   * 已评级数量
   */
  leveled: number;
  /**
   * 全部数量
   */
  total: number;

  [property: string]: any;
}

/**
 * UserLevelSrtBackupConfig 策略备份数据模型
 * @property {string} backupDescription - 备份描述
 * @property {string} backupName - 备份名称
 * @property {string} createTime - 创建时间
 * @property {CommonStatus} deleteStatus - 删除状态
 * @property {CommonStatus} enableStatus - 启用状态
 * @property {number} id - 主键ID
 * @property {string} updateTime - 更新时间
 * @property {string} userId - 用户ID
 *
 */
export interface UserLevelSrtBackupConfig {
  /**
   * 备份描述
   */
  backupDescription?: null | string;
  /**
   * 备份名称
   */
  backupName?: null | string;
  createTime?: null | string;
  /**
   * 删除状态
   */
  deleteStatus?: CommonStatus;
  /**
   * 启用状态
   */
  enableStatus?: CommonStatus;
  id?: number | null;
  updateTime?: null | string;
  /**
   * 用户id
   */
  userId?: null | string;

  [property: string]: any;
}

/**
 * LevelSrtConditionMarketOptionVO - 条件市场选项数据模型
 * @interface LevelSrtConditionMarketOptionVO
 * @property {null|string} optionField - 可选项对应字段
 * @property {null|string} optionName - 可选项名称
 * @property {null|string} optionUnit - 可选项对应单位
 */
export interface LevelSrtConditionMarketOptionVO {
  /**
   * 可选项对应字段
   */
  optionField: string;
  /**
   * 可选项名称
   */
  optionName: string;
  /**
   * 可选项对应单位
   */
  optionUnit: string;

  [property: string]: any;
}

/**
 * StrategyTypeDO - 策略类型数据模型
 * @interface StrategyTypeDO
 * @property {number} calculateOrder - 计算顺序
 * @property {number} strategyId - 策略id
 * @property {number} strategyCategory - 策略分类
 * @property {null|string} strategyType - 策略类型名称
 * @property {CommonStatus} enableStatus - 启用状态
 */
export type StrategyTypeDO = {
  /**
   * 计算顺序
   */
  calculateOrder: number | null;
  /**
   * 策略id
   */
  strategyId: number | null;
  strategyCategory: number | null;
  /**
   * 策略类型名称
   */
  strategyType: null | string;
  enableStatus: CommonStatus | null;
  [property: string]: any;
};

/**
 * LevelSrtDTO
 */
export type LevelSrtDTO = {
  /**
   * 策略选择的评级
   */
  chosenLevel: null | string;
  /**
   * 证券数量
   */
  count: number | null;
  /**
   * 策略分组名称
   */
  groupName: null | string;
  /**
   * 策略ID
   */
  id: number | null;
  /**
   * 策略评级
   */
  level: null | string;
  /**
   * 策略评级条件列表
   */
  levelConditionList: LevelConditionDO[] | null;
  /**
   * 策略评级条件排序列表
   */
  levelConditionOrderList: LevelConditionOrderDO[] | null;
  /**
   * 策略名称
   */
  name: null | string;
  /**
   * 策略类型
   */
  policyType: number | null;
  /**
   * 策略计算顺序
   */
  strategyOrder: number | null;
  /**
   * 目标评级
   */
  targetLevel: null | string;
  /**
   * 策略创建用户ID
   */
  userId: null | string;
  /**
   * 策略创建用户
   */
  userName: null | string;
  /**
   * 策略版本号id
   */
  versionId: number | null;
  [property: string]: any;
};

/**
 * 评级策略计算条件实体类
 *
 * 这个类型定义了评级策略中使用的各种计算条件，
 * 支持多种条件类型如行情数据、财务指标、同业券商调整等。
 *
 * @see LevelStrategyConditionTypeEnum
 * <AUTHOR> Ye
 * @version 2.0.0
 * @date 2025-07-12
 */
export type LevelConditionDO = {
  /**
   * 主键id
   */
  id: number | null;

  /**
   * 计算类型id
   * @see LevelStrategyConditionTypeEnum
   */
  calculationId: number | null;

  /**
   * 左边的值
   */
  leftValue: number | null;

  /**
   * 右边的值
   */
  rightValue: number | null;

  /**
   * 条件文字描述
   */
  nameText: null | string;

  /**
   * 策略id
   */
  strategyId: number | null;

  /**
   * 数据值
   */
  itemValue: number | null;
  /**
   * 数据值2
   */
  itemValue2: number | null;

  /**
   * 数据值比较方向
   * @example '大于'
   */
  direction: CalculateDirectionType | null;
  /**
   * 数据值比较方向2
   * @example '大于'
   */
  direction2: CalculateDirectionType | null;

  /**
   * 评级条件计算顺序
   * @example 1
   */
  orders: null | string | number;

  /**
   * 条件顺序名称
   * @example "评分3"
   */
  orderName: null | string;

  /**
   * 数据项类型
   */
  itemType: null | string;
  /**
   * 数据项类型2
   */
  itemType2: null | string;
  /**
   * 数据项类型3
   */
  itemType3: null | string;
  /**
   * 计算类型
   */
  calculateStatus: number | null;
  /**
   * 1.行情时间范围；2.同业调整的时间范围
   */
  days: number | null;

  [property: string]: any;
};

/**
 * 创建 LevelConditionDO 的初始化工厂函数
 * @param overrides 可选的覆盖属性，会覆盖默认值
 * @returns 初始化的 LevelConditionDO 对象
 */
export function createLevelConditionDO(
  overrides: Partial<LevelConditionDO> = {}
): LevelConditionDO {
  const defaultValues: LevelConditionDO = {
    /**
     * 主键id - 默认为null（通常由后端生成）
     */
    id: null,

    /**
     * 计算类型id - 默认为null
     * @see LevelStrategyConditionTypeEnum
     */
    calculationId: null,

    /**
     * 左边的值 - 默认为null（用于区间条件）
     */
    leftValue: null,

    /**
     * 右边的值 - 默认为null（用于区间条件）
     */
    rightValue: null,

    /**
     * 条件文字描述 - 默认为null
     */
    nameText: null,

    /**
     * 策略id - 默认为null
     */
    strategyId: null,

    /**
     * 数据值 - 默认为null
     */
    itemValue: null,

    /**
     * 数据值2 - 默认为null
     */
    itemValue2: null,

    /**
     * 数据值比较方向 - 默认为null
     */
    direction: null,

    /**
     * 数据值比较方向2 - 默认为null
     */
    direction2: null,

    /**
     * 评级条件计算顺序 - 默认为null
     */
    orders: null,

    /**
     * 条件顺序名称 - 默认为null
     * @example "评分3"
     */
    orderName: null,

    /**
     * 数据项类型 - 默认为null
     */
    itemType: null,

    /**
     * 数据项类型2 - 默认为null
     */
    itemType2: null,

    /**
     * 数据项类型3 - 默认为null
     */
    itemType3: null,

    /**
     * 计算类型 - 默认为null
     */
    calculateStatus: null,

    /**
     * 时间范围（天数）- 默认为null
     * 1.行情时间范围；2.同业调整的时间范围
     */
    days: null,
  };

  return {
    ...defaultValues, // 展开 defaultValues 对象的所有属性
    ...overrides, // 展开 overrides 对象的所有属性（会覆盖同名属性）
  };
}

/**
 * LevelConditionOrderDO
 */
export type LevelConditionOrderDO = {
  /**
   * 计算条件列表
   */
  conditionList: string[] | null;
  /**
   * 计算条件列表字符串
   * @example '3,4,5'
   */
  conditionListStr: null | string;
  /**
   * 计算条件唯一标志
   * @example 'A'
   */
  conditions: null | string;
  /**
   * 主键ID
   */
  id: number | null;
  /**
   * 关系符号
   * @example 'and'
   */
  relation: Relation;
  /**
   * 评级策略ID
   */
  strategyId: number | null;
  /**
   * 创建用户
   */
  userId: null | string;
  [property: string]: any;
};

/**
 * 关系符号
 */
export enum Relation {
  And = 'and',
  Or = 'or',
}

/**
 * 创建 LevelConditionOrderDO 的初始化工厂函数
 * @param overrides 可选的覆盖属性，会覆盖默认值
 * @returns 初始化的 LevelConditionOrderDO 对象
 *
 * @example
 * // 创建默认对象
 * const defaultOrder = createLevelConditionOrderDO();
 *
 * // 创建带有自定义值的对象
 * const customOrder = createLevelConditionOrderDO({
 *   strategyId: 123,
 *   relation: Relation.Or,
 *   conditionList: ['condition1', 'condition2']
 * });
 */
export function createLevelConditionOrderDO(
  overrides: Partial<LevelConditionOrderDO> = {}
): LevelConditionOrderDO {
  const defaultValues: LevelConditionOrderDO = {
    /**
     * 计算条件列表 - 默认为空数组
     */
    conditionList: [],
    /**
     * 计算条件列表字符串 - 默认为null
     */
    conditionListStr: null,
    /**
     * 计算条件唯一标志 - 默认为null
     */
    conditions: null,
    /**
     * 主键ID - 默认为null（通常由后端生成）
     */
    id: null,
    /**
     * 关系符号 - 默认为 'and'
     */
    relation: Relation.And,
    /**
     * 评级策略ID - 默认为null
     */
    strategyId: null,
    /**
     * 创建用户 - 默认为null
     */
    userId: null,
  };

  return {
    ...defaultValues, // 展开 defaultValues 对象的所有属性
    ...overrides, // 展开 overrides 对象的所有属性（会覆盖同名属性）
  };
}

/**
 * LevelStrategyDTO
 */
export interface LevelStrategyDTO {
  /**
   * 策略选择的评级
   */
  chosenLevel: null | string;
  /**
   * 策略对应的证券数量
   */
  count: number | null;
  createTime: null | string;
  /**
   * 策略分组名称
   */
  groupName: null | string;
  /**
   * 主键id
   */
  id: number | null;
  /**
   * 策略评级
   * 策略评级(当前策略被引用的评级)
   */
  level: null | string;
  /**
   * 策略评级条件列表
   */
  levelConditionList: LevelConditionDO[] | null;
  /**
   * 策略评级条件排序列表
   */
  levelConditionOrderList: LevelConditionOrderDO[] | null;
  /**
   * 模型id
   */
  modelId: number | null;
  /**
   * 引用模型名称
   */
  modelName: null | string;
  /**
   * 策略名称
   */
  name: null | string;
  /**
   * 评级策略类型
   */
  policyType: number | null;
  /**
   * 特殊策略类型：0.非特殊策略 1.矩阵策略
   */
  specialType: number | null;
  /**
   * 策略计算顺序
   */
  strategyOrder: number | null;
  /**
   * 后置策略的目标评级：1.指定评级：如BB；2.上调下调：如-2
   */
  targetLevel: null | string;
  updateTime: null | string;
  /**
   * 策略创建用户
   */
  userId: null | string;
  /**
   * 用户所选择此策略所在评级
   */
  userLevel: null | string;
  /**
   * 策略创建用户名称
   */
  userName: null | string;
  /**
   * 策略版本号
   */
  versionId: number | null;
  [property: string]: any;
}

/**
 * 创建 LevelStrategyDTO 的初始化工厂函数
 * @param overrides 可选的覆盖属性，会覆盖默认值
 * @returns 初始化的 LevelStrategyDTO 对象
 */
export function createLevelStrategyDTO(
  overrides: Partial<LevelStrategyDTO> = {}
): LevelStrategyDTO {
  const defaultValues: LevelStrategyDTO = {
    chosenLevel: null,
    count: null,
    createTime: null,
    groupName: null,
    id: null,
    level: null,
    levelConditionList: [],
    levelConditionOrderList: [],
    modelId: null,
    modelName: null,
    name: null,
    policyType: null, // 默认策略类型为1
    specialType: null, // 默认非特殊策略
    strategyOrder: null,
    targetLevel: null,
    updateTime: null,
    userId: null,
    userLevel: null,
    userName: null,
    versionId: null,
  };

  return {
    ...defaultValues, // 展开 defaultValues 对象的所有属性
    ...overrides, // 展开 overrides 对象的所有属性（会覆盖同名属性）
  };
}

/**
 * 创建空的 LevelStrategyDTO 对象（所有字段为 null）
 * @returns 空的 LevelStrategyDTO 对象
 */
export function createEmptyLevelStrategyDTO(): LevelStrategyDTO {
  return {
    chosenLevel: null,
    count: null,
    createTime: null,
    groupName: null,
    id: null,
    level: null,
    levelConditionList: null,
    levelConditionOrderList: null,
    modelId: null,
    modelName: null,
    name: null,
    policyType: null,
    specialType: null,
    strategyOrder: null,
    targetLevel: null,
    updateTime: null,
    userId: null,
    userLevel: null,
    userName: null,
    versionId: null,
  };
}

/**
 * 策略条件组合数据模型
 *
 * 用于定义评级策略中的条件组合结构，支持多个条件项的逻辑组合。
 * 每个条件组包含一个条件标识符、逻辑关系和多个具体的条件项。
 *
 * @interface StrategyConditionCombo
 * @see ConditionItem
 * @see RelationEnum
 */
export interface StrategyConditionCombo {
  /**
   * 条件标识符
   * @description 用于标识条件组的唯一标识，如 'A', 'B', 'C' 等
   * @example 'A'
   */
  condition: string;

  /**
   * 逻辑关系
   * @description 条件组合内各条件项之间的逻辑关系，无论组合中存在多少个条件项，
   * 都只能使用 'and' 或 'or' 两种逻辑关系进行组合
   * @see RelationEnum
   */
  relation: RelationEnum;

  /**
   * 条件项列表
   * @description 包含在此条件组中的具体条件项数组
   */
  conditionItems: ConditionItem[];

  /**
   * 是否显示操作按钮
   * @description 控制UI界面中是否显示添加、删除等操作按钮
   */
  showActionButtons: boolean;
}

/**
 * 条件项数据模型
 *
 * 定义策略条件组合中的单个条件项结构，包含条件的逻辑关系、
 * 条件值和条件名称等基本信息。
 *
 * @interface ConditionItem
 * @see RelationEnum
 */
export interface ConditionItem {
  /**
   * 条件间逻辑关系
   * @description 当前条件项与其他条件项之间的逻辑关系，首个条件项通常为 null
   * @see RelationEnum
   */
  relation: RelationEnum | null;

  /**
   * 条件值
   * @description 条件的具体数值或标识符，对应后端的 orders 字段
   * @example '1'
   * @see LevelConditionDO.orders
   */
  value: string | null;

  /**
   * 条件名称
   * @description 条件的显示名称，用于UI展示，对应后端的 orderName 字段
   * @example '上市时间1'
   * @see LevelConditionDO.orderName
   */
  name: string;
}
