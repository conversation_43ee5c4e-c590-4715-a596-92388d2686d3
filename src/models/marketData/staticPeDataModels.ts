/**
 * StockStaticPeVO
 */
export type StockStaticPeVO = {
  /**
   * 是否交易所规则超限：0是，1否
   */
  ifExchangeRuleExceeded: number;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 静态市盈率
   */
  staticPe: number | null;
  /**
   * 交易日期
   */
  tradingDate: null | string;
  [property: string]: any;
};
/**
 * ExchangeStaticPeSecAdjustHisVO
 */
export type ExchangeStaticPeSecAdjustHisVO = {
  /**
   * 变动类型：下调、恢复
   */
  adjustType: null | string;
  /**
   * 交易日期
   */
  date: null | string;
  /**
   * 上周最后一个交易日静态市盈率
   */
  lastWeekLastTradingDayStaticPe: number | null;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 交易日期对应静态市盈率
   */
  staticPe: number | null;
  [property: string]: any;
};
