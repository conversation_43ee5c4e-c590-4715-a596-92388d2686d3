/**
 * FinancingRestrictedStockVO
 */
export type FinancingRestrictedStockVO = {
  /**
   * 是否融资受限
   */
  ifFinancingRestricted: number;
  /**
   * 是否融资标的
   */
  ifFinancingTarget: number;
  /**
   * 前一交易日担保物比例(%)
   */
  lastTradeDayAssureRatio: number | null;
  /**
   * 前一交易日静态市盈率
   */
  lastTradeDayStaticPe: number | null;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 交易日期
   */
  tradingDate: null | string;
  [property: string]: any;
};
/**
 * FinancingRestrictedStockAdjustHisVO
 */
export type FinancingRestrictedStockAdjustHisVO = {
  /**
   * 调整类型
   */
  adjustType: null | string;
  /**
   * 调整前担保物比例（%）
   */
  beforeAdjustCollateralRatio: number | null;
  /**
   * 调整前静态市盈率
   */
  beforeAdjustStaticPe: number | null;
  id: number | null;
  /**
   * 是否融资受限
   */
  ifFinancingRestricted: number;
  /**
   * 是否融资标的
   */
  ifFinancingTarget: number;
  /**
   * 前一交易日担保物比例（%）
   */
  lastTradingDateCollateralRatio: number | null;
  /**
   * 前一交易日静态市盈率
   */
  lastTradingDateStaticPe: number | null;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 交易日期
   */
  tradingDate: null | string;
  [property: string]: any;
};
