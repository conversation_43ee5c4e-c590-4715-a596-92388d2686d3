/**
 * ExchangeCollateralSecVO
 */
export type ExchangeCollateralSecVO = {
  /**
   * 交易所折算率
   */
  exchangeCollateralHaircut: number | null;
  /**
   * 上周最后一个交易日静态市盈率
   */
  lastWeekLastTradingDayStaticPe: number | null;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 发行制度
   */
  publishType: null | string;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券品种
   */
  secType: number;
  /**
   * 标准依据
   */
  standardBasis: null | string;
  /**
   * 交易日期
   */
  tradingDate: null | string;
  [property: string]: any;
};
/**
 * ExchangeCollateralAdjustVO
 */
export type ExchangeCollateralAdjustVO = {
  /**
   * 调整原因
   */
  adjustReason: null | string;
  /**
   * 调整类型：调入/调出/调高/调低/调0
   */
  adjustType: null | string;
  /**
   * 调整日期
   */
  date: null | string;
  /**
   * 交易市场
   */
  market: null | string;
  /**
   * 发行制度
   */
  publishType: null | string;
  /**
   * 调整后折算率
   */
  rateAfter: number | null;
  /**
   * 调整前折算率
   */
  rateBefore: number | null;
  /**
   * 证券分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券品种
   */
  secType: number;
  [property: string]: any;
};
