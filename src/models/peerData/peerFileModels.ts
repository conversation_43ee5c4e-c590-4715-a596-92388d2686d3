/**
 * PeerAdjustCountVO
 */
export type PeerAdjustCountVO = {
  /**
   * 类型数量
   */
  count: number | null;
  /**
   * 同业券商调整日期
   */
  date: null | string;
  /**
   * 类型名称
   */
  typeName: null | string;
  [property: string]: any;
};

/**
 * StackedLineChartVO
 */
export type StackedLineChartVO = {
  /**
   * 系列数据
   */
  seriesData: Series[] | null;
  /**
   * X 轴标签列表
   */
  xAxisLabels: string[] | null;
  [property: string]: any;
};

/**
 * 内部类，用于表示单个系列的数据
 *
 * Series
 */
export type Series = {
  /**
   * 系列数据
   */
  data: { [key: string]: any }[] | null;
  /**
   * 系列名称
   */
  name: null | string;
  [property: string]: any;
};
