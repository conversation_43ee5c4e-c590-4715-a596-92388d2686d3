/**
 * 同业个券信息相关枚举
 */

/**
 * PeerSecDataModel
 */

export interface PeerSecDataModel {
  /**
   * 长江证券
   */
  cjzq: null | string;
  /**
   * 日期
   */
  date: null | string;
  /**
   * 东方财富
   */
  dczq: null | string;
  /**
   * 东方证券
   */
  dfzq: null | string;
  /**
   * 光大证券
   */
  gdzq: null | string;
  /**
   * 广发证券
   */
  gfzq: null | string;
  /**
   * 国泰君安
   */
  gtja: null | string;
  /**
   * 国信证券
   */
  gxzq: null | string;
  /**
   * 海通证券
   */
  haitongzq: null | string;
  /**
   * 华泰证券
   */
  htzq: null | string;
  id: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券品种
   */
  secType: number;
  /**
   * 申万宏源
   */
  swhy: null | string;
  /**
   * 天风证券
   */
  tfzq: null | string;
  /**
   * 西部证券
   */
  xbzq: null | string;
  /**
   * 兴业证券
   */
  xyzq: null | string;
  /**
   * 银河证券
   */
  zgyh: null | string;
  /**
   * 浙商证券
   */
  zheshangzq: null | string;
  /**
   * 中金公司
   */
  zjgs: null | string;
  /**
   * 招商证券
   */
  zszq: null | string;
  /**
   * 中泰证券
   */
  ztzq: null | string;
  /**
   * 中信建投
   */
  zxjt: null | string;
  /**
   * 中信证券
   */
  zxzq: null | string;
  [property: string]: any;
}

/**
 * PeerSecAdjustVO
 */
export type PeerSecAdjustVO = {
  /**
   * 调整比例
   */
  adjustRatio: null | string;
  /**
   * 调整后
   */
  afterAdjust: null | string;
  /**
   * 调整前
   */
  beforeAdjust: null | string;
  /**
   * 券商名称
   */
  source: null | string;
  [property: string]: any;
};
