/**
 * 每日简报/券商维度相关模型
 */
import { TypeCountVO } from '@/models/common/utilModels';

/**
 * PeerAdjustDailyReportVo
 */
export type PeerAdjustDailyReportVo = {
  /**
   * 日期
   */
  date?: null | string;
  /**
   * 调整详情
   */
  infoDescribe?: null | string;
  /**
   * 系统评级
   */
  level?: null | string;
  /**
   * 新值
   */
  newValue?: null | string;
  /**
   * 旧值
   */
  oldValue?: null | string;
  /**
   * 同业集中度范围
   */
  peerConcentrationRange?: null | string;
  /**
   * 同业折算率范围
   */
  peerHaircutRange?: null | string;
  /**
   * 同业评级范围
   */
  peerLevelRange?: null | string;
  /**
   * 同业评级
   */
  secCategory?: null | string;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 证券名称
   */
  secName?: null | string;
  /**
   * 券商中文名称
   */
  source?: null | string;
  [property: string]: any;
};

/**
 * ChartAnalysisVO
 */
export type ChartAnalysisVO = {
  /**
   * 最新收盘价分析
   */
  closes: TypeCountVO[] | null;
  /**
   * 行业分析
   */
  industry: TypeCountVO[] | null;
  /**
   * 评级分析
   */
  level: TypeCountVO[] | null;
  /**
   * 矩阵分析
   */
  matrix: MatrixInfoVO[] | null;
  /**
   * 同业券商旧评级分析
   */
  oldPeerLevel: TypeCountVO[] | null;
  /**
   * 同业券商评级分析
   */
  peerLevel: TypeCountVO[] | null;
  /**
   * 地区分析
   */
  region: TypeCountVO[] | null;
  /**
   * 评分分析
   */
  score: TypeCountVO[] | null;
  /**
   * 证券类型分析
   */
  stockType: TypeCountVO[] | null;
  /**
   * 市值分析
   */
  ttmAnalysis: TypeCountVO[] | null;
  [property: string]: any;
};

/**
 * MatrixInfoVO
 */
export type MatrixInfoVO = {
  /**
   * 矩阵横纵坐标对应股票数量
   */
  list: MatrixCellSecCountVO[] | null;
  /**
   * 矩阵可选日期字符串
   */
  matrixDateStr: null | string;
  /**
   * 矩阵描述
   */
  matrixDescription: null | string;
  /**
   * 矩阵id
   */
  matrixId: number | null;
  /**
   * 矩阵股票数量
   */
  number: number | null;
  /**
   * 矩阵名称
   */
  type: null | string;
  /**
   * 创建用户
   */
  userName: null | string;
  [property: string]: any;
};

/**
 * MatrixCellVO
 *
 * MatrixCellSecCountVO
 */
export type MatrixCellSecCountVO = {
  /**
   * 横坐标
   */
  across: null | string;
  /**
   * 股票数量
   */
  count: number | null;
  /**
   * 是否包含当前股票
   */
  flag: number | null;
  /**
   * 标签组合数量
   */
  labels: number | null;
  /**
   * 纵坐标
   */
  vertical: null | string;
  [property: string]: any;
};

/**
 * PeerSecAdjustHisDO
 */
export type PeerSecAdjustHisDO = {
  /**
   * 调整类型
   */
  adjustType: null | string;
  /**
   * 调整日期
   */
  date: null | string;
  /**
   * 调整描述
   */
  infoDescribe: null | string;
  /**
   * 新值
   */
  newValue: null | string;
  /**
   * 旧值
   */
  oldValue: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 券商中文名
   */
  source: null | string;
  [property: string]: any;
};

/**
 * PeerDataOverView
 */
export type PeerDataOverView = {
  /**
   * 是否担保品
   */
  collateral: number;
  /**
   * 折算率
   */
  collateralHaircut: number | null;
  /**
   * 集中度区间
   */
  concentration: null | string;
  date: null | string;
  /**
   * 融资保证金比例
   */
  financeMarginRatio: number | null;
  /**
   * 是否融资标的
   */
  financingTarget: number;
  /**
   * 塔金评级
   */
  level: null | string;
  /**
   * 券商分类
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券类型
   */
  secType: number;
  /**
   * 融券保证金比例
   */
  shortMarginRatio: number | null;
  /**
   * 是否融券标的
   */
  shortSellTarget: number;
  [property: string]: any;
};

/**
 * PeerFileAdjustInfoVO
 */
export type PeerFileAdjustInfoVO = {
  /**
   * 券商当前折算率
   */
  collateralHaircut: number | null;
  /**
   * 券商当前集中度
   */
  concentration: null | string;
  /**
   * 一级行业名称
   */
  industryName: null | string;
  /**
   * 同业券商档案调整描述
   */
  infoDescribe: null | string;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 调整前值
   */
  newValue: null | string;
  /**
   * 调整后值
   */
  oldValue: null | string;
  /**
   * 同业券商集中度区间
   */
  peerConcentrationRange: null | string;
  /**
   * 同业折算率区间
   */
  peerHaircutRange: null | string;
  /**
   * 同业券商评级区间
   */
  peerLevelRange: null | string;
  /**
   * 同业券商评级
   */
  secCategory: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 证券类型
   */
  secType: number;
  [property: string]: any;
};

/**
 * PeerSecuritiesSetting
 */
export type PeerSecuritiesSetting = {
  /**
   * 中文名称
   */
  cnName: null | string;
  /**
   * 是否有担保状态
   */
  dbStatus: number | null;
  /**
   * 简称
   */
  enName: null | string;
  id: number | null;
  /**
   * 是否在同业统计表格中展示
   */
  ifColumnShow: number;
  /**
   * 集中度是否采集
   */
  jzdCj: number | null;
  /**
   * 集中度分类
   */
  jzdCls: number | null;
  /**
   * 集中度是否有全量
   */
  jzdFull: number | null;
  /**
   * 集中度是否有调整
   */
  jzdTz: number | null;
  /**
   * 集中度控制指标链接
   */
  jzdUrl: null | string;
  /**
   * 备注
   */
  mark1: null | string;
  /**
   * 1:正常；2：维护中；3：开发中
   */
  mark2: null | string;
  /**
   * 需要更新market字段的表
   */
  marketFieldUpdateTable: null | string;
  /**
   * 券商排名
   */
  ranking: number | null;
  remark: null | string;
  updateTime: null | string;
  url: null | string;
  /**
   * 折算率是否全量
   */
  zslFull: number | null;
  [property: string]: any;
};

/**
 * PeerAdjustInfoVO
 */
export type PeerAdjustInfoVO = {
  /**
   * 同业集中度区间
   */
  concentrationRange: null | string;
  /**
   * 证券一级行业
   */
  industryName: null | string;
  /**
   * 标签
   */
  labels: null | string;
  /**
   * 塔金评级
   */
  level: null | string;
  /**
   * 同业评级区间
   */
  levelRange: null | string;
  /**
   * 同业调整后折算率
   */
  nowHaircut: null | string;
  /**
   * 同业现融资保证金比例
   */
  nowMarginRatio: null | string;
  /**
   * 同业调整后评级
   */
  nowPeerLevel: null | string;
  /**
   * 同业原折算率
   */
  oldHaircut: null | string;
  /**
   * 同业原融资保证金比例
   */
  oldMarginRatio: null | string;
  /**
   * 同业原评级
   */
  oldPeerLevel: null | string;
  /**
   * 同业券商评级
   */
  peerLevel: null | string;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 同业折算率区间
   */
  zslRange: null | string;
  /**
   * 调整后折算率是否符合交易所监管规则
   */
  ifAdjustHaircutSatisfyExchangeRule?: boolean | null;
  [property: string]: any;
};
