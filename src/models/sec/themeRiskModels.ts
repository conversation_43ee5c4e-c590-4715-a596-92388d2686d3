/**
 * 主题风险股票池相关模型
 */

import { CommonStatus } from '@/enums/baseEnum';

/**
 * ThemeRiskSecPoolVO
 * @interface ThemeRiskSecPoolVO
 * @property {string} actualController 实控人
 * @property {number} curClosePrice 当前股价
 * @property {string} disclosureTime 爆料时间
 * @property {number} downPct 至今涨跌幅
 * @property {number} id 主键
 * @property {string} level 当前系统评级
 * @property {number} preClose 爆前股价
 * @property {string} property 属性
 * @property {string} remark 备注
 * @property {string} secCode 股票代码
 * @property {string} secName 股票简称
 * @property {number} themeId 主题风险字典ID
 * @property {number} totalAmount 当前市值
 */
export interface ThemeRiskSecPoolVO {
  /**
   * 实控人
   */
  actualController?: null | string;
  /**
   * 当前股价
   */
  curClosePrice?: number | null;
  /**
   * 爆料时间
   */
  disclosureTime?: null | string;
  /**
   * 至今涨跌幅
   */
  downPct?: number | null;
  id?: number | null;
  /**
   * 当前系统评级
   */
  level?: null | string;
  /**
   * 爆前股价
   */
  preClose?: number | null;
  /**
   * 属性
   */
  property?: null | string;
  /**
   * 备注
   */
  remark?: null | string;
  /**
   * 股票代码
   */
  secCode?: null | string;
  /**
   * 股票简称
   */
  secName?: null | string;
  /**
   * 主题风险字典ID
   */
  themeId?: number | null;
  /**
   * 当前市值
   */
  totalAmount?: number | null;
  [property: string]: any;
}

/**
 * ThemeRiskDictDO
 */
export interface ThemeRiskDictDO {
  enableStatus?: CommonStatus;
  id?: number | null;
  /**
   * 主题风险名称
   */
  themeName?: null | string;
  [property: string]: any;
}
