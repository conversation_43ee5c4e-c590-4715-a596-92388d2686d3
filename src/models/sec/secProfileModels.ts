/**
 * @file: secProfile
 * @description: 证券档案相关模型
 *
 */
import { SecType } from '@/enums/secEnum';

/**
 * TarkinJudgement - 用户证券池数据模型
 * @interface UserSecPoolVO
 * @property {number} id id
 * @property {string} judgement 判断值

 */
export interface TarkinJudgementType {
  /**
   * id(元)
   */
  id?: number | null;
  /**
   * 判断值
   */
  judgement?: string | null;
}

/**
 * SecFileInfoVO - 证券档案数据模型
 * @interface SecFileInfoVO
 */

export type SecFileInfoVO = {
  /**
   * 所属地区
   */
  areaDesc: null | string;
  /**
   * 流通盘占比（%）
   */
  circulatingRatio: number | null;
  /**
   * 最新收盘价（元）
   */
  closePrice: number | null;
  /**
   * 公司债列表
   */
  companyBondList: ExchangeProductInfoDO[] | null;
  /**
   * 控股股东，多个用、分隔
   */
  controlShareholder: null | string;
  /**
   * 可转债列表
   */
  convBondList: ConvBondArchiveDO[] | null;
  /**
   * 退市日期
   */
  endDate: null | string;
  /**
   * 企业债列表
   */
  enterpriseBondList: ExchangeProductInfoDO[] | null;
  /**
   * 财务评分
   */
  financialScore: number | null;
  /**
   * 一级行业名称
   */
  industryName: null | string;
  /**
   * 二级行业名称
   */
  industryNameTwo: null | string;
  /**
   * 上次评级
   */
  lastLevel: null | string;
  /**
   * 上次评级日期
   */
  lastLevelDate: null | string;
  /**
   * 系统评级
   */
  level: null | string;
  /**
   * 上市天数
   */
  listingDays: number | null;
  /**
   * 主营业务
   */
  mainBusiness: null | string;
  /**
   * 模型集中度
   */
  modelConcentration: null | string;
  /**
   * 模型融资保证金比例
   */
  modelFinanceMarginRatio: null | string;
  /**
   * 模型折算率
   */
  modelHaircut: null | string;
  /**
   * 模型融券保证金比例
   */
  modelShortMarginRatio: null | string;
  /**
   * 综合评分
   */
  overallScore: number | null;
  /**
   * 最新市净率
   */
  pb: number | null;
  /**
   * 评级理由
   */
  ratingReason: null | string;
  /**
   * 最新滚动市盈率
   */
  rollingPe: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 上市日期
   */
  startDate: null | string;
  /**
   * 静态市盈率
   */
  staticPe: number | null;
  /**
   * 证券状态
   */
  status: number | null;
  /**
   * 最新市值（亿元）
   */
  totalAmount: number | null;
  /**
   * 所属板块信息
   */
  typeInfo: null | string;
  /**
   * 用户模型评级
   */
  userLevel: null | string;
  [property: string]: any;
};

/**
 * ExchangeProductInfoDO
 */
export type ExchangeProductInfoDO = {
  /**
   * 证券代码
   */
  code: null | string;
  /**
   * 退市日期
   */
  endDate: null | string;
  /**
   * 主键
   */
  id: number | null;
  /**
   * 上市日期
   */
  listingDate: null | string;
  /**
   * 证券市场
   */
  market: null | string;
  /**
   * 证券类型
   */
  secCategory: null | string;
  /**
   * 交易所代码带后缀
   */
  secCode: string;
  /**
   * 证券简称
   */
  secName: null | string;
  /**
   * 证券品种
   */
  secType: SecType;
  /**
   * 数据源
   */
  source: null | string;
  [property: string]: any;
};

/**
 * ConvBondArchiveDO
 */
export type ConvBondArchiveDO = {
  /**
   * 债券余额
   */
  amount: number | null;
  /**
   * 债券期限
   */
  bondDuration: number | null;
  /**
   * 止息日
   */
  ceaseDate: null | string;
  /**
   * 当期票息
   */
  couponRate: number | null;
  /**
   * 创建时间
   */
  createTime: null | string;
  /**
   * 退市日
   */
  endDate: null | string;
  /**
   * 到期日
   */
  expireDate: null | string;
  /**
   * 证券名称
   */
  fullName: null | string;
  id: number | null;
  /**
   * 发行对象
   */
  issueObject: null | string;
  /**
   * 发行价格
   */
  issuePrice: number | null;
  /**
   * 发行规模
   */
  issueScale: number | null;
  /**
   * 发行类型
   */
  issueType: null | string;
  /**
   * 最近行权日
   */
  latestExerciseDate: null | string;
  /**
   * 上市日期
   */
  listingDate: null | string;
  /**
   * 评级机构
   */
  partyName: null | string;
  /**
   * 每年付息日
   */
  payDay: null | string;
  /**
   * 利率说明
   */
  payRateExplain: null | string;
  /**
   * 申购日期
   */
  publicStartDate: null | string;
  /**
   * 利率类型
   */
  rateType: null | string;
  /**
   * 信用评级
   */
  rating: null | string;
  /**
   * 赎回条款
   */
  redeemClause: null | string;
  /**
   * 强赎触发价
   */
  redeemTrigValue: number | null;
  /**
   * 剩余年限
   */
  remainingYear: null | string;
  /**
   * 回售条款
   */
  resaleClause: null | string;
  /**
   * 回售触发价
   */
  resaleTrigPrice: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  /**
   * 证券名称
   */
  secName: null | string;
  /**
   * 数据源
   */
  source: null | string;
  /**
   * 正股代码
   */
  stockCode: null | string;
  /**
   * 正股名称
   */
  stockName: null | string;
  /**
   * 转股结束日
   */
  transferEndDate: null | string;
  /**
   * 转股溢价率
   */
  transferRatio: number | null;
  /**
   * 转股开始日
   */
  transferStartDate: null | string;
  /**
   * 转股价(元)
   */
  transferStockPrice: number | null;
  /**
   * 转股价值(元)
   */
  transferStockValue: number | null;
  /**
   * 更新时间
   */
  updateTime: null | string;
  /**
   * 起息日
   */
  valueDate: null | string;
  [property: string]: any;
};
