/**
 * @file: secModels
 * @description: 证券相关数据模型
 * @date: 2024/8/27
 * @author: <PERSON> Ye
 */

/**
 * UserSecPoolVO - 用户证券池数据模型
 * @interface UserSecPoolVO
 * @property {number} closePrice 最新收盘价(元)
 * @property {null|string} date 最新日期
 * @property {null|string} industryName 一级行业
 * @property {null|string} level 系统评级
 * @property {null|string} peerHaircutRange 同业折算率区间
 * @property {null|string} peerLevelRange 同业评级区间
 * @property {number} rollingPe 滚动市盈率
 * @property {null|string} secCode 证券代码
 * @property {null|string} secName 证券名称
 * @property {number} totalAmount 最新市值(亿)
 * @property {null|string} userLevel 用户评级
 */
export interface UserSecPoolVO {
  /**
   * 最新收盘价(元)
   */
  closePrice?: number | null;
  /**
   * 最新日期
   */
  date?: null | string;
  /**
   * 一级行业
   */
  industryName?: null | string;
  /**
   * 系统评级
   */
  level?: null | string;
  /**
   * 同业折算率区间
   */
  peerHaircutRange?: null | string;
  /**
   * 同业评级区间
   */
  peerLevelRange?: null | string;
  /**
   * 滚动市盈率
   */
  rollingPe?: number | null;
  /**
   * 证券代码
   */
  secCode?: null | string;
  /**
   * 证券名称
   */
  secName?: null | string;
  /**
   * 最新市值(亿)
   */
  totalAmount?: number | null;
  /**
   * 用户评级
   */
  userLevel?: null | string;
  [property: string]: any;
}

/**
 * SecInfoTrackingVO - 证券信息追踪数据模型
 * @interface SecInfoTrackingVO
 * @property {null|string} controlShareholderPledgeRatio 控股股东质押比例(%)
 * @property {number} debt 负债合计(亿元)
 * @property {number} debtMidValue 负债合计中位数(亿元)
 * @property {number} debtRatio 资产负债率(%)
 * @property {number} debtRatioMidValue 资产负债率中位数(%)
 * @property {number} income 营业收入(亿元)
 * @property {number} incomeMidValue 营业收入中位数(亿元)
 * @property {number} netProfit 净利润(亿元)
 * @property {number} netProfitMidValue 净利润中位数(亿元)
 * @property {number} pledgeNumber 控股股东质押股份笔数
 * @property {null|string} secCode 证券代码
 */
export interface SecInfoTrackingVO {
  /**
   * 控股股东质押比例(%)
   */
  controlShareholderPledgeRatio: null | string;
  /**
   * 负债合计(亿元)
   */
  debt: number | null;
  /**
   * 负债合计中位数(亿元)
   */
  debtMidValue: number | null;
  /**
   * 资产负债率(%)
   */
  debtRatio: number | null;
  /**
   * 资产负债率中位数(%)
   */
  debtRatioMidValue: number | null;
  /**
   * 销售毛利率(%)
   */
  grossMargin: number | null;
  /**
   * 销售毛利率中位数(%)
   */
  grossMarginMidValue: number | null;
  /**
   * 营业收入(亿元)
   */
  income: number | null;
  /**
   * 营业收入中位数(亿元)
   */
  incomeMidValue: number | null;
  /**
   * 净利润(亿元)
   */
  netProfit: number | null;
  /**
   * 净利润中位数(亿元)
   */
  netProfitMidValue: number | null;
  /**
   * 控股股东质押股份笔数
   */
  pledgeNumber: number | null;
  /**
   * 证券代码
   */
  secCode: null | string;
  [property: string]: any;
}

export function createSecInfoTrackingVO(): SecInfoTrackingVO {
  return {
    controlShareholderPledgeRatio: null,
    debt: null,
    debtMidValue: null,
    debtRatio: null,
    debtRatioMidValue: null,
    income: null,
    incomeMidValue: null,
    netProfit: null,
    netProfitMidValue: null,
    pledgeNumber: null,
    secCode: null,
    grossMargin: null,
    grossMarginMidValue: null,
  };
}
