/**
 * @file constants统一导出
 * @description 由于使用了 unplugin-auto-import 的 dirs 配置，
 * 所有 constants 目录下的导出都会被自动扫描和导入，
 * 因此这个文件主要用于提供常量组合和类型定义
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

// 注意：不再重新导出其他文件的内容，避免重复导入
// unplugin-auto-import 会自动扫描 src/constants 目录下的所有文件

// ==================== 常量组合（独立定义，避免重复导入）====================

/**
 * 日志相关常量集合
 * @description 包含所有与日志记录相关的常量
 */
export const LOG_CONSTANTS = {
  LEVEL: {
    INFO: 'INFO',
    ERROR: 'ERROR',
  },
  COLOR: {
    INFO: 'green',
    ERROR: 'red',
  },
  QUERY_TYPE: {
    USER_NAME: 'userName',
    IP_ADDRESS: 'ipAddress',
    SCHEMA_NAME: 'schemaName',
    TASK_STATUS: 'taskStatus',
    SYNC_TYPE: 'syncType',
    DB_TYPE: 'dbType',
    TASK_ID: 'taskId',
    PROFILE_TYPE: 'profileType',
  },
} as const;

/**
 * 业务相关常量集合
 * @description 包含业务逻辑中常用的常量
 */
export const BUSINESS_CONSTANTS = {
  /** 默认分页大小 */
  DEFAULT_PAGE_SIZE: 10,
  /** 最大分页大小 */
  MAX_PAGE_SIZE: 1000,
  /** 默认查询超时时间（毫秒） */
  DEFAULT_QUERY_TIMEOUT: 30000,
  /** 文件上传最大大小（字节） */
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
} as const;
