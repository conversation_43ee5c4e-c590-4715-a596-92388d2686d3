/**
 * @file: serviceManageConstants
 * @description: 系统管理/恒生柜台T2SDK服务管理和MQ连接管理相关常量定义
 * @date: 2025/07/15
 * @author: AI Assistant
 */

import {
  type ClientOperation,
  type ClientStatus,
  MqConnectionStatus,
  ServiceOperation,
  ServiceStatus,
} from '@/models/systemManage/ServiceManageModel';

// ==================== 页面文本常量 ====================

/**
 * 页面标题和标签文本常量
 * @description 页面中显示的固定文本内容
 */
export const PAGE_TEXTS = {
  /** 页面主标题 */
  PAGE_TITLE: '🔧 恒生柜台T2SDK服务管理',

  /** 卡片标题 */
  CARD_TITLES: {
    SERVICE_CONTROL: '🚀 T2SDK服务控制',
    CLIENT_MANAGE: '💻 客户端连接管理',
    CONFIG_INFO: '⚙️ 配置信息概览',
  },

  /** 按钮文本 */
  BUTTONS: {
    REFRESH_STATUS: '刷新状态',
    START_SERVICE: '启动服务',
    STOP_SERVICE: '停止服务',
    RESTART_SERVICE: '重启服务',
    START_CLIENT: '启动',
    STOP_CLIENT: '停止',
    RESTART_CLIENT: '重启',
    BATCH_START: '批量启动',
    BATCH_STOP: '批量停止',
    BATCH_RESTART: '批量重启',
  },

  /** 标签文本 */
  LABELS: {
    SERVICE_STATUS: '服务状态',
    START_TIME: '启动时间',
    RUNNING_DURATION: '运行时长',
    LAST_UPDATE: '最后更新',
    CONFIG_STATUS: '配置状态',
    AVAILABLE_STATUS: '可用状态',
    CONFIGURED: '已配置',
    NOT_CONFIGURED: '未配置',
    AVAILABLE: '可用',
    NOT_AVAILABLE: '不可用',
    UNKNOWN: '未知',
  },

  /** 统计信息标签 */
  STATS: {
    TOTAL_CLIENTS: '总客户端数',
    CONFIGURED_CLIENTS: '已配置客户端',
    RUNNING_CLIENTS: '运行中客户端',
    AVAILABLE_CLIENTS: '可用客户端',
    TOTAL_COUNT: '总计',
    RUNNING_COUNT: '运行中',
    CONFIGURED_COUNT: '已配置',
  },
} as const;

// ==================== 状态映射常量 ====================

/**
 * 服务状态显示文本映射
 * @description 将服务状态枚举映射为用户友好的显示文本
 */
export const SERVICE_STATUS_TEXT_MAP: Record<ServiceStatus, string> = {
  [ServiceStatus.RUNNING]: '运行中',
  [ServiceStatus.STOPPED]: '已停止',
  [ServiceStatus.STARTING]: '启动中',
  [ServiceStatus.STOPPING]: '停止中',
  [ServiceStatus.ERROR]: '错误',
  [ServiceStatus.NOT_CONFIGURED]: '未配置',
} as const;

/**
 * 客户端状态显示文本映射
 * @description 将客户端状态枚举映射为用户友好的显示文本
 */
export const CLIENT_STATUS_TEXT_MAP: Record<ClientStatus, string> = SERVICE_STATUS_TEXT_MAP;

/**
 * 服务状态类型映射
 * @description 将服务状态映射为Naive UI的标签类型
 */
export const SERVICE_STATUS_TYPE_MAP: Record<
  ServiceStatus,
  'success' | 'error' | 'warning' | 'info' | 'default'
> = {
  [ServiceStatus.RUNNING]: 'success',
  [ServiceStatus.STOPPED]: 'error',
  [ServiceStatus.STARTING]: 'warning',
  [ServiceStatus.STOPPING]: 'warning',
  [ServiceStatus.ERROR]: 'error',
  [ServiceStatus.NOT_CONFIGURED]: 'default',
} as const;

/**
 * 客户端状态类型映射
 * @description 将客户端状态映射为Naive UI的标签类型
 */
export const CLIENT_STATUS_TYPE_MAP: Record<
  ClientStatus,
  'success' | 'error' | 'warning' | 'info' | 'default'
> = SERVICE_STATUS_TYPE_MAP;

// ==================== 操作相关常量 ====================

/**
 * 服务操作显示文本映射
 * @description 将操作类型映射为用户友好的显示文本
 */
export const SERVICE_OPERATION_TEXT_MAP: Record<ServiceOperation, string> = {
  [ServiceOperation.START]: '启动',
  [ServiceOperation.STOP]: '停止',
  [ServiceOperation.RESTART]: '重启',
} as const;

/**
 * 客户端操作显示文本映射
 * @description 将客户端操作类型映射为用户友好的显示文本
 */
export const CLIENT_OPERATION_TEXT_MAP: Record<ClientOperation, string> =
  SERVICE_OPERATION_TEXT_MAP;

/**
 * 需要确认的操作列表
 * @description 执行前需要用户确认的危险操作
 */
export const DANGEROUS_OPERATIONS: readonly ServiceOperation[] = [
  ServiceOperation.STOP,
  ServiceOperation.RESTART,
] as const;

// ==================== 消息文本常量 ====================

/**
 * 提示消息文本常量
 * @description 操作成功、失败、警告等提示消息
 */
export const MESSAGES = {
  /** 成功消息 */
  SUCCESS: {
    STATUS_REFRESH: '状态刷新成功',
    SERVICE_START: '服务启动操作成功',
    SERVICE_STOP: '服务停止操作成功',
    SERVICE_RESTART: '服务重启操作成功',
    CLIENT_START: '客户端启动操作成功',
    CLIENT_STOP: '客户端停止操作成功',
    CLIENT_RESTART: '客户端重启操作成功',
    BATCH_START: '批量启动操作完成',
    BATCH_STOP: '批量停止操作完成',
    BATCH_RESTART: '批量重启操作完成',
  },

  /** 错误消息 */
  ERROR: {
    STATUS_REFRESH: '状态刷新失败',
    GET_SERVICE_STATUS: '获取服务状态失败',
    GET_CLIENTS_STATUS: '获取客户端状态失败',
    SERVICE_OPERATION: '服务操作失败',
    CLIENT_OPERATION: '客户端操作失败',
    BATCH_OPERATION: '批量操作失败',
    NETWORK_ERROR: '网络请求失败，请稍后重试',
  },

  /** 警告消息 */
  WARNING: {
    NO_CLIENTS_SELECTED: '请先选择要操作的客户端',
  },

  /** 确认对话框消息 */
  CONFIRM: {
    SERVICE_STOP: '您确定要停止T2SDK服务吗？这可能会影响所有客户端连接。',
    SERVICE_RESTART: '您确定要重启T2SDK服务吗？这可能会影响所有客户端连接。',
    CLIENT_STOP: '您确定要停止客户端吗？',
    CLIENT_RESTART: '您确定要重启客户端吗？',
    BATCH_OPERATION: '您确定要{operation}选中的 {count} 个客户端吗？',
  },
} as const;

// ==================== 配置常量 ====================

/**
 * 自动刷新配置
 * @description 自动刷新相关的配置参数
 */
export const AUTO_REFRESH_CONFIG = {
  /** 自动刷新间隔（毫秒） */
  INTERVAL: 30000,

  /** 操作后延迟刷新时间（毫秒） */
  DELAY_AFTER_OPERATION: 1000,
} as const;

/**
 * 客户端显示名称映射
 * @description 将客户端内部名称映射为用户友好的显示名称
 */
export const CLIENT_DISPLAY_NAME_MAP: Record<string, string> = {
  retailCounter: '零售柜台',
  retailCounterTest: '零售柜台测试',
  institutionCounter: '机构柜台',
  client1: '客户端1',
  client2: '客户端2',
  client3: '客户端3',
  default: '默认客户端',
} as const;

/**
 * 默认客户端显示名称
 * @description 当客户端名称不在映射表中时使用的默认格式
 */
export const DEFAULT_CLIENT_NAME_FORMAT = '客户端 {name}';

// ==================== 样式相关常量 ====================

/**
 * CSS类名常量
 * @description 组件中使用的CSS类名
 */
export const CSS_CLASSES = {
  CONTAINER: 'service-manage-container',
  PAGE_HEADER: 'page-header',
  PAGE_TITLE: 'page-title',
  HEADER_ACTIONS: 'header-actions',
  SERVICE_CONTROL_CARD: 'service-control-card',
  CLIENT_MANAGE_CARD: 'client-manage-card',
  CONFIG_INFO_CARD: 'config-info-card',
  CLIENT_CARD: 'client-card',
  CLIENT_CARD_SELECTED: 'selected',
  BATCH_OPERATIONS: 'batch-operations',
  SERVICE_ACTIONS: 'service-actions',
  CLIENT_ACTIONS: 'client-actions',
} as const;

// ==================== 工具函数常量 ====================

/**
 * 获取状态显示文本
 * @param status 状态值
 * @returns 显示文本
 */
export const getStatusText = (status: ServiceStatus | ClientStatus): string => {
  return SERVICE_STATUS_TEXT_MAP[status] || PAGE_TEXTS.LABELS.UNKNOWN;
};

/**
 * 获取状态类型
 * @param status 状态值
 * @returns Naive UI标签类型
 */
export const getStatusType = (
  status: ServiceStatus | ClientStatus
): 'success' | 'error' | 'warning' | 'info' | 'default' => {
  return SERVICE_STATUS_TYPE_MAP[status] || 'default';
};

/**
 * 获取操作显示文本
 * @param operation 操作类型
 * @returns 显示文本
 */
export const getOperationText = (operation: ServiceOperation | ClientOperation): string => {
  return SERVICE_OPERATION_TEXT_MAP[operation] || operation;
};

/**
 * 判断是否为危险操作
 * @param operation 操作类型
 * @returns 是否为危险操作
 */
export const isDangerousOperation = (operation: ServiceOperation | ClientOperation): boolean => {
  return DANGEROUS_OPERATIONS.includes(operation as ServiceOperation);
};

/**
 * 获取客户端显示名称
 * @param clientName 客户端内部名称
 * @returns 显示名称
 */
export const getClientDisplayName = (clientName: string): string => {
  return (
    CLIENT_DISPLAY_NAME_MAP[clientName] || DEFAULT_CLIENT_NAME_FORMAT.replace('{name}', clientName)
  );
};

/**
 * 格式化时间显示
 * @param dateTime 时间字符串
 * @returns 格式化后的时间字符串
 */
export const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '未知';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch {
    return dateTime;
  }
};

/**
 * 格式化运行时长
 * @param duration 运行时长（毫秒）
 * @returns 格式化后的时长字符串
 */
export const formatDuration = (duration: number): string => {
  if (!duration || duration <= 0) return '未运行';

  const hours = Math.floor(duration / 3600000);
  const minutes = Math.floor((duration % 3600000) / 60000);
  const seconds = Math.floor((duration % 60000) / 1000);

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds}秒`;
  } else {
    return `${seconds}秒`;
  }
};

// ==================== MQ连接管理相关常量 ====================

/**
 * MQ管理页面文本常量
 * @description MQ连接管理页面中显示的固定文本内容
 */
export const MQ_PAGE_TEXTS = {
  /** 页面标题 */
  PAGE_TITLE: '🔗 MQ连接管理',

  /** 卡片标题 */
  CARD_TITLES: {
    CONNECTION_CONTROL: '🚀 MQ连接控制',
    QUEUE_INFO: '📋 队列信息',
    OPERATION_HISTORY: '📊 操作历史',
    TEST_PANEL: '🧪 测试面板',
  },

  /** 按钮文本 */
  BUTTONS: {
    REFRESH_STATUS: '刷新状态',
    START_CONNECTION: '启动连接',
    STOP_CONNECTION: '停止连接',
    RESTART_CONNECTION: '重启连接',
    TEST_SEND: '测试发送',
    CLEAR_HISTORY: '清空历史',
    EXPORT_HISTORY: '导出历史',
  },

  /** 标签文本 */
  LABELS: {
    CONNECTION_STATUS: '连接状态',
    CONNECT_TIME: '连接时间',
    CONNECTION_DURATION: '连接时长',
    LAST_UPDATE: '最后更新',
    AVAILABLE_STATUS: '可用状态',

    AVAILABLE: '可用',
    NOT_AVAILABLE: '不可用',
    UNKNOWN: '未知',
  },
} as const;

/**
 * MQ工厂状态显示文本映射
 * @description 将MQ工厂状态枚举映射为用户友好的显示文本
 */
export const MQ_CONNECTION_STATUS_TEXT_MAP: Record<MqConnectionStatus, string> = {
  [MqConnectionStatus.NOT_INITIALIZED]: '未初始化',
  [MqConnectionStatus.RUNNING]: '运行中',
  [MqConnectionStatus.STOPPED]: '已停止',
  [MqConnectionStatus.INIT_FAILED]: '初始化失败',
} as const;

/**
 * MQ工厂状态类型映射
 * @description 将MQ工厂状态映射为Naive UI的标签类型
 */
export const MQ_CONNECTION_STATUS_TYPE_MAP: Record<
  MqConnectionStatus,
  'success' | 'error' | 'warning' | 'info' | 'default'
> = {
  [MqConnectionStatus.NOT_INITIALIZED]: 'default',
  [MqConnectionStatus.RUNNING]: 'success',
  [MqConnectionStatus.STOPPED]: 'error',
  [MqConnectionStatus.INIT_FAILED]: 'error',
} as const;
