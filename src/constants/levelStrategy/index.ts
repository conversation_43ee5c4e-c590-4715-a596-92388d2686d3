/**
 * @file index.ts
 * @description 评级策略相关常量
 */

import { ref } from 'vue';
import { SelectOption } from 'naive-ui';

/**
 * 行情数据类型-涨跌幅
 */
export const MARKET_DATA_TYPE_PRICE_CHANGE = '涨跌幅';

/**
 * 财务数据-增速数据类型-选项
 */
export const GROWTH_RATE_TYPE_OPTIONS = ref<SelectOption[]>([
  { label: '3年负债增速-5年负债增速', value: '3年负债增速-5年负债增速' },
  { label: '3年营收增速-5年营收增速', value: '3年营收增速-5年营收增速' },
  { label: '3年净利润增速-5年净利润增速', value: '3年净利润增速-5年净利润增速' },
  { label: '3年营收负债增速差-5年营收负债增速差', value: '3年营收负债增速差-5年营收负债增速差' },
  {
    label: '3年净利润负债增速差-5年净利润负债增速差',
    value: '3年净利润负债增速差-5年净利润负债增速差',
  },
  { label: '3年扣非净利润增速-5年扣非净利润增速', value: '3年扣非净利润增速-5年扣非净利润增速' },
  { label: '3年归母净利润增速-5年归母净利润增速', value: '3年归母净利润增速-5年归母净利润增速' },
  { label: '3年每股净资产增速-5年每股净资产增速', value: '3年每股净资产增速-5年每股净资产增速' },
  {
    label: '3年净资产收益率增速-5年净资产收益率增速',
    value: '3年净资产收益率增速-5年净资产收益率增速',
  },
  { label: '3年毛利率增速-5年毛利率增速', value: '3年毛利率增速-5年毛利率增速' },
  { label: '3年总资产增速-5年总资产增速', value: '3年总资产增速-5年总资产增速' },
  { label: '3年净资产增速-5年净资产增速', value: '3年净资产增速-5年净资产增速' },
]);

/**
 * 财务数据-财务周期选项
 */
export const FINANCE_PERIOD_OPTIONS = ref<SelectOption[]>([
  { label: '最新季度报', value: '最新季度报' },
  { label: '年报', value: 'YearDate' },
  { label: '半年报', value: 'HalfYearDate' },
  { label: '三季报', value: 'ThirdQuarterDate' },
  { label: '一季报', value: 'OneQuarterDate' },
]);

/**
 * 策略条件标识符类型
 * @see LevelConditionOrderDO.conditions
 */
export type ConditionIdentifier =
  | 'A'
  | 'B'
  | 'C'
  | 'D'
  | 'E'
  | 'F'
  | 'G'
  | 'H'
  | 'I'
  | 'J'
  | 'K'
  | 'L'
  | 'M'
  | 'N'
  | 'O'
  | 'P'
  | 'Q'
  | 'R'
  | 'S'
  | 'T'
  | 'U'
  | 'V'
  | 'W'
  | 'X'
  | 'Y'
  | 'Z';

/**
 * 策略条件标识符常量数组
 * @see LevelConditionOrderDO.conditions
 */
export const CONDITION_IDENTIFIERS: readonly ConditionIdentifier[] = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
] as const;

/**
 * 检查字符串是否为有效的条件标识符
 * @param value 要检查的字符串
 * @returns 是否为有效的条件标识符
 */
export const isValidConditionIdentifier = (
  value: string | null | undefined
): value is ConditionIdentifier => {
  return value != null && CONDITION_IDENTIFIERS.includes(value as ConditionIdentifier);
};
