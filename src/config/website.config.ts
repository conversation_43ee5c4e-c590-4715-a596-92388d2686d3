import logoImage from '@/assets/images/logo.png';
import xingyeLogo from '@/assets/images/xingyeLogo.png';
import loginImage from '@/assets/images/account-logo.png';
import loginImage2 from '@/assets/images/account-logo2.png';
import { useGlobSetting } from '@/hooks/setting';

const globSetting = useGlobSetting();

const { setLogoName } = globSetting;
//console.log(setLogoName);
export const websiteConfig = Object.freeze({
  title: '',
  logo: setLogoName == 'xingye' ? xingyeLogo : loginImage2,
  loginImage: setLogoName == 'xingye' ? xingyeLogo : loginImage2,
  loginDesc:
    setLogoName == 'xingye'
      ? '塔金信用业务管理系统'
      : 'Intelligent Matrix Driven Risk Management System',
});
