/**
 * @file: ratingConfig
 * @description: 🚀 完全动态的评级处理系统 - 支持任意后端返回的评级类型
 * @date: 2024/12/19
 * @author: AI Assistant
 */

import { getLevelColor, getLevelMap } from '@/utils/business/level/rating';
import { setLevelColor } from '@/utils/ui/color/LevelColor';

/**
 * 评级配置接口
 */
export interface RatingConfig {
  /** 评级标签 */
  label: string;
  /** 评级值 */
  value: number;
  /** 是否显示 */
  visible?: boolean;
  /** 排序权重 */
  sortOrder?: number;
  /** 是否为动态发现的评级 */
  isDynamic?: boolean;
  /** 评级类别 */
  category?: 'known' | 'unknown' | 'special';
  /** 评级颜色 */
  color?: string;
}

/**
 * 🎯 动态评级注册表 - 运行时发现的评级会被注册到这里
 */
const dynamicRatingRegistry = new Map<string, RatingConfig>();

/**
 * 🎯 智能颜色分配 - 为评级分配合适的颜色
 * @param rating 评级字符串
 * @returns 颜色值
 */
const calculateRatingColor = (rating: string): string => {
  // 🔥 首先尝试使用现有的颜色函数
  const existingColor = setLevelColor(rating);
  if (existingColor && existingColor !== '#616161') {
    return existingColor;
  }

  // 🎯 为未知评级生成颜色
  const colors = [
    '#8e24aa',
    '#5e35b1',
    '#3949ab',
    '#1e88e5',
    '#039be5',
    '#00acc1',
    '#00897b',
    '#43a047',
    '#689f38',
    '#9e9d24',
    '#f57c00',
    '#ff5722',
    '#795548',
    '#607d8b',
    '#546e7a',
  ];

  // 基于评级字符串生成一个稳定的颜色索引
  let hash = 0;
  for (let i = 0; i < rating.length; i++) {
    hash = rating.charCodeAt(i) + ((hash << 5) - hash);
  }

  return colors[Math.abs(hash) % colors.length];
};

/**
 * 🎯 智能评级值计算 - 为未知评级分配合理的数值
 * @param rating 评级字符串
 * @returns 计算出的评级值
 */
const calculateRatingValue = (rating: string): number => {
  // 特殊评级处理
  if (rating === '未评级' || rating === 'UNRATED' || rating === '-') {
    return -1;
  }

  // 基于字符串长度和字符计算一个合理的值
  let value = 0;
  for (let i = 0; i < rating.length; i++) {
    const char = rating.charAt(i);
    if (char >= 'A' && char <= 'Z') {
      // A=26, B=25, C=24, ... Z=1
      value += 26 - (char.charCodeAt(0) - 65);
    } else if (char >= 'a' && char <= 'z') {
      // 小写字母值稍低
      value += (26 - (char.charCodeAt(0) - 97)) * 0.5;
    } else if (char >= '0' && char <= '9') {
      // 数字直接加值
      value += parseInt(char);
    }
  }

  // 确保值在合理范围内
  return Math.max(1, Math.min(100, value));
};

/**
 * 🚀 动态注册评级 - 发现新评级时自动注册
 * @param rating 评级字符串
 * @returns 评级配置
 */
export const registerDynamicRating = (rating: string): RatingConfig => {
  if (dynamicRatingRegistry.has(rating)) {
    return dynamicRatingRegistry.get(rating)!;
  }

  const ratingValue = calculateRatingValue(rating);
  const config: RatingConfig = {
    label: rating,
    value: ratingValue,
    visible: true,
    sortOrder: ratingValue,
    isDynamic: true,
    category: 'unknown',
    color: calculateRatingColor(rating),
  };

  dynamicRatingRegistry.set(rating, config);
  console.log(`🎯 动态注册新评级: ${rating}, 计算值: ${config.value}`);

  return config;
};

/**
 * 🎯 获取完整的评级配置 - 包含已知和动态发现的评级
 * @returns 评级配置数组
 */
export const getRatingConfig = (): RatingConfig[] => {
  const levelMap = getLevelMap();

  const knownRatings: RatingConfig[] = [
    // 🔥 基于现有评级映射创建配置，包含颜色信息
    ...levelMap.map((level) => ({
      label: level.label,
      value: level.value,
      visible: true,
      sortOrder: level.value,
      isDynamic: false,
      category: 'known' as const,
      color: setLevelColor(level.label), // 🎯 使用正确的颜色函数
    })),
    // 🔥 添加特殊评级
    {
      label: '未评级',
      value: 0,
      visible: true,
      sortOrder: -1,
      isDynamic: false,
      category: 'special' as const,
      color: setLevelColor('未评级'),
    },
  ];

  // 🚀 合并动态发现的评级
  const dynamicRatings = Array.from(dynamicRatingRegistry.values());

  return [...knownRatings, ...dynamicRatings];
};

/**
 * 🚀 从实际数据中发现所有评级类型 - 这是关键！
 * @param data 后端返回的数据数组
 * @returns 发现的所有评级类型
 */
export const discoverRatingsFromData = (data: any[]): string[] => {
  const discoveredRatings = new Set<string>();

  data.forEach((item) => {
    if (item.typeName && typeof item.typeName === 'string') {
      discoveredRatings.add(item.typeName);
      // 🎯 自动注册新发现的评级
      registerDynamicRating(item.typeName);
    }
  });

  const ratingsArray = Array.from(discoveredRatings);
  console.log(`🔍 从数据中发现评级类型: ${ratingsArray.join(', ')}`);

  return ratingsArray;
};

/**
 * 🎯 创建动态评级数据结构 - 基于实际数据动态生成
 * @param data 后端返回的数据（可选，用于动态发现评级）
 * @param includeHidden 是否包含隐藏的评级
 * @param sortForStacking 是否为堆叠图表排序（value 低的在上面）
 * @returns 评级数据对象
 */
export const createRatingDataStructure = (
  data?: any[],
  includeHidden = false,
  sortForStacking = false
): Record<string, any[]> => {
  // 🚀 如果提供了数据，先从数据中发现评级类型
  if (data && data.length > 0) {
    discoverRatingsFromData(data);
  }
  const ratingConfig = getRatingConfig();
  const ratingData: Record<string, any[]> = {};

  // 🎯 根据用途选择不同的排序方式
  const sortedConfig = ratingConfig
    .filter((config) => includeHidden || config.visible !== false)
    .sort((a, b) => {
      if (sortForStacking) {
        // 🔥 堆叠图表：value 值低的在前面（显示在上面）
        // 特殊处理：未评级始终在最后
        if (a.label === '未评级') return 1;
        if (b.label === '未评级') return -1;
        return (a.value || 0) - (b.value || 0); // 升序：E(0) → D(2) → C(2) → ... → AAA(10)
      } else {
        // 🔥 普通排序：按权重降序排列
        return (b.sortOrder || 0) - (a.sortOrder || 0);
      }
    });

  sortedConfig.forEach((config) => {
    ratingData[config.label] = [];
  });

  return ratingData;
};

/**
 * 🎯 获取所有可见的评级标签
 * @returns 评级标签数组
 */
export const getVisibleRatingLabels = (): string[] => {
  return getRatingConfig()
    .filter((config) => config.visible !== false)
    .sort((a, b) => (b.sortOrder || 0) - (a.sortOrder || 0))
    .map((config) => config.label);
};

/**
 * 🎯 获取评级颜色 - 智能颜色分配
 * @param rating 评级字符串
 * @returns 颜色值
 */
export const getRatingColor = (rating: string): string => {
  const config = getRatingConfig().find((c) => c.label === rating);
  if (config && config.color) {
    return config.color;
  }

  // 🚀 如果没有找到，自动注册并返回颜色
  const newConfig = registerDynamicRating(rating);
  return newConfig.color || '#616161';
};

/**
 * 🎯 检查评级是否有效
 * @param rating 评级字符串
 * @returns 是否为有效评级
 */
export const isValidRating = (rating: string): boolean => {
  if (!rating || typeof rating !== 'string') {
    return false;
  }

  // 检查是否已存在
  const existingConfig = getRatingConfig().find((config) => config.label === rating);
  if (existingConfig) {
    return true;
  }

  // 🎯 自动注册新的评级类型
  registerDynamicRating(rating);
  console.log(`✨ 自动注册新评级: ${rating}`);

  return true;
};

/**
 * 🎯 评级常量 - 便于在其他地方使用
 */
export const RATING_CONSTANTS = {
  /** 未评级标识 */
  UNRATED: '未评级',
  /** 最高评级 */
  HIGHEST: 'AAA',
  /** 最低评级 */
  LOWEST: 'E',
} as const;

/**
 * 🎨 验证颜色映射 - 检查 getLevelMap 和颜色函数的一致性
 */
export const validateColorMapping = () => {
  console.log('🎨 验证评级颜色映射...');

  const levelMap = getLevelMap();
  console.log('\n📋 getLevelMap() 中的评级:');

  levelMap.forEach((level) => {
    const configColor = getRatingColor(level.label);
    const setLevelColorResult = setLevelColor(level.label);
    const getLevelColorResult = getLevelColor(level.label);

    console.log(`🎯 ${level.label}:`, {
      value: level.value,
      configColor,
      setLevelColor: setLevelColorResult,
      getLevelColor: getLevelColorResult,
      isConsistent:
        configColor === setLevelColorResult && setLevelColorResult === getLevelColorResult,
    });
  });

  // 测试特殊评级
  const specialRatings = ['未评级', 'DD', 'ZZ'];
  console.log('\n🔍 特殊评级测试:');
  specialRatings.forEach((rating) => {
    const color = getRatingColor(rating);
    console.log(`${rating}: ${color}`);
  });
};

/**
 * 📊 测试排序逻辑 - 验证堆叠图表的排序是否正确
 */
export const testStackingOrder = () => {
  console.log('📊 测试堆叠图表排序逻辑...');

  // 普通排序（权重降序）
  const normalOrder = createRatingDataStructure(undefined, false, false);
  console.log('\n🔸 普通排序（权重高→低）:', Object.keys(normalOrder));

  // 堆叠排序（value 升序）
  const stackingOrder = createRatingDataStructure(undefined, false, true);
  console.log('🔸 堆叠排序（value低→高）:', Object.keys(stackingOrder));

  // 显示每个评级的 value 值
  console.log('\n📋 评级 value 值对照:');
  const levelMap = getLevelMap();
  levelMap.forEach(level => {
    console.log(`${level.label}: ${level.value}`);
  });

  console.log('\n✅ 堆叠排序说明：');
  console.log('- E(0) 应该在最前面（柱子最上面）');
  console.log('- D(2), C(2) 在中间');
  console.log('- AAA(10) 在最后面（柱子最下面）');
  console.log('- 未评级 始终在最后');
};

/**
 * 🎯 清理动态注册表 - 用于测试或重置
 */
export const clearDynamicRatings = () => {
  dynamicRatingRegistry.clear();
  console.log('🧹 已清理所有动态注册的评级');
};
