@import 'transition/index.less';

.n-message__content{
  font-size: 18px !important;
}

.n-message .n-message__icon{
  font-size: 22px !important;
}
 .n-cascader-menu .n-cascader-submenu .n-cascader-submenu--virtual{
  width: fit-content !important;
}
  .n-cascader-menu .n-cascader-option{
  width: fit-content  !important;
}
.n-cascader-submenu--virtual{
  width:fit-content  !important;
}
.n-cascader-menu.v-vl-visible-items{
  width: fit-content !important;
}
.n-cascader-menu  .v-vl{
  width: fit-content !important;
}

/* 每日预警页面Tab标签字体优化 - 全局样式 */
/* 主标签页字体优化 - 券商维度/个券维度 */
.main-tabs .n-tabs-tab {
  font-weight: 600 !important;
  font-size: 18px !important;
  letter-spacing: 0.3px !important;
  padding: 14px 24px !important;
  color: #2c3e50 !important;
  transition: all 0.3s ease !important;
}

.main-tabs .n-tabs-tab:hover {
  color: #3b82f6 !important;
  transform: translateY(-1px) !important;
}

.main-tabs .n-tabs-tab.n-tabs-tab--active {
  font-weight: 700 !important;
  color: #1d4ed8 !important;
}

/* 数据标签页字体优化 - 标的券调出/担保品调出等 */
.data-tabs .n-tabs-tab {
  font-weight: 600 !important;
  font-size: 16px !important;
  letter-spacing: 0.2px !important;
  padding: 12px 20px !important;
  color: #374151 !important;
  transition: all 0.3s ease !important;
}

.data-tabs .n-tabs-tab:hover {
  color: #059669 !important;
  transform: translateY(-1px) !important;
}

.data-tabs .n-tabs-tab.n-tabs-tab--active {
  font-weight: 700 !important;
  color: #047857 !important;
}

/* 导航区域高度优化 */
.main-tabs .n-tabs-nav {
  min-height: 56px !important;
  padding: 8px 12px !important;
}

.data-tabs .n-tabs-nav {
  min-height: 52px !important;
  padding: 6px 10px !important;
}

/* 通用Tab标签字体优化 - 确保所有tab都有更大更清晰的字体 */
.n-tabs .n-tabs-tab {
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.2px !important;
}

/* 针对每日预警页面的特殊优化 */
.main-tabs .n-tabs-tab {
  font-size: 18px !important;
  font-weight: 600 !important;
  letter-spacing: 0.3px !important;
  padding: 14px 24px !important;
}

.data-tabs .n-tabs-tab {
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.2px !important;
  padding: 12px 20px !important;
}

.broker-select-enhanced-large .n-base-selection .n-base-selection-label {
  position: static !important;
  height: 48px !important;
  width: 100% !important;
}

///* ==================== 🎨 全局按钮样式优化 ==================== */
//
///* 按钮通用样式优化 */
//.n-button {
//  border-radius: 8px !important;
//  font-weight: 500 !important;
//  transition: all 0.3s ease !important;
//  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
//}
//
///* 按钮悬停效果 */
//.n-button:hover {
//  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
//  transform: translateY(-1px) !important;
//}
//
///* 按钮按下效果 */
//.n-button:active {
//  transform: translateY(0) !important;
//  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
//}
//
///* 🎨 Primary 按钮样式 */
//.n-button--primary-type {
//  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
//.n-button--primary-type:hover {
//  background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
//}
//
//.n-button--primary-type:active {
//  background: linear-gradient(135deg, #004085 0%, #002752 100%) !important;
//}
//
///* 🎨 Success 按钮样式 */
//.n-button--success-type {
//  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
//.n-button--success-type:hover {
//  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
//}
//
//.n-button--success-type:active {
//  background: linear-gradient(135deg, #155724 0%, #0d3d1a 100%) !important;
//}
//
///* 🎨 Warning 按钮样式 */
//.n-button--warning-type {
//  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
//.n-button--warning-type:hover {
//  background: linear-gradient(135deg, #e0a800 0%, #c69500 100%) !important;
//}
//
//.n-button--warning-type:active {
//  background: linear-gradient(135deg, #c69500 0%, #a67c00 100%) !important;
//}
//
///* 🎨 Info 按钮样式 */
//.n-button--info-type {
//  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
//.n-button--info-type:hover {
//  background: linear-gradient(135deg, #138496 0%, #0f6674 100%) !important;
//}
//
//.n-button--info-type:active {
//  background: linear-gradient(135deg, #0f6674 0%, #0a4d56 100%) !important;
//}
//
///* 🎨 Error 按钮样式 */
//.n-button--error-type {
//  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
//.n-button--error-type:hover {
//  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
//}
//
//.n-button--error-type:active {
//  background: linear-gradient(135deg, #a71e2a 0%, #861e25 100%) !important;
//}
//
///* 🎨 Disabled 按钮样式 */
//.n-button--disabled {
//  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//  opacity: 0.6 !important;
//  cursor: not-allowed !important;
//}
//
//.n-button--disabled:hover {
//  transform: none !important;
//  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
//}
//
///* 🎨 Loading 按钮样式 */
//.n-button--loading {
//  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
//  border: none !important;
//  color: #ffffff !important;
//}
//
///* 🎨 Secondary 按钮样式优化 */
//.n-button--secondary-type {
//  border-radius: 8px !important;
//  font-weight: 500 !important;
//  transition: all 0.3s ease !important;
//}
//
///* 🎨 响应式设计 */
//@media (max-width: 768px) {
//  .n-button {
//    min-width: auto !important;
//    font-size: 12px !important;
//    padding: 0 12px !important;
//  }
//}
