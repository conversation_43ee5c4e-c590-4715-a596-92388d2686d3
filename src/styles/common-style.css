/* .n-form-item { */

/*  margin-bottom: 20px; */

/* } */



.n-input,
.n-input-number,
.n-date-picker {
  width: 100%; /* 设置宽度为100% */
  border-radius: 8px; /* 增加圆角，使其更加柔和 */
  font-size: 16px; /* 设置字体大小 */
  transition: border-color 0.3s, box-shadow 0.3s, transform 0.2s, background 0.3s; /* 添加背景过渡效果 */
  background: linear-gradient(135deg, #fcfcfd, #fcfdfb); /* 添加渐变背景 */
}

/* .n-input:focus, */

/* .n-input-number:focus, */

/* .n-date-picker:focus { */

/*  border-color: #af1c50 !important; */

/*  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3) !important; */

/*  transform: scale(1.02) !important; */

/* } */

/* 禁用状态下的输入框、数字输入框和日期选择器的样式 */
.n-input[disabled],
.n-input-number[disabled],
.n-date-picker[disabled] {
  background-color: #f5f5f5 !important; /* 设置禁用状态的背景颜色为浅灰色 */
  color: #817979 !important; /* 设置禁用状态的文本颜色为浅灰色 */
}

/* 输入框、数字输入框和日期选择器的占位符样式 */
.n-input::placeholder,
.n-input-number::placeholder,
.n-date-picker::placeholder {
  color: #bfbfbf; /* 设置占位符文本颜色为浅灰色 */
  font-style: italic; /* 设置占位符文本为斜体 */
}

/* 设置元素宽度为100% */
.w-full {
  width: 100%; /* 设置宽度为100% */
}

/* 标签左对齐的样式 */
.n-form-item-label-left {
  justify-content: flex-start; /* 设置标签内容左对齐 */
}

/* 高级美观的按钮样式 */
.n-button-primary {
  background: linear-gradient(135deg, #409eff, #66b1ff) !important; /* 使用渐变背景 */
  color: #fff; /* 设置按钮文本颜色为白色 */
  border: none; /* 移除边框 */
  padding: 10px 20px; /* 设置按钮内边距为 10px 20px */
  border-radius: 6px; /* 设置按钮的圆角半径为 6px */
  font-size: 16px; /* 设置按钮文本的字体大小为 16px */
  cursor: pointer; /* 设置鼠标悬停在按钮上时显示手型光标 */
  transition: background 0.3s, box-shadow 0.3s; /* 添加背景色和阴影的过渡效果，持续时间为 0.3 秒 */
}

/* 鼠标悬停在按钮上时的样式 */
.n-button-primary:hover {
  background: linear-gradient(135deg, #66b1ff, #409eff); /* 修改背景渐变方向 */
  box-shadow: 0 4px 8px rgb(64 158 255 / 20%); /* 添加阴影效果，颜色为 rgba(64, 158, 255, 0.2) */
}

/* 按钮被点击时的样式 */
.n-button-primary:active {
  background: linear-gradient(135deg, #3a8ee6, #5aafff); /* 修改背景渐变方向和颜色 */
  box-shadow: 0 2px 4px rgb(64 158 255 / 30%); /* 添加阴影效果，颜色为 rgba(64, 158, 255, 0.3) */
}

/* 禁用状态下的按钮样式 */
.n-button-primary[disabled] {
  background: #dcdcdc; /* 设置禁用状态的背景颜色为浅灰色 */
  cursor: not-allowed; /* 设置禁用状态的光标样式 */
}

/* 只读模式禁用按钮样式 - 全局通用样式 */
.readonly-disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* 卡片样式 */
.box-card {
  background-color: #fffefe !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 16px rgb(0 0 0 / 15%);
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}


.box-card .clearfix {
  font-size: 20px !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 0;
  padding: 10px;
  background-image: linear-gradient(135deg, #fafcff 0%, #f7f9fd 100%) !important;
  background-size: cover;
  position: relative;
  border-radius: 8px 8px 0 0;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 20%);
  letter-spacing: 1px;
}


.rating-text-AAA {
  color: #c62828 !important;
}

.rating-text-AA {
  color: #e53935 !important;
}

.rating-text-A {
  color: #ef5350 !important;
}

.rating-text-BBB {
  color: #0277bd !important;
}

.rating-text-BB {
  color: #039be5 !important;
}

.rating-text-B {
  color: #29b6f6 !important;
}

.rating-text-CCC {
  color: #f9a825 !important;
}

.rating-text-CC {
  color: #fbc02d !important;
}

.rating-text-C {
  color: #fdd835 !important;
}

.rating-text-D {
  color: #5907a2 !important;
}

.rating-text-E {
  color: #616161 !important;
}


.rating-bg-AAA {
  background: #c62828 !important;
}

.rating-bg-AA {
  background: #e53935 !important;
}

.rating-bg-A {
  background: #ef5350 !important;
}

.rating-bg-BBB {
  background: #0277bd !important;
}

.rating-bg-BB {
  background: #039be5 !important;
}

.rating-bg-B {
  background: #29b6f6 !important;
}

.rating-bg-CCC {
  background: #f9a825 !important;
}

.rating-bg-CC {
  background: #fbc02d !important;
}

.rating-bg-C {
  background: #fdd835 !important;
}

.rating-bg-D {
  background: #5907a2 !important;
}

.rating-bg-E {
  background: #616161 !important;
}

/* 风险标签样式 */
.label-item {
  display: inline-block;
  margin-right: 5px;
  margin-top: 5px;
  padding: 0 8px;
  border-radius: 25px !important;
  transition: all 1s ease;
  cursor: pointer;
  color: #fff !important;
  text-align: center !important;

  /* font-weight: 550 !important; */
  font-size: xx-large !important;
  letter-spacing: 1px !important;
}

.label-item:hover {
  transform: translateY(-1px) !important;
}

/* 风险标签不同风险等级对应颜色 */
.label-risk-level-1 {
  background: linear-gradient(135deg, #56ab2f, #a8e063) !important;
  box-shadow: 0 4px 6px rgb(86 171 47 / 30%);
}

.label-risk-level-1:hover {
  box-shadow: 0 6px 8px rgb(86 171 47 / 40%) !important;
}

.label-risk-level-2 {
  background: linear-gradient(135deg, #e3d300, #ead920) !important;
  box-shadow: 0 4px 6px rgb(148 142 66 / 30%);
}

.label-risk-level-2:hover {
  box-shadow: 0 6px 8px rgb(217 192 37 / 40%) !important;
}

.label-risk-level-3 {
  background: linear-gradient(135deg, #f7971e, #f7971e) !important;
  box-shadow: 0 4px 6px rgb(247 151 30 / 30%);
}

.label-risk-level-3:hover {
  box-shadow: 0 6px 8px rgb(247 151 30 / 40%) !important;
}

.label-risk-level-4 {
  background: linear-gradient(135deg, #dc2f5b, #dc2f5b) !important;
  box-shadow: 0 4px 6px rgb(220 47 91 / 30%);
}

.label-risk-level-4:hover {
  box-shadow: 0 6px 8px rgb(220 47 91 / 40%) !important;
}

.label-risk-level-5 {
  background: linear-gradient(135deg, #ea132e, #9f1d0c) !important;
  box-shadow: 0 4px 6px rgb(234 19 46 / 30%);
}

.label-risk-level-5:hover {
  box-shadow: 0 6px 8px rgb(234 19 46 / 40%) !important;
}

