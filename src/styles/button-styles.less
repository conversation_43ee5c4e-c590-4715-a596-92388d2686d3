/* ==================== 🎨 Naive UI 按钮全局样式配置 ==================== */
/* 
 * 文件说明：统一管理所有 Naive UI 按钮组件的样式
 * 创建时间：2025-01-23
 * 维护说明：所有按钮样式修改请在此文件中进行
 */

/* ==================== 🔧 按钮基础样式 ==================== */

.n-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
}

/* 悬停效果 */
.n-button:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 按下效果 */
.n-button:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 焦点效果 */
.n-button:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* ==================== 🎨 按钮类型样式 ==================== */

/* Primary 按钮 - 主要操作 */
.n-button--primary-type {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: #ffffff !important;
}

.n-button--primary-type:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
}

.n-button--primary-type:active {
  background: linear-gradient(135deg, #004085 0%, #002752 100%) !important;
}

/* Success 按钮 - 成功操作 */
.n-button--success-type {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
  color: #ffffff !important;
}

.n-button--success-type:hover {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
}

.n-button--success-type:active {
  background: linear-gradient(135deg, #155724 0%, #0d3d1a 100%) !important;
}

/* Warning 按钮 - 警告操作 */
.n-button--warning-type {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #ffffff !important;
}

.n-button--warning-type:hover {
  background: linear-gradient(135deg, #e0a800 0%, #c69500 100%) !important;
}

.n-button--warning-type:active {
  background: linear-gradient(135deg, #c69500 0%, #a67c00 100%) !important;
}

/* Info 按钮 - 信息操作 */
.n-button--info-type {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  color: #ffffff !important;
}

.n-button--info-type:hover {
  background: linear-gradient(135deg, #138496 0%, #0f6674 100%) !important;
}

.n-button--info-type:active {
  background: linear-gradient(135deg, #0f6674 0%, #0a4d56 100%) !important;
}

/* Error 按钮 - 错误/危险操作 */
.n-button--error-type {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  color: #ffffff !important;
}

.n-button--error-type:hover {
  background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
}

.n-button--error-type:active {
  background: linear-gradient(135deg, #a71e2a 0%, #861e25 100%) !important;
}

/* ==================== 🚫 按钮状态样式 ==================== */

/* Disabled 按钮 - 禁用状态 */
.n-button--disabled {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  color: #ffffff !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

.n-button--disabled:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Loading 按钮 - 加载状态 */
.n-button--loading {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #ffffff !important;
  cursor: wait !important;
}

.n-button--loading:hover {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

/* ==================== 🎭 按钮尺寸样式 ==================== */

/* 大尺寸按钮 */
.n-button--large-size {
  padding: 12px 24px !important;
  font-size: 16px !important;
  min-height: 40px !important;
}

/* 中等尺寸按钮 */
.n-button--medium-size {
  padding: 8px 16px !important;
  font-size: 14px !important;
  min-height: 36px !important;
}

/* 小尺寸按钮 */
.n-button--small-size {
  padding: 6px 12px !important;
  font-size: 12px !important;
  min-height: 28px !important;
}

/* 超小尺寸按钮 */
.n-button--tiny-size {
  padding: 4px 8px !important;
  font-size: 11px !important;
  min-height: 24px !important;
}

/* ==================== 📱 响应式设计 ==================== */

@media (max-width: 768px) {
  .n-button {
    min-width: auto !important;
    font-size: 12px !important;
    padding: 6px 12px !important;
  }
  
  .n-button--large-size {
    padding: 10px 20px !important;
    font-size: 14px !important;
  }
  
  .n-button--medium-size {
    padding: 8px 14px !important;
    font-size: 12px !important;
  }
}

/* ==================== 🎯 特殊场景优化 ==================== */

/* 按钮组中的按钮 */
.n-button-group .n-button {
  border-radius: 0 !important;
}

.n-button-group .n-button:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.n-button-group .n-button:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

/* 图标按钮优化 */
.n-button .n-icon {
  transition: transform 0.3s ease !important;
}

.n-button:hover .n-icon {
  transform: scale(1.1) !important;
}

/* ==================== 🌙 深色主题适配 ==================== */

[data-theme="dark"] .n-button {
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .n-button:hover {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15) !important;
}

/* ==================== 🎨 自定义动画效果 ==================== */

@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.n-button--pulse {
  animation: button-pulse 2s infinite !important;
}
