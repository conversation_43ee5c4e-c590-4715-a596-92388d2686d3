/* styles/table-styles.css */
.n-data-table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
  font-size: 1.1em !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  /* box-shadow: 0 4px 12px rgb(0 0 0 / 15%); */

  /* border-radius: 10px; */
  overflow: hidden;
  background-color: #fdfcfc !important;
  transition: background 0.3s, box-shadow 0.3s !important; /* 定义背景色和阴影的过渡效果 */
}


.n-data-table th,
.n-data-table td {
  padding: 12px 15px;
  text-align: center;
  font-weight: 500 !important;
  vertical-align: middle;
  border-right: 0.8px solid #e0e0e0 !important; /* 添加列之间的边框 */
}

/* 最后一列不需要右边框 */
.n-data-table th:last-child,
.n-data-table td:last-child {
  border-right: none !important;
}

/* 新增表头背景颜色 */
.n-data-table th{
  background-color: #d2ebfc !important;
  text-transform: uppercase !important;
  letter-spacing: 0.1em !important;
  border-right: 1px solid #b8d4f0 !important; /* 表头列边框，颜色稍深 */
}

/* 表头最后一列不需要右边框 */
.n-data-table th:last-child {
  border-right: none !important;
}



/* 设置表格奇数行的背景色 */
.n-data-table tr:nth-child(odd) >td {
  background-color: #fffff9 !important; /* 奇数行背景色 */
}

/* 设置表格鼠标悬停时的背景色 */
.n-data-table tr:hover >td {
  background-color: #ffeada !important;
  box-shadow: 0 2px 12px rgb(217 215 215 / 15%);
}


/* 确保表格单元格内元素的定位相对 */
.n-data-table td {
  position: relative;
}

/* 自定义滚动条样式 */
.n-data-table::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
}

.n-data-table::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道的背景色 */
  border-radius: 10px; /* 滚动条轨道的圆角 */
}

.n-data-table::-webkit-scrollbar-thumb {
  background: #888; /* 滚动条拇指的背景色 */
  border-radius: 10px; /* 滚动条拇指的圆角 */
}


/* 表格居中对齐样式 */
.center-table .n-data-table-th__title-wrapper {
  text-align: center !important;
  justify-content: center !important;
}

.center-table .n-data-table-td {
  text-align: center;
  vertical-align: middle !important;
}

/* 全局表格居中对齐优化 - 更强的优先级 */
.n-data-table .n-data-table-th__title-wrapper {
  text-align: center;
  justify-content: center;
  display: flex !important;
  align-items: center ;
  width: 100% !important;
}

.n-data-table .n-data-table-td {
  text-align: center ;
  vertical-align: middle !important;
  display: table-cell !important;
}

/* Naive UI 表格单元格内容居中 */
.n-data-table .n-data-table-td__content {
  text-align: center;
  display: flex !important;
  justify-content: center ;
  align-items: center;
  width: 100% !important;
  min-height: 40px !important;
}

/* 表格按钮居中对齐 */
.n-data-table .n-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  margin: 0 auto !important;
}

.n-data-table .n-button__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

/* 表格标签居中 */
.n-data-table .n-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
}

/* 表格内容垂直居中 */
.n-data-table tbody tr {
  height: auto !important;
  min-height: 48px !important;
}

.n-data-table tbody td {
  height: auto !important;
  min-height: 48px !important;
  line-height: 1.5 !important;
  display: table-cell !important;
  vertical-align: middle !important;
}

/* 强制所有表格内容居中 - 排除交互组件 */
.n-data-table td > *:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-space):not(.n-checkbox):not(.n-input-number) {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
}

/* 特殊处理 NSpace 组件，保持其 flex 布局 */
.n-data-table td > .n-space {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
}

.n-data-table td > .n-button,
.n-data-table td > .n-tag {
  display: inline-flex !important;
  margin: 0 auto !important;
}

/* 针对 Naive UI 表格的深度样式覆盖 */
.n-data-table .n-data-table-tbody .n-data-table-tr .n-data-table-td {
  text-align: center !important;
  vertical-align: middle !important;
}

.n-data-table .n-data-table-tbody .n-data-table-tr .n-data-table-td > *:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-space):not(.n-checkbox):not(.n-input-number):not(.n-time-picker):not(.n-cascader):not(.n-tree-select) {
  text-align: center !important;
  margin: 0 auto !important;
  display: inline-block !important;
}

/* 特殊处理 NSpace 组件，保持其 flex 布局 */
.n-data-table .n-data-table-tbody .n-data-table-tr .n-data-table-td > .n-space {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
}



.n-data-table .n-data-table-tbody .n-data-table-tr .n-data-table-td .n-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
}

/* 表格头部居中 */
.n-data-table .n-data-table-thead .n-data-table-tr .n-data-table-th {
  text-align: center !important;
}

.n-data-table .n-data-table-thead .n-data-table-tr .n-data-table-th .n-data-table-th__title-wrapper {
  justify-content: center !important;
  text-align: center !important;
}

/* 确保文本内容居中 */
.n-data-table td,
.n-data-table th {
  text-align: center !important;
  vertical-align: middle !important;
}

.n-data-table td > span:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-checkbox):not(.n-input-number),
.n-data-table td > div:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-checkbox):not(.n-input-number):not(.n-space),
.n-data-table td > a:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-checkbox):not(.n-input-number) {
  text-align: center !important;
  display: inline-block !important;
  width: auto !important;
}

/* 最强优先级的表格居中样式 */
.force-center-table .n-data-table,
.force-center-table .n-data-table-wrapper,
.force-center-table .n-data-table-container {
  text-align: center !important;
}

.force-center-table .n-data-table td,
.force-center-table .n-data-table th,
.force-center-table .n-data-table-td,
.force-center-table .n-data-table-th {
  text-align: center !important;
  vertical-align: middle !important;
}

.force-center-table .n-data-table-th__title-wrapper {
  justify-content: center !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
}



/* 特殊处理 NSpace 组件，保持其 flex 布局 */
.force-center-table .n-data-table td > .n-space,
.force-center-table .n-data-table-td > .n-space {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
}

.force-center-table .n-data-table .n-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
  text-align: center !important;
}

.force-center-table .n-data-table .n-button__content {
  justify-content: center !important;
  text-align: center !important;
  width: auto !important;
}

.force-center-table .n-data-table .n-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
}

/* 超强优先级 - 直接针对 Naive UI 的具体类名 */
.force-center-table .n-data-table-tbody .n-data-table-tr .n-data-table-td,
.force-center-table .n-data-table-thead .n-data-table-tr .n-data-table-th {
  text-align: center !important;
  vertical-align: middle !important;
}

.force-center-table .n-data-table-tbody .n-data-table-tr .n-data-table-td > *:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-space):not(.n-checkbox):not(.n-input-number):not(.n-time-picker):not(.n-cascader):not(.n-tree-select),
.force-center-table .n-data-table-thead .n-data-table-tr .n-data-table-th > *:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-space):not(.n-checkbox):not(.n-input-number):not(.n-time-picker):not(.n-cascader):not(.n-tree-select) :not(.force-center-table) {
  text-align: center !important;
  margin: 0 auto !important;
  display: inline-block !important;
}

/* 特殊处理 NSpace 组件，保持其 flex 布局 */
.force-center-table .n-data-table-tbody .n-data-table-tr .n-data-table-td > .n-space,
.force-center-table .n-data-table-thead .n-data-table-tr .n-data-table-th > .n-space {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
}

.force-center-table .n-data-table-tbody .n-data-table-tr .n-data-table-td .n-button,
.force-center-table .n-data-table-thead .n-data-table-tr .n-data-table-th .n-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto !important;
  text-align: center !important;
}

/* 针对表格内的文本节点 */
.force-center-table .n-data-table td,
.force-center-table .n-data-table th {
  text-align: center !important;
  vertical-align: middle !important;
}

.force-center-table .n-data-table td::before,
.force-center-table .n-data-table th::before {
  display: none !important;
}

/* 确保文本子元素都居中 - 排除交互组件 */
.force-center-table .n-data-table span:not(.n-switch):not(.n-button):not(.n-tag):not(.n-input):not(.n-select):not(.n-date-picker):not(.n-checkbox):not(.n-input-number),
.force-center-table .n-data-table p,
.force-center-table .n-data-table strong,
.force-center-table .n-data-table em {
  text-align: center !important;
}


