<template>
  <!-- 集中度模型管理/集中度分组模型(新) -->
  <div id="newConcentrationGroupingModel" :key="updateKey" class="concentration-model-container">
    <n-card class="model-list-card">
      <template #header>
        <div class="card-header">
          <n-h2 class="model-list-title" prefix="bar" type="error">
            模型列表
            <n-popover>
              <template #trigger>
                <n-icon class="help-icon" size="24">
                  <HelpCircleOutline />
                </n-icon>
              </template>
              <div class="help-content">点击下方模型即可编辑。</div>
            </n-popover>
          </n-h2>
        </div>
      </template>
      <template #header-extra>
        <n-button
          v-show="!buttonHiding"
          class="action-button primary-button"
          type="primary"
          @click="addNewRule"
        >
          <template #icon>
            <n-icon>
              <AddCircleOutline />
            </n-icon>
          </template>
          新增模型
        </n-button>
      </template>
      <template #action>
        <!--   页面右下角的按钮     -->
        <n-space justify="end">
          <n-button
            v-show="!buttonHiding"
            class="action-button success-button"
            type="success"
            @click="optionConfig"
          >
            <template #icon>
              <n-icon>
                <SettingsOutline />
              </n-icon>
            </template>
            可选分组选项配置
          </n-button>
        </n-space>
      </template>

      <ConcentrationModelSetup
        ref="ConcentrationModelSetupRef"
        :buttonHiding="buttonHiding"
        :ifUser="ifUser"
        :levelModelList="userLevelSrtModelList"
        @click-model="clickModel"
        @edit-concentration="editConcentration"
        @update-enable-status="updateEnableStatus"
        @update-main-status="updateMainStatus"
        @colse-model="handleCloseModel"
      />
    </n-card>

    <div class="card-spacing"></div>

    <n-card class="main-content-card">
      <template #header>
        <div class="main-content-layout">
          <!-- 左侧区域：模型信息 -->
          <div class="left-section">
            <n-h1 class="current-model-title" prefix="bar">
              {{ currentModel.modelName }}
            </n-h1>
            <div class="calculate-time">计算时间：{{ currentModel.calculateTime || '-' }}</div>
            <!-- 自动刷新状态指示器 -->
            <div v-if="isAutoRefreshing" class="auto-refresh-indicator">
              <n-icon class="refresh-icon">
                <svg fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2V6L16 2L12 2Z" fill="currentColor" />
                  <path d="M12 18V22L8 18L12 18Z" fill="currentColor" />
                  <path d="M4.93 4.93L7.76 7.76L4.93 4.93Z" fill="currentColor" />
                  <path d="M16.24 16.24L19.07 19.07L16.24 16.24Z" fill="currentColor" />
                  <path d="M2 12H6L2 8L2 12Z" fill="currentColor" />
                  <path d="M18 12H22L18 16L18 12Z" fill="currentColor" />
                  <path d="M4.93 19.07L7.76 16.24L4.93 19.07Z" fill="currentColor" />
                  <path d="M16.24 7.76L19.07 4.93L16.24 7.76Z" fill="currentColor" />
                </svg>
              </n-icon>
              <span class="refresh-text">自动检查计算状态中...</span>
            </div>
          </div>

          <!-- 中央区域：统计图表 -->
          <div class="center-section">
            <StockLevelNumChart
              :modelId="currentModel.modelId"
              :showTitle="true"
              :showXLine="true"
              class="main-stock-chart"
              width="750px"
            />
          </div>

          <!-- 右侧区域：操作按钮 -->
          <div class="right-section">
            <div class="action-buttons-vertical">
              <n-button
                :disabled="ifCalculating == 0"
                :type="showModelResultTable ? 'info' : 'success'"
                class="action-button result-button"
                @click="handleModelResult"
              >
                <template #icon>
                  <n-icon>
                    <EyeOutline v-if="!showModelResultTable" />
                    <EyeOffOutline v-else />
                  </n-icon>
                </template>
                {{
                  ifCalculating === 0
                    ? '模型结果正在计算中'
                    : showModelResultTable
                    ? '查看配置信息'
                    : '查看模型结果'
                }}
              </n-button>
              <!-- 模型结果比较按钮 -->
              <n-button
                :disabled="ifCalculating == 0"
                :type="showModelResultCompareTable ? 'info' : 'warning'"
                class="action-button compare-button"
                @click="handleModelResultCompare"
              >
                <template #icon>
                  <n-icon>
                    <GitCompareOutline v-if="!showModelResultCompareTable" />
                    <EyeOffOutline v-else />
                  </n-icon>
                </template>
                {{
                  ifCalculating === 0
                    ? '模型结果正在计算中'
                    : showModelResultCompareTable
                    ? '查看配置信息'
                    : '模型历史结果比较'
                }}
              </n-button>
              <!-- 计算按钮 -->
              <n-button
                :disabled="isCurrentModelReadonly || currentModel.ifCalculating == 0"
                :loading="calculateLoading"
                class="action-button calculate-button"
                secondary
                strong
                type="success"
                @click="handleCalculateModel"
              >
                <template #icon>
                  <n-icon>
                    <CalculatorOutline />
                  </n-icon>
                </template>
                {{
                  calculateLoading
                    ? '计算中'
                    : currentModel.ifCalculating == 0
                    ? '正在计算中'
                    : '点击计算'
                }}
              </n-button>
            </div>
          </div>
        </div>
      </template>
      <n-tabs
        v-show="!showModelResultTable && !showModelResultCompareTable"
        :key="modelId"
        animated
        class="main-tabs"
        type="card"
      >
        <n-tab-pane :name="SecType.STOCK" display-directive="show" tab="股票">
          <div class="tab-content">
            <StockAllocation
              :group-list="groupOption"
              :isReadonly="isCurrentModelReadonly"
              :levelModelId="levelModelId"
              :modelId="currentModel.modelId"
              :secType="SecType.STOCK"
            />
          </div>
        </n-tab-pane>
        <n-tab-pane :name="SecType.FUND" display-directive="show" tab="基金">
          <div class="tab-content">
            <DefaultConcentrationGroup
              :group-list="groupOption"
              :isReadonly="isCurrentModelReadonly"
              :modelId="currentModel.modelId"
              :secType="SecType.FUND"
            />
            <div class="content-spacing"></div>
            <ConcentrationFundConfig
              :group-list="groupOption"
              :isReadonly="isCurrentModelReadonly"
              :modelId="currentModel.modelId"
            />
          </div>
        </n-tab-pane>
        <n-tab-pane :name="SecType.BOND" display-directive="show" tab="债券">
          <div class="tab-content">
            <DefaultConcentrationGroup
              :group-list="groupOption"
              :isReadonly="isCurrentModelReadonly"
              :modelId="currentModel.modelId"
              :secType="SecType.BOND"
            />
            <div class="content-spacing"></div>
            <BondAllocation
              :group-list="groupOption"
              :isReadonly="isCurrentModelReadonly"
              :modelId="currentModel.modelId"
              :secType="SecType.BOND"
            />
          </div>
        </n-tab-pane>
      </n-tabs>

      <!-- 模型结果 -->
      <div v-if="showModelResultTable" class="model-result-container">
        <ConcentrationGroupingModelResult :model-id="currentModel.modelId" />
      </div>

      <!-- 模型结果比较 -->
      <div v-if="showModelResultCompareTable" class="model-result-compare-container">
        <ConcentrationGroupingModelResultCompare
          :calculate-time="currentModel.calculateTime"
          :model-id="currentModel.modelId"
        />
      </div>
    </n-card>

    <!-- 新增/编辑模型弹窗 -->
    <n-modal
      v-model:show="addShowModal"
      :bordered="false"
      :style="{ width: '70vw', maxWidth: '800px', minWidth: '500px' }"
      :title="addForm.modelId ? '编辑模型' : '新增模型'"
      class="custom-modal add-model-modal"
      preset="card"
    >
      <div class="modal-content">
        <n-form ref="formRef" class="model-form" label-placement="left" label-width="140">
          <n-form-item class="form-item" label="模型名称">
            <n-input
              v-model:value="addForm.modelName"
              class="form-input"
              placeholder="请输入模型名称"
            />
          </n-form-item>
          <n-form-item class="form-item" label="关联评级策略模型">
            <n-select
              v-model:value="addForm.relatedLevelStrategyModelId"
              :options="userLevelSrtModelList"
              class="form-select"
              clearable
              label-field="modelName"
              placeholder="请选择关联评级策略模型"
              value-field="modelId"
            />
          </n-form-item>
          <n-form-item class="form-item" label="模型备注">
            <n-input
              v-model:value="addForm.remark"
              :rows="4"
              class="form-textarea"
              placeholder="请输入备注"
              type="textarea"
            />
          </n-form-item>
        </n-form>

        <div class="modal-actions">
          <n-space justify="center" size="large">
            <n-button class="modal-button cancel-button" size="large" @click="addShowModal = false">
              取消
            </n-button>
            <n-button
              class="modal-button confirm-button"
              size="large"
              type="primary"
              @click="saveNewRule"
            >
              确定
            </n-button>
          </n-space>
        </div>
      </div>
    </n-modal>

    <!-- "可选分组选项配置"弹窗 -->
    <n-modal
      v-model:show="optionConfigModal"
      :bordered="false"
      :style="{ width: '75vw', maxWidth: '1200px', minWidth: '800px' }"
      class="custom-modal option-config-modal"
      preset="card"
      title="可选分组选项配置"
    >
      <div class="modal-content">
        <SelectableOptionConfig :group-list="groupOption" @close="handleOptionConfigModalClose" />
      </div>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  // ① 官方库
  import { computed, onActivated, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  // ② 三方库
  import { useMessage } from 'naive-ui';
  import {
    AddCircleOutline,
    CalculatorOutline,
    EyeOffOutline,
    EyeOutline,
    GitCompareOutline,
    HelpCircleOutline,
    SettingsOutline,
  } from '@vicons/ionicons5';

  // ③ 别名路径
  import { updateConcentrationModel } from '@/api/marginTrading/concentration/concentrationModelApi';
  import {
    calculateConcentraGroupModel,
    querySelectableGroupOption,
  } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import { getUserLevelSrtModelList } from '@/api/levelStrategy/model/levelStrategyModelApi';
  import { SecType } from '@/enums/secEnum';
  import { CounterSecurityCategoryDictDO } from '@/models/common/baseResponse';
  import { ConcentrationModelConfigDO } from '@/models/marginTrading/model/ConcentrationModel';
  import { LevelStrategyModelConfig } from '@/models/level/levelStrategyModels';
  import BondAllocation from '@/views/compentList/concentrationGroupingModel/BondAllocation.vue';
  import ConcentrationFundConfig from '@/views/compentList/concentrationGroupingModel/ConcentrationFundConfig.vue';
  import ConcentrationGroupingModelResult from '@/views/compentList/concentrationGroupingModel/ConcentrationGroupingModelResult.vue';
  import ConcentrationGroupingModelResultCompare from '@/views/compentList/concentrationGroupingModel/ConcentrationGroupingModelResultCompare.vue';
  import DefaultConcentrationGroup from '@/views/compentList/concentrationGroupingModel/DefaultConcentrationGroup.vue';
  import SelectableOptionConfig from '@/views/compentList/concentrationGroupingModel/SelectableOptionConfig.vue';
  import StockAllocation from '@/views/compentList/newConcentrationGroupingModel/StockAllocation.vue';
  import StockLevelNumChart from '@/views/compentList/newConcentrationGroupingModel/StockLevelNumChart.vue';
  import ConcentrationModelSetup from '@/views/compentList/tarkinConcentrationSetting/ConcentrationModelSetup.vue';

  // 类型与接口声明
  interface Props {
    ifUser?: boolean;
    buttonHiding?: boolean;
  }

  // Props / Emits
  const props = withDefaults(defineProps<Props>(), {
    ifUser: false,
    buttonHiding: false,
  });

  // 组件注册
  defineOptions({
    name: 'ConcentrationGroupingModel',
  });

  // 第三方 Hook / Composable 引用
  const route = useRoute(); // 路由信息获取
  const router = useRouter(); // 路由导航
  const message = useMessage(); // 消息提示

  // 本地 ref / reactive / computed 定义
  const ifCalculating = ref<number>(); // 计算状态标识
  const refreshTimer = ref<NodeJS.Timeout | null>(null); // 自动刷新定时器
  const isAutoRefreshing = ref<boolean>(false); // 自动刷新状态
  const updateKey = ref<number>(0); // 组件更新键
  const addShowModal = ref<boolean>(false); // 新增模型弹窗显示状态
  const optionConfigModal = ref<boolean>(false); // 选项配置弹窗显示状态
  const showModelResultTable = ref<boolean>(false); // 模型结果表格显示状态
  const showModelResultCompareTable = ref<boolean>(false); // 模型结果比较表格显示状态
  const calculateLoading = ref<boolean>(false); // 计算按钮加载状态
  const currentModel = ref<any>({}); // 当前选中的模型
  const userLevelSrtModelList = ref<LevelStrategyModelConfig[]>([]); // 用户评级策略模型列表
  const ConcentrationModelSetupRef = ref(); // 模型设置组件引用
  const levelModelId = ref<number | null>(null); // 评级模型ID
  const groupOption = ref<CounterSecurityCategoryDictDO[]>([]); // 分组选项配置

  // 计算属性：判断当前选中模型是否为只读
  const isCurrentModelReadonly = computed(() => {
    return currentModel.value?.ifModifyPermission === 1;
  });

  // 表单数据
  const addForm = reactive<ConcentrationModelConfigDO>({
    remark: null,
    modelName: null,
    relatedLevelStrategyModelId: null,
    modelId: null,
    mainStatus: 1,
    enableStatus: 1,
    createTime: null,
    updateTime: null,
    userId: null,
    deleteStatus: null,
  });
  // 业务函数 / 方法（从公用工具函数到私有方法，自上而下由通用到专用）

  /**
   * 保存当前模型状态到localStorage
   * 用于页面刷新后恢复选中的模型
   */
  const saveCurrentModelState = () => {
    if (currentModel.value?.modelId) {
      localStorage.setItem('selectedConcentrationModelId', currentModel.value.modelId.toString());
    }
  };

  /**
   * 获取集中度分组选项配置
   */
  const getSelectableGroupOption = async () => {
    const { code, data, msg } = await querySelectableGroupOption();
    if (code === 200) {
      groupOption.value = data;
    } else {
      message.error(msg);
    }
  };

  /**
   * 获取用户模型列表
   */
  const getUserLevelModelList = async () => {
    const { code, data, msg } = await getUserLevelSrtModelList();
    if (code === 200) {
      userLevelSrtModelList.value = data;
      const modelList = data.filter((item) => item.enableStatus == 0);
      if (modelList.length > 0) {
        levelModelId.value = modelList[0].modelId;
        ifCalculating.value = modelList[0].ifCalculating;
      }
    } else {
      message.error(msg);
    }
  };

  /**
   * 关闭模型处理
   */
  const handleCloseModel = () => {
    // 处理模型关闭逻辑
  };

  /**
   * 选项配置弹窗
   */
  const optionConfig = () => {
    optionConfigModal.value = true;
  };

  /**
   * 选项配置弹窗关闭处理
   */
  const handleOptionConfigModalClose = () => {
    optionConfigModal.value = false;
    getSelectableGroupOption();
  };

  /**
   * 新增模型
   */
  const addNewRule = () => {
    addForm.relatedLevelStrategyModelId = null;
    addForm.modelName = null;
    addForm.modelId = null;
    addForm.remark = null;
    addForm.mainStatus = 1;
    addForm.enableStatus = 1;
    addShowModal.value = true;
  };

  /**
   * 编辑模型
   */
  const editConcentration = (row: any) => {
    addForm.modelName = row.modelName;
    addForm.relatedLevelStrategyModelId = row.relatedLevelStrategyModelId;
    addForm.modelId = row.modelId;
    addForm.remark = row.remark;
    addShowModal.value = true;
  };

  /**
   * 切换模型结果显示
   */
  const handleModelResult = () => {
    showModelResultTable.value = !showModelResultTable.value;
    // 如果显示模型结果，则隐藏模型结果比较
    if (showModelResultTable.value) {
      showModelResultCompareTable.value = false;
    }
  };

  /**
   * 切换模型结果比较显示
   */
  const handleModelResultCompare = () => {
    showModelResultCompareTable.value = !showModelResultCompareTable.value;
    // 如果显示模型结果比较，则隐藏模型结果
    if (showModelResultCompareTable.value) {
      showModelResultTable.value = false;
    }
  };
  /**
   * 计算模型的处理函数
   * 点击"点击计算"按钮时调用，这会触发页面刷新
   * 与模型切换不同，这里需要刷新页面来重新加载计算结果
   */
  const handleCalculateModel = async () => {
    calculateLoading.value = true;
    const { code, msg } = await calculateConcentraGroupModel(currentModel.value.modelId);
    calculateLoading.value = false;

    if (code === 200) {
      message.success(msg);
      // 保存当前模型ID，确保页面刷新后能恢复到当前模型
      saveCurrentModelState();

      // 更新URL参数并刷新页面
      const currentModelId = currentModel.value.modelId;
      router
        .push({
          path: route.path,
          query: { ...route.query, modelId: currentModelId },
        })
        .then(() => {
          location.reload(); // 刷新页面以加载最新的计算结果
        });
    } else {
      message.error(msg);
    }
  };

  /**
   * 保存新增/编辑模型
   */
  const saveNewRule = async () => {
    if (!addForm.modelName) {
      message.error('请输入模型名称');
      return;
    }
    if (!addForm.relatedLevelStrategyModelId) {
      message.error('请选择关联评级策略模型');
      return;
    }

    const { code, msg } = await updateConcentrationModel(addForm);
    if (code === 200) {
      message.success(msg);
      addShowModal.value = false;
      ConcentrationModelSetupRef.value?.get_ConcentrationModels(false);
      // 重置表单
      addForm.remark = null;
      addForm.modelName = null;
      addForm.relatedLevelStrategyModelId = null;
      addForm.modelId = null;
    } else {
      message.error(msg);
    }
  };

  /**
   * 更新启用状态
   */
  const updateEnableStatus = async (val: boolean, row: any) => {
    row.enableStatus = val ? 0 : 1;
    const { code, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels(false);

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  };

  /**
   * 更新主备状态
   */
  const updateMainStatus = async (val: boolean, row: any) => {
    row.mainStatus = val ? 0 : 1;
    const { code, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels(false);

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  };

  /**
   * 检查当前模型的计算状态并控制自动刷新
   * ifCalculating = 0: 正在计算中，启动自动刷新
   * ifCalculating = 1: 计算完成，停止自动刷新
   */
  const checkCalculatingStatus = () => {
    if (currentModel.value?.ifCalculating == 0) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  };

  /**
   * 启动自动刷新定时器
   * 当模型正在计算时（ifCalculating=0），每10秒检查一次计算状态
   * 直到计算完成（ifCalculating=1）为止
   */
  const startAutoRefresh = () => {
    if (refreshTimer.value) return; // 避免重复启动

    isAutoRefreshing.value = true;
    refreshTimer.value = setInterval(async () => {
      console.log('自动刷新检查计算状态...');
      // 保存当前选中的模型ID，防止刷新后丢失选中状态
      const currentModelId = currentModel.value?.modelId;

      // 重新获取模型列表来检查最新的计算状态
      await ConcentrationModelSetupRef.value?.get_ConcentrationModels(false);

      // 恢复之前选中的模型
      if (currentModelId) {
        setTimeout(() => {
          // 通知子组件选择特定模型（内部选择，不触发emit事件）
          ConcentrationModelSetupRef.value?.selectModelById(currentModelId);

          // 从子组件的数据列表中找到对应的模型数据并更新父组件的currentModel
          const childComponent = ConcentrationModelSetupRef.value;
          if (childComponent && childComponent.dataList) {
            const targetModel = childComponent.dataList.find(
              (item) => item.modelId === currentModelId
            );
            if (targetModel) {
              console.log('自动刷新: 更新currentModel', targetModel);
              currentModel.value = targetModel;
              // 重新检查计算状态，如果计算完成则停止自动刷新
              checkCalculatingStatus();
            }
          }
        }, 100);
      }
    }, 10000); // 每10秒检查一次
  };

  /**
   * 停止自动刷新定时器
   * 当模型计算完成时调用，清理定时器资源
   */
  const stopAutoRefresh = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value);
      refreshTimer.value = null;
    }
    isAutoRefreshing.value = false;
  };

  /**
   * 用户点击模型卡片时的处理函数
   * 这是模型切换的核心逻辑，区别于页面刷新后的状态恢复
   */
  const clickModel = (row: any) => {
    console.log('clickModel called with:', row.modelId, row.modelName);

    // 更新当前选中的模型数据
    currentModel.value = row;

    // 保存选中状态到localStorage，用于页面刷新后恢复
    saveCurrentModelState();

    // 更新URL参数（使用原生API，避免触发Vue Router的路由变化事件）
    // 这样可以避免组件重新挂载，保持页面状态的连续性
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('modelId', row.modelId.toString());
    window.history.replaceState({}, '', newUrl.toString());
    console.log('URL updated to modelId:', row.modelId);

    // 检查新选中模型的计算状态，决定是否启动自动刷新
    checkCalculatingStatus();
  };

  /**
   * 恢复选中的模型状态（仅在页面刷新/重新挂载时使用）
   * 这个函数与clickModel的区别：
   * - clickModel: 用户主动点击模型卡片，实时切换
   * - restoreSelectedModel: 页面刷新后，根据保存的状态恢复之前的选择
   */
  const restoreSelectedModel = async () => {
    console.log('restoreSelectedModel called');

    // 优先从URL参数获取modelId（用户可能直接访问带参数的URL）
    const urlModelId = route.query.modelId;
    // 其次从localStorage获取（页面刷新后的备份）
    const savedModelId = localStorage.getItem('selectedConcentrationModelId');

    console.log('restoreSelectedModel: urlModelId:', urlModelId, 'savedModelId:', savedModelId);

    // URL参数优先级更高，因为用户可能通过URL直接访问特定模型
    const targetModelId = urlModelId || savedModelId;

    if (targetModelId) {
      console.log('restoreSelectedModel: 恢复模型', targetModelId);

      // 等待子组件完全加载完成，确保数据列表已获取
      await new Promise((resolve) => setTimeout(resolve, 200));

      // 通知子组件选择特定模型（内部选择，不触发emit事件）
      ConcentrationModelSetupRef.value?.selectModelById(Number(targetModelId));

      // 从子组件的数据列表中找到对应的模型数据并更新父组件状态
      const childComponent = ConcentrationModelSetupRef.value;
      if (childComponent && childComponent.dataList) {
        const targetModel = childComponent.dataList.find(
          (item) => item.modelId === Number(targetModelId)
        );
        if (targetModel) {
          console.log('restoreSelectedModel: 找到目标模型，更新currentModel', targetModel);
          currentModel.value = targetModel;
          // 检查恢复的模型是否正在计算，如果是则启动自动刷新
          checkCalculatingStatus();
        } else {
          console.log('restoreSelectedModel: 未找到目标模型，数据列表:', childComponent.dataList);
        }
      } else {
        console.log('restoreSelectedModel: 子组件或数据列表不存在');
      }
    } else {
      console.log('restoreSelectedModel: 没有找到要恢复的模型ID');
    }
  };

  // watch / watchEffect
  watch(
    () => currentModel.value?.ifCalculating,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        checkCalculatingStatus();
      }
    },
    { immediate: true }
  );

  // 生命周期钩子
  onActivated(() => {
    console.log('组件激活！');
  });

  onMounted(() => {
    getUserLevelModelList();
    getSelectableGroupOption();
    updateKey.value++;

    // 延迟恢复选中状态，确保子组件已加载
    setTimeout(() => {
      restoreSelectedModel();
    }, 200);
  });

  onUnmounted(() => {
    stopAutoRefresh();
  });
</script>

<style lang="less">
  #newConcentrationGroupingModel {
    /* 整体容器样式 */

    .concentration-model-container {
      padding: 0;
      min-height: 100vh;
    }

    /* 卡片间距 - 压缩间距 */

    .card-spacing {
      height: 16px;
    }

    .content-spacing {
      height: 12px;
    }

    /* 模型列表卡片样式 - 移除动画 */

    .model-list-card {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(45, 140, 240, 0.1);
      border: 1px solid rgba(45, 140, 240, 0.1);
    }

    /* 压缩卡片头部高度 */

    .model-list-card :deep(.n-card-header) {
      padding: 12px 20px !important;
    }

    .model-list-card :deep(.n-card__action) {
      padding: 8px 20px !important;
    }

    /* 卡片头部样式 */

    .card-header {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .model-list-title {
      font-size: 22px !important; /* 压缩字体 */
      font-weight: 700 !important;
      color: #1a202c;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px; /* 减少间距 */
      line-height: 1.2; /* 压缩行高 */
    }

    .help-icon {
      color: #64748b;
      cursor: pointer;
    }

    .help-content {
      font-size: 18px;
      font-weight: 500;
      color: #374151;
      padding: 8px 12px;
    }

    /* 主要内容卡片样式 - 移除动画 */

    .main-content-card {
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(45, 140, 240, 0.1);
      border: 1px solid rgba(45, 140, 240, 0.1);
    }

    /* 压缩主内容卡片头部高度 */

    .main-content-card :deep(.n-card-header) {
      padding: 20px 24px 5px 24px !important;
    }

    /* 主内容三列布局 */

    .main-content-layout {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      gap: 32px;
      align-items: center;
      width: 100%;
      min-height: 200px;
    }

    /* 左侧区域：模型信息 */

    .left-section {
      display: flex;
      flex-direction: column;
      gap: 12px;
      justify-content: center;
    }

    .current-model-title {
      font-size: 28px !important;
      font-weight: 700 !important;
      color: #1a202c;
      margin: 0;
      line-height: 1.3;
    }

    .calculate-time {
      font-size: 16px;
      font-weight: 500;
      color: #64748b;
      opacity: 0.8;
      margin-top: 8px;
    }

    .auto-refresh-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      padding: 6px 12px;
      background: rgba(45, 140, 240, 0.1);
      border-radius: 8px;
      border: 1px solid rgba(45, 140, 240, 0.2);
    }

    .refresh-icon {
      font-size: 16px;
      color: #2d8cf0;
      animation: spin 2s linear infinite;
    }

    .refresh-text {
      font-size: 14px;
      font-weight: 500;
      color: #2d8cf0;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    /* 中央区域：统计图表 */

    .center-section {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .main-stock-chart {
      width: 100%;
      max-width: 600px;
      border-radius: 12px;
      overflow: hidden;
    }

    /* 右侧区域：操作按钮 */

    .right-section {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .action-buttons-vertical {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 100%;
    }

    /* 按钮样式 - 适应垂直布局 */

    .action-button {
      height: 50px !important;
      width: 100%;
      font-size: 18px !important;
      font-weight: 700 !important;
      border-radius: 12px;
      padding: 0 16px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      white-space: nowrap;
      text-align: center;
    }

    /* 图标切换动画效果 */

    .action-button :deep(.n-icon) {
      font-size: 18px !important;
      transition: all 0.2s ease-in-out;
    }

    .action-button:hover :deep(.n-icon) {
      transform: scale(1.1);
    }

    .primary-button {
      background: linear-gradient(135deg, #2d8cf0 0%, #409eff 100%);
      border: none;
      color: white;
    }

    .success-button {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      border: none;
      color: white;
    }

    .result-button {
      background: linear-gradient(135deg, #409eff 0%, #5aafff 100%);
      border: none;
      color: white;
    }

    .compare-button {
      background: linear-gradient(135deg, #e6a23c 0%, #f0b90b 100%);
      border: none;
      color: white;
    }

    .calculate-button {
      background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      border: 2px solid #67c23a;
      color: #67c23a;
      background: white;
    }

    /* 禁用状态 */

    .action-button:disabled {
      background: #e5e7eb !important;
      color: #9ca3af !important;
      cursor: not-allowed;
      box-shadow: none !important;
    }

    /* 标签页样式 - 移除动画 */

    .main-tabs {
      margin-top: 2px; /* 减少上边距 */
    }

    .main-tabs :deep(.n-tabs-tab) {
      font-size: 20px !important;
      font-weight: 700 !important;
      padding: 12px 28px !important; /* 压缩内边距 */
      border-radius: 12px 12px 0 0;
    }

    .main-tabs :deep(.n-tabs-tab.n-tabs-tab--active) {
      background: linear-gradient(135deg, #2d8cf0 0%, #409eff 100%);
      color: white !important;
      font-weight: 700 !important;
    }

    .tab-content {
      padding: 18px 0; /* 压缩内边距 */
    }

    /* 模型结果容器 */

    .model-result-container {
      padding: 18px 0; /* 压缩内边距 */
    }

    /* 模型结果比较容器 */

    .model-result-compare-container {
      padding: 18px 0; /* 压缩内边距 */
    }

    /* 加载动画样式 */

    .model-setup-spinner :deep(.n-spin-content) {
      min-height: 200px;
    }

    /* 模态框样式 */

    .custom-modal {
      border-radius: 16px;
      overflow: hidden;
    }

    .custom-modal :deep(.n-card) {
      border-radius: 16px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    .custom-modal :deep(.n-card-header) {
      background: linear-gradient(135deg, #2d8cf0 0%, #409eff 100%);
      color: white;
      font-size: 22px !important;
      font-weight: 700 !important;
      padding: 20px 32px;
    }

    .modal-content {
      padding: 32px;
    }

    /* 表单样式 */

    .model-form {
      margin-bottom: 32px;
    }

    .form-item {
      margin-bottom: 24px;
    }

    .form-item :deep(.n-form-item-label) {
      font-size: 18px !important;
      font-weight: 600 !important;
      color: #374151;
    }

    .form-input,
    .form-select {
      height: 48px !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      border-radius: 8px;
    }

    .form-input :deep(.n-input__input-el),
    .form-select :deep(.n-base-selection-input) {
      font-size: 18px !important;
      font-weight: 500 !important;
    }

    .form-textarea {
      font-size: 18px !important;
      font-weight: 500 !important;
      border-radius: 8px;
    }

    .form-textarea :deep(.n-input__textarea-el) {
      font-size: 18px !important;
      font-weight: 500 !important;
      min-height: 120px;
    }

    /* 模态框按钮样式 */

    .modal-actions {
      border-top: 1px solid #e5e7eb;
      padding-top: 24px;
      margin-top: 24px;
    }

    .modal-button {
      height: 48px !important;
      min-width: 120px;
      font-size: 18px !important;
      font-weight: 700 !important;
      border-radius: 8px;
      padding: 0 24px !important;
    }

    .cancel-button {
      background: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;
    }

    .confirm-button {
      background: linear-gradient(135deg, #2d8cf0 0%, #409eff 100%);
      border: none;
      color: white;
    }

    /* 响应式设计 */
    @media (max-width: 1400px) {
      .main-content-layout {
        grid-template-columns: 1fr 1.5fr 1fr;
        gap: 24px;
      }

      .main-stock-chart {
        max-width: 500px;
      }
    }

    @media (max-width: 1200px) {
      .main-content-layout {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
      }

      .left-section {
        align-items: center;
      }

      .action-buttons-vertical {
        flex-direction: row;
        justify-content: center;
        gap: 12px;
      }

      .action-button {
        width: auto;
        min-width: 160px;
      }
    }

    @media (max-width: 768px) {
      .model-list-title {
        font-size: 20px !important;
      }

      .current-model-title {
        font-size: 24px !important;
      }

      .action-button {
        height: 44px !important;
        font-size: 18px !important;
        min-width: 120px;
      }

      .add-model-modal,
      .option-config-modal {
        width: 90vw;
        max-width: 500px;
      }
    }
  }
</style>
