<template>
  <!--  集中度分组模型 -->
  <div>
    <n-card>
      <template #header>
        <n-h2 class="mb-0" prefix="bar" type="error">
          模型列表
          <n-popover>
            <template #trigger>
              <n-icon size="20">
                <HelpCircleOutline />
              </n-icon>
            </template>
            <div style="font-size: 18px"> 点击下方模型即可编辑。</div>
          </n-popover>
        </n-h2>
      </template>
      <template #header-extra>
        <n-button v-show="!buttonHiding" type="primary" @click="addNewRule"> 新增模型</n-button>
      </template>
      <template #action>
        <n-space justify="end">
          <n-button v-show="!buttonHiding" type="success" @click="optionConfig">
            选项配置
          </n-button>
        </n-space>
      </template>

      <ConcentrationModelSetup
        ref="ConcentrationModelSetupRef"
        :buttonHiding="buttonHiding"
        :ifUser="ifUser"
        @click-model="clickModel"
        @edit-concentration="editConcentration"
        @update-enable-status="updateEnableStatus"
        @update-main-status="updateMainStatus"
        @colse-model="modelName = null"
      />
    </n-card>
    <br />
    <n-card>
      <template #header>
        <n-h1 class="mb-0" prefix="bar">
          {{ currentModel.modelName }}
        </n-h1>
      </template>
      <template #header-extra>
        <n-space>
          <n-button
            :disabled="ifCalculating == 0"
            :type="modelResult ? 'info' : 'success'"
            @click="handleModelResult"
          >
            {{
              ifCalculating == 0
                ? '模型结果正在计算中'
                : modelResult
                ? '查看配置信息'
                : '查看模型结果'
            }}
          </n-button>
          <n-button
            v-if="ifCalculating != 0"
            :loading="calculateLoading"
            secondary
            strong
            type="success"
            @click="handleCalculateModel"
          >
            {{ calculateLoading ? '计算中' : '点击计算' }}
          </n-button>
        </n-space>
      </template>
      <n-tabs v-show="!modelResult" :key="modelId" animated type="card">
        <n-tab-pane :name="SecType.STOCK" display-directive="show" tab="股票">
          <StockAllocation
            :group-list="groupOption"
            :levelModelId="levelModelId"
            :modelId="currentModel.modelId"
            :secType="SecType.STOCK"
          />
        </n-tab-pane>
        <n-tab-pane :name="SecType.FUND" display-directive="show" tab="基金">
          <DefaultConcentrationGroup
            :group-list="groupOption"
            :modelId="currentModel.modelId"
            :secType="SecType.FUND"
          />
          <ConcentrationFundConfig :group-list="groupOption" :modelId="currentModel.modelId" />
          <!--          <FundAllocation :modelId="currentModel.modelId" :secType="SecType.FUND" />-->
        </n-tab-pane>

        <n-tab-pane :name="SecType.BOND" display-directive="show" tab="债券">
          <DefaultConcentrationGroup
            :group-list="groupOption"
            :modelId="currentModel.modelId"
            :secType="SecType.BOND"
          />
          <br />

          <BondAllocation
            :group-list="groupOption"
            :modelId="currentModel.modelId"
            :secType="SecType.BOND"
          />
        </n-tab-pane>
      </n-tabs>
      <!--    模型结果-->
      <ConcentrationGroupingModelResult v-if="modelResult" :model-id="currentModel.modelId" />
    </n-card>

    <n-modal
      v-model:show="addShowModal"
      :bordered="false"
      :title="addForm.modelId ? '编辑模型' : '新增模型'"
      class="custom-card w-[500px]"
      preset="card"
      size="huge"
    >
      <n-form ref="formRef" label-placement="left">
        <n-form-item label="模型名称">
          <n-input v-model:value="addForm.modelName" placeholder="请输入模型名称" />
        </n-form-item>
        <n-form-item label="模型备注">
          <n-input v-model:value="addForm.remark" placeholder="请输入备注" type="textarea" />
        </n-form-item>
      </n-form>
      <br />
      <br />
      <n-space justify="center">
        <n-button @click="addShowModal = false"> 取消</n-button>
        <n-button type="primary" @click="saveNewRule"> 确定</n-button>
      </n-space>
    </n-modal>

    <n-modal
      v-model:show="optionConfigModal"
      :bordered="false"
      class="custom-card w-[830px]"
      preset="card"
      size="huge"
      title="选项配置"
    >
      <SelectableOptionConfig :group-list="groupOption" @close="handleOptionConfigModalClose" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import ConcentrationModelSetup from '@/views/compentList/tarkinConcentrationSetting/ConcentrationModelSetup.vue';
  import ConcentrationSettingCard from '@/views/compentList/tarkinConcentrationSetting/ConcentrationSettingCard.vue';
  import FundAllocation from '@/views/compentList/concentrationGroupingModel/FundAllocation.vue';
  import SelectableOptionConfig from '@/views/compentList/concentrationGroupingModel/SelectableOptionConfig.vue';
  import ConcentrationFundConfig from '@/views/compentList/concentrationGroupingModel/ConcentrationFundConfig.vue';
  import BondAllocation from '@/views/compentList/concentrationGroupingModel/BondAllocation.vue';
  import StockAllocation from '@/views/compentList/concentrationGroupingModel/StockAllocation.vue';
  import DefaultConcentrationGroup from '@/views/compentList/concentrationGroupingModel/DefaultConcentrationGroup.vue';
  import ConcentrationGroupingModelResult from '@/views/compentList/concentrationGroupingModel/ConcentrationGroupingModelResult.vue';
  import { ref, onMounted, nextTick, reactive, watch } from 'vue';

  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { useMessage } from 'naive-ui';
  import { updateConcentrationModel } from '@/api/marginTrading/concentration/concentrationModelApi';
  import { SecType } from '@/enums/secEnum';
  import { getUserLevelSrtModelList } from '@/api/levelStrategy/model/levelStrategyModelApi';
  import { ConcentrationModelConfigDO } from '@/models/marginTrading/model/ConcentrationModel';
  import { calculateModelResult } from '@/api/tailored/xingye/marginRatioModel/marginRatioModelResultApi';
  import {
    calculateConcentraGroupModel,
    querySelectableGroupOption,
  } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import { CounterSecurityCategoryDictDO } from '@/models/common/baseResponse';

  defineOptions({
    name: 'ConcentrationGroupingModel',
  });
  const props = defineProps({
    ifUser: { type: Boolean, default: false },
    buttonHiding: { type: Boolean, default: false },
  });
  const ifCalculating = ref<string | null>(null);

  const stockType = ref<string[]>();
  const modelId = ref<number | null>();
  const modelName = ref<string | null>();
  const addShowModal = ref(false);
  const optionConfigModal = ref(false);
  const modelResult = ref(false);
  const calculateLoading = ref(false);
  const showModal = ref(false);
  const currentModel = ref<any>({});
  const ConcentrationModelSetupRef = ref();
  const form = ref(false);
  const levelModelId = ref(null);
  const groupOption = ref<CounterSecurityCategoryDictDO[]>([]);
  const message = useMessage();
  const addForm = reactive<ConcentrationModelConfigDO>({
    remark: null,
    modelName: null,
    modelId: null,
    mainStatus: 1,
    enableStatus: 1,
    createTime: null,
    updateTime: null,
    userId: null,
    deleteStatus: null,
  });
  //选项配置
  const optionConfig = () => {
    optionConfigModal.value = true;
  };
  const handleOptionConfigModalClose = () => {
    optionConfigModal.value = false;
    query_SelectableGroupOption();
  };

  //新增模板
  const addNewRule = () => {
    addForm.modelName = null;
    addForm.modelId = null;
    addForm.remark = null;
    addForm.mainStatus = 1;
    addForm.enableStatus = 1;
    addShowModal.value = true;
  };
  //编辑模板
  const editConcentration = (row) => {
    addForm.modelName = row.modelName;
    addForm.modelId = row.modelId;
    addForm.remark = row.remark;
    addShowModal.value = true;
  };
  //获取集中度分组选项配置
  const query_SelectableGroupOption = async () => {
    let { code, data, msg } = await querySelectableGroupOption();
    if (code === 200) {
      groupOption.value = data;
    } else {
      message.error(msg);
    }
  };

  // 模型结果
  const handleModelResult = async () => {
    modelResult.value = !modelResult.value;
  };
  // 计算模型
  const handleCalculateModel = async () => {
    calculateLoading.value = true;
    const { code, msg, data } = await calculateConcentraGroupModel(currentModel.value.modelId);
    calculateLoading.value = false;

    if (code === 200) {
      message.success(msg);
      location.reload();
    } else {
      message.error(msg);
    }
  };
  //新增模板确定
  const saveNewRule = async () => {
    if (!addForm.modelName) {
      message.success('请输入模型名称');
      return;
    }
    if (!addForm.remark) {
      message.success('请输入备注');
      return;
    }
    const { code, data, msg } = await updateConcentrationModel(addForm);
    if (code === 200) {
      message.success(msg);
      addShowModal.value = false;
      ConcentrationModelSetupRef.value?.get_ConcentrationModels();
      addForm.remark = null;
      addForm.modelName = null;
      addForm.modelId = null;
    } else {
      message.error(msg);
    }
  };

  //更新启用状态
  const updateEnableStatus = async (val, row) => {
    row.enableStatus = val ? 0 : 1;
    const { code, data, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels();

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  }; //更新主备状态
  const updateMainStatus = async (val, row) => {
    row.mainStatus = val ? 0 : 1;
    const { code, data, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels();

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  };

  //获取用户模型列表
  const get_UserLevelSrtModelList = async () => {
    let { code, data, msg } = await getUserLevelSrtModelList();
    if (code === 200) {
      let modelList = data.filter((item) => item.enableStatus == 0);
      if (modelList.length > 0) {
        levelModelId.value = modelList[0].modelId;
        ifCalculating.value = modelList[0].ifCalculating;
      }
    } else {
      message.error(msg);
    }
  };

  //点击某个模型
  const clickModel = (row) => {
    currentModel.value = row;
    //console.log(row)
  };
  onMounted(() => {
    get_UserLevelSrtModelList();
    query_SelectableGroupOption();
  });
</script>

<style scoped></style>
