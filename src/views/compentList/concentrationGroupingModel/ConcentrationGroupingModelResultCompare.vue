<!--  集中度分组模型 / 模型结果比较表 -->

<template>
  <div>
    <!-- 查询条件区域 -->
    <n-space :size="16" class="mb-4">
      <n-button secondary strong>新模型结果日期</n-button>
      <n-date-picker
        v-model:formatted-value="queryForm.newDate"
        :is-date-disabled="disableDateValue"
        class="w-[200px]"
        placeholder="请选择新日期"
        type="date"
        update-value-on-close
        value-format="yyyy-MM-dd"
      />

      <n-button secondary strong>旧模型结果日期</n-button>
      <n-date-picker
        v-model:formatted-value="queryForm.oldDate"
        :is-date-disabled="disableDateValue"
        class="w-[200px]"
        placeholder="请选择旧日期"
        type="date"
        update-value-on-close
        value-format="yyyy-MM-dd"
      />
    </n-space>

    <!-- 表格区域 -->
    <n-card bordered>
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="4">
          <n-form ref="formRef" class="search-form" inline label-placement="left">
            <n-space :size="16" align="center" class="form-controls-wrapper">
              <n-form-item class="search-form-item" label="证券名称">
                <SecurityInfoSearch @get-value="getStockValue" />
              </n-form-item>

              <n-form-item class="search-form-item" label="证券品种">
                <n-select
                  v-model:value="formInline.secType"
                  :options="secOptions"
                  clearable
                  placeholder="请选择证券品种"
                  style="width: 200px"
                />
              </n-form-item>

              <n-form-item class="search-form-item" label="过滤不同记录">
                <n-switch v-model:value="formInline.filterDifferRecord" style="width: 60px" />
              </n-form-item>

              <n-form-item class="button-form-item">
                <n-space :size="12">
                  <n-button
                    attr-type="button"
                    style="width: 110px; height: 34px"
                    type="info"
                    @click="onSubmit"
                  >
                    <template #icon>
                      <n-icon :component="Search" />
                    </template>
                    查询
                  </n-button>
                  <n-button
                    :loading="exportLoading"
                    style="width: 110px; height: 34px"
                    type="warning"
                    @click="exportBtn"
                  >
                    <template #icon>
                      <n-icon :component="CloudDownload" />
                    </template>
                    导出
                  </n-button>
                </n-space>
              </n-form-item>
            </n-space>
          </n-form>
        </n-grid-item>
        <n-grid-item span="4">
          <n-data-table
            :columns="columns"
            :data="dataTableList"
            :loading="loading"
            :max-height="550"
            :min-height="550"
            :pagination="false"
            :row-key="(item) => item.secCode"
            :scroll-x="1400"
            :single-line="false"
            bordered
            @update:sorter="handleSorterChangeWrapper"
          />

          <n-space justify="center">
            <n-space justify="center">
              <n-pagination
                v-model:page="pageRequest.current"
                v-model:page-size="pageRequest.size"
                :item-count="total"
                :page-sizes="[10, 20, 50, 100, 300]"
                class="mt-5"
                show-size-picker
                @update:page="updatePage"
                @update:page-size="updatePageSize"
              >
                <template #suffix> 共 {{ total }} 条</template>
              </n-pagination>
            </n-space>
          </n-space>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns, NIcon } from 'naive-ui';
  // 图标导入
  import { Search, CloudDownload } from '@vicons/ionicons5';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import SecurityInfoSearch from '@/components/SecuritySearch/SecurityInfoSearch.vue';
  import { openStockArchives } from '@/utils/goToArchives';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { getEnumOptions, getEnumProperties } from '@/enums/baseEnum';
  import { SecTypeInfo } from '@/enums/secEnum';
  import { exportExcel } from '@/api/system/https';
  import { formatTime } from '@/utils/common/date/dateUtil';
  import {
    queryResultDateOption,
    queryConcentraGroupModelResultCompare,
  } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import type { ConcentraGroupModelResultCompareVO } from '@/models/marginTrading/model/ConcentraGroupModel';

  // Props
  interface Props {
    modelId: number | string;
  }

  const props = withDefaults(defineProps<Props>(), {
    modelId: '',
  });
  const { modelId } = toRefs(props);

  // Composables
  const message = useMessage();
  const userStore = useUserStore();

  // Reactive data
  const loading = ref(false);
  const exportLoading = ref(false);
  const dataTableList = ref<ConcentraGroupModelResultCompareVO[]>([]);
  const dateOptions = ref<string[]>([]);

  // Query form
  const queryForm = reactive({
    newDate: null as string | null,
    oldDate: null as string | null,
  });

  const formInline = reactive({
    secCode: null,
    secType: null,
    filterDifferRecord: true,
  });

  // Options
  const secOptions = getEnumOptions(SecTypeInfo);

  // Table columns
  const columns: DataTableColumns = [
    {
      title: '序号',
      align: 'center',
      key: 'index',
      width: '70px',
      render(row, index) {
        return index + 1;
      },
    },
    {
      title: '证券代码',
      align: 'center',
      key: 'secCode',
      width: '120px',
      sorter: true,
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openStockArchives(row.secCode, row.secName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.secCode }
        );
      },
    },
    {
      align: 'center',
      width: '150px',
      title: '证券名称',
      key: 'secName',
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openStockArchives(row.secCode, row.secName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.secName }
        );
      },
    },
    {
      title: '证券品种',
      align: 'center',
      key: 'secType',
      width: '100px',
      sorter: true,
      render(row) {
        const { label, type } = getEnumProperties(
          SecTypeInfo,
          row.secType == null ? '-' : row.secType
        );
        return h(
          NTag,
          {
            type: type as 'info' | 'warning' | 'error' | 'success',
          },
          { default: () => label }
        );
      },
    },
    {
      align: 'center',
      width: '130px',
      sorter: true,
      title: '新模型结果',
      render(row) {
        return h(
          'span',
          {
            style: { color: setLevelColor(row.newModelResult) },
          },
          { default: () => row.newModelResult || '-' }
        );
      },
      key: 'newModelResult',
    },
    {
      align: 'center',
      width: '130px',
      sorter: true,
      title: '旧模型结果',
      render(row) {
        return h(
          'span',
          {
            style: { color: setLevelColor(row.oldModelResult) },
          },
          { default: () => row.oldModelResult || '-' }
        );
      },
      key: 'oldModelResult',
    },
    {
      align: 'center',
      width: '120px',
      title: '变化状态',
      key: 'changeStatus',
      render(row) {
        let status = '';
        let type: 'info' | 'warning' | 'error' | 'success' = 'info';

        if (row.newModelResult === row.oldModelResult) {
          status = '无变化';
          type = 'info';
        } else if (!row.oldModelResult) {
          status = '新增';
          type = 'success';
        } else if (!row.newModelResult) {
          status = '删除';
          type = 'error';
        } else {
          status = '变化';
          type = 'warning';
        }

        return h(NTag, { type }, { default: () => status });
      },
    },
    { align: 'center', width: '110px', title: '新日期', key: 'newDate' },
    { align: 'center', width: '110px', title: '旧日期', key: 'oldDate' },
  ];

  // Methods
  const getDateOptions = async () => {
    try {
      const { code, data, msg } = await queryResultDateOption(modelId.value);
      if (code === 200) {
        dateOptions.value = data || [];
        initDefaultDates();
      } else {
        message.error(msg);
      }
    } catch (error) {
      message.error('获取日期选项失败');
    }
  };

  const initDefaultDates = () => {
    if (dateOptions.value.length > 0) {
      // 新模型结果日期取最新一个日期（数组下标0的元素）
      queryForm.newDate = dateOptions.value[0];
      if (dateOptions.value.length > 1) {
        // 旧模型结果日期取第二个日期（索引为1）
        queryForm.oldDate = dateOptions.value[1];
      }
    }
    pageRequest.current = 1;
    queryModelResultCompare(pageRequest);
  };

  const disableDateValue = (date: Date) => {
    return !dateOptions.value.some((item) => item === formatTime(date, 'YYYY-MM-DD'));
  };

  const getStockValue = (id: string) => {
    formInline.secCode = id;
  };

  // 查询方法
  const queryModelResultCompare = async (pageRequest: PageRequest) => {
    if (!queryForm.newDate || !queryForm.oldDate) {
      message.warning('请选择新旧日期');
      return;
    }

    loading.value = true;
    try {
      const params = {
        ...pageRequest,
        modelId: modelId.value,
        newDate: queryForm.newDate,
        oldDate: queryForm.oldDate,
        secCode: formInline.secCode || '',
        secType: formInline.secType,
        filterDifferRecord: formInline.filterDifferRecord,
      };

      const { code, data, msg } = await queryConcentraGroupModelResultCompare(params);
      if (code === 200) {
        dataTableList.value = data.records || [];
        total.value = data.total || 0;
      } else {
        message.error(msg);
      }
    } catch (error) {
      message.error('查询失败');
    } finally {
      loading.value = false;
    }
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(queryModelResultCompare);

  const exportBtn = async () => {
    if (!queryForm.newDate || !queryForm.oldDate) {
      message.warning('请选择新旧日期');
      return;
    }

    exportLoading.value = true;
    try {
      const params = {
        modelId: modelId.value,
        newDate: queryForm.newDate,
        oldDate: queryForm.oldDate,
        secCode: formInline.secCode || '',
        secType: formInline.secType,
        filterDifferRecord: formInline.filterDifferRecord,
      };

      const { token } = userStore.getUserInfo as any;
      await exportExcel(params, '/concentra-group-model/result/compare/export', token);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    } finally {
      exportLoading.value = false;
    }
  };

  // Watch
  watch(
    () => modelId.value,
    (newVal) => {
      if (newVal) {
        getDateOptions();
      }
    },
    { immediate: true, deep: true }
  );

  watch(
    [() => queryForm.newDate, () => queryForm.oldDate],
    () => {
      if (queryForm.newDate && queryForm.oldDate) {
        pageRequest.current = 1;
        queryModelResultCompare(pageRequest);
      }
    },
    { deep: true }
  );
</script>

<style scoped>
  /* 搜索表单样式优化 */
  .search-form {
    display: flex;
    align-items: center;
  }

  .form-controls-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    width: 100%;
  }

  /* 搜索表单项样式 */
  .search-form-item :deep(.n-form-item-blank) {
    display: flex;
    align-items: center;
    min-height: 34px;
  }

  /* 按钮表单项样式 */
  .button-form-item :deep(.n-form-item-blank) {
    display: flex;
    align-items: center;
    min-height: 34px;
  }

  /* 确保所有表单项在同一水平线上 */
  :deep(.n-form-item) {
    margin-bottom: 0 !important;
    display: flex;
    align-items: center;
  }

  /* 优化表单项标签对齐 */
  :deep(.n-form-item-label) {
    display: flex;
    align-items: center;
    height: 34px;
  }

  /* 确保按钮高度一致 */
  :deep(.n-button) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Switch 组件样式调整 */
  :deep(.n-switch) {
    height: 34px;
    display: flex;
    align-items: center;
  }
</style>
