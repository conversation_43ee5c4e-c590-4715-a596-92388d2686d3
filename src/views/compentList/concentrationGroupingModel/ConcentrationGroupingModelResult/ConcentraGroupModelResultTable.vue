<!--  集中度分组模型 / 模型结果表 -->

<template>
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" class="search-form" inline label-placement="left">
          <n-space :size="16" align="center">
            <n-form-item class="search-form-item" label="证券名称">
              <SecurityInfoSearch @on-security-selected="getStockValue" />
            </n-form-item>

            <n-form-item class="button-form-item">
              <n-space :size="12">
                <n-button
                  attr-type="button"
                  style="width: 110px; height: 34px"
                  type="info"
                  @click="onSubmit"
                >
                  <template #icon>
                    <n-icon :component="Search" />
                  </template>
                  查询
                </n-button>
                <n-button
                  :loading="exportLoading"
                  style="width: 110px; height: 34px"
                  type="warning"
                  @click="exportBtn"
                >
                  <template #icon>
                    <n-icon :component="CloudDownload" />
                  </template>
                  导出
                </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :columns="columns"
          :data="dataTableList"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1100"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChangeWrapper"
        />

        <n-space justify="center">
          <n-space justify="center">
            <n-pagination
              v-model:page="pageRequest.current"
              v-model:page-size="pageRequest.size"
              :item-count="total"
              :page-sizes="[10, 20, 50, 100, 300]"
              class="mt-5"
              show-size-picker
              @update:page="updatePage"
              @update:page-size="updatePageSize"
            >
              <template #suffix> 共 {{ total }} 条</template>
            </n-pagination>
          </n-space>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script lang="ts" setup>
  import { reactive, ref, watch } from 'vue';
  import { DataTableColumns, NButton, NIcon, useMessage } from 'naive-ui';
  // 图标导入
  import { CloudDownload, Search } from '@vicons/ionicons5';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import { openStockArchives } from '@/utils/goToArchives';
  import { exportExcel } from '@/api/system/https';
  import { queryConcentraGroupModelResult } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import { ModelSecConcentraGroupHistoryDO } from '@/models/marginTrading/model/concentraGroupModes';
  import {
    createBasicColumn,
    createButtonColumn,
    createLevelColumn,
  } from '@/utils/ui/table/stockTableColumnFactory';

  const props = defineProps<{ params: any }>();
  const message = useMessage();
  const userStore = useUserStore();
  const dataTableList = ref<ModelSecConcentraGroupHistoryDO[]>([]);
  const exportLoading = ref(false);

  const columns: DataTableColumns = [
    // 序号列
    createBasicColumn('序号', 'index', {
      width: '70px',
      render(row, index) {
        return index + 1;
      },
    }),

    // 证券代码按钮列
    createButtonColumn({
      title: '证券代码',
      key: 'secCode',
      width: '120px',
      onClick: (row) => openStockArchives(row.secCode, row.secName, 1),
      buttonType: 'info',
    }),

    // 证券名称按钮列
    createButtonColumn({
      title: '证券名称',
      key: 'secName',
      width: '100px',
      onClick: (row) => openStockArchives(row.secCode, row.secName, 1),
      buttonType: 'info',
    }),

    // 集中度分组列（带颜色）
    createLevelColumn('集中度分组', 'concentrationGroup', {
      width: '130px',
      sorter: true,
    }),

    // 日期列
    createBasicColumn('日期', 'date', {
      width: '130px',
      sorter: true,
    }),
  ];

  const loading = ref(true);

  const formInline = reactive({
    secCode: null,
  });

  //查询
  const query_ConcentraGroupModelResult = async (pageRequest: PageRequest) => {
    loading.value = true;
    let { code, data, msg } = await queryConcentraGroupModelResult({
      ...pageRequest,
      ...formInline,
      ...props.params,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };
  const getStockValue = (id, name) => {
    formInline.secCode = id;
  };
  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(query_ConcentraGroupModelResult);

  const exportBtn = async () => {
    let { token } = userStore.getUserInfo;
    exportLoading.value = true;
    await exportExcel({ ...props.params }, '/concentra-group-model/result/export', token);
    exportLoading.value = false;
  };

  watch(
    () => props.params,
    (newVal, oldVal) => {
      if (newVal && newVal?.date && newVal?.modelId) {
        pageRequest.current = 1;
        query_ConcentraGroupModelResult(pageRequest);
      }
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped>
  /* 搜索表单样式优化 */
  .search-form {
    display: flex;
    align-items: center;
  }

  /* 搜索表单项样式 */
  .search-form-item :deep(.n-form-item-blank) {
    display: flex;
    align-items: center;
    min-height: 34px;
  }

  /* 按钮表单项样式 */
  .button-form-item :deep(.n-form-item-blank) {
    display: flex;
    align-items: center;
    min-height: 34px;
  }

  /* 确保所有表单项在同一水平线上 */
  :deep(.n-form-item) {
    margin-bottom: 0 !important;
    display: flex;
    align-items: center;
  }

  /* 优化表单项标签对齐 */
  :deep(.n-form-item-label) {
    display: flex;
    align-items: center;
    height: 34px;
  }

  /* 确保按钮高度一致 */
  :deep(.n-button) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
