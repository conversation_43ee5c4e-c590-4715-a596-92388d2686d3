<template>
  <div>
    <n-card bordered>
      <SecuritySearchFund width="243px" @get-value="getStockId" />
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="4">
          <n-data-table
            :columns="columns"
            :data="dataTableList"
            :loading="loading"
            :max-height="550"
            :min-height="550"
            :pagination="false"
            :row-key="(item) => item.id"
            :scroll-x="1100"
            :single-line="false"
            bordered
            @update:sorter="handleSorterChangeWrapper"
          />

          <n-space justify="center">
            <n-space justify="center">
              <n-pagination
                v-model:page="pageRequest.current"
                v-model:page-size="pageRequest.size"
                :item-count="total"
                :page-sizes="[10, 50, 100, 300]"
                class="mt-5"
                show-size-picker
                @update:page="updatePage"
                @update:page-size="updatePageSize"
              >
                <template #suffix> 共 {{ total }} 条</template>
              </n-pagination>
            </n-space>
          </n-space>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref, watch } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import { queryStockConcentraGroupMapResult } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import SecuritySearchFund from '@/components/SecuritySearch/SecuritySearchFund.vue';
  import { openStockArchives } from '@/utils/goToArchives';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import {
    createBasicColumn,
    createButtonColumn,
    createLevelColumn,
  } from '@/utils/ui/table/tableColumnFactory';
  import {
    LevelSrtModelConcentraGroupMappingVO,
    ModelSecConcentraGroupHistoryDO,
  } from '@/models/marginTrading/model/ConcentraGroupModel';
  const props = defineProps({ modelId: null });
  const message = useMessage();
  const dataTableList = ref<LevelSrtModelConcentraGroupMappingVO[]>([]);

  const columns: DataTableColumns<LevelSrtModelConcentraGroupMappingVO> = [
    createBasicColumn<LevelSrtModelConcentraGroupMappingVO>('序号', 'index', {
      width: '70px',
      render(row, index) {
        return index + 1;
      },
    }),
    createButtonColumn<LevelSrtModelConcentraGroupMappingVO>({
      title: '证券代码',
      key: 'secCode',
      width: '120px',
      onClick: (row) => openStockArchives(row.secCode, row.secName, 1),
    }),
    createButtonColumn<LevelSrtModelConcentraGroupMappingVO>({
      title: '证券名称',
      key: 'secName',
      width: '100px',
      onClick: (row) => openStockArchives(row.secCode, row.secName, 1),
    }),
    createLevelColumn<LevelSrtModelConcentraGroupMappingVO>(
      '分类评级模型结果',
      'levelSrtModelLevel',
      {
        width: '130px',
      }
    ),
    createLevelColumn<LevelSrtModelConcentraGroupMappingVO>(
      '映射的集中度分组',
      'concentrationGroup',
      {
        width: '130px',
      }
    ),
  ];

  const loading = ref(true);

  const formInline = reactive({
    secCode: null,
  });

  //查询
  const query_StockConcentraGroupMapResult = async (pageRequest: PageRequest) => {
    loading.value = true;
    let { code, data, msg } = await queryStockConcentraGroupMapResult({
      ...pageRequest,
      ...formInline,
      modelId: props.modelId,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(query_StockConcentraGroupMapResult);
  const getStockId = (id, name) => {
    formInline.secCode = id;
    pageRequest.current = 1;
    query_StockConcentraGroupMapResult(pageRequest);
  };

  watch(
    () => props.modelId,
    (newVal) => {
      if (newVal) {
        query_StockConcentraGroupMapResult(pageRequest);
      }
    },
    { immediate: true, deep: true }
  );

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
