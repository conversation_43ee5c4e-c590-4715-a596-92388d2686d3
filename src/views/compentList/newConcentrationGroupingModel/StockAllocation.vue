<template>
  <div>
    <n-spin :show="tableLoading">
      <n-space justify="space-between">
        <n-space>
          <n-button type="info" @click="handleViewResult">
            <template #icon>
              <n-icon>
                <EyeOutline v-if="!levelResult" />
                <EyeOffOutline v-else />
              </n-icon>
            </template>
            {{ !levelResult ? '查看股票集中度分组映射结果' : '关闭' }}
          </n-button>
          <n-button secondary strong type="success" @click="peerComparisonBtn">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            同业对比
          </n-button>
        </n-space>

        <n-button
          v-show="!levelResult"
          :disabled="isReadonly"
          :loading="saveLoading"
          size="large"
          type="success"
          @click="handleSave"
        >
          <template #icon>
            <n-icon>
              <SaveOutline />
            </n-icon>
          </template>
          保存配置
        </n-button>
      </n-space>
      <br />
      <n-table v-show="!levelResult" :single-line="false" class="text-center">
        <thead>
          <tr>
            <th><b>序号</b></th>
            <th><b>分类评级模型结果</b></th>
            <th><b>映射集中度分组</b></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in userLevel" :key="index">
            <td class="w-[60px]">{{ index + 1 }}</td>
            <td>
              <span :style="{ color: setLevelColor(item.level) }">{{ item.level }}</span></td
            >
            <td class="w-[450px]">
              <n-select
                v-if="levelList.length > 1 && levelList[index]"
                v-model:value="levelList[index]['targetConcentraGroup']"
                :disabled="isReadonly"
                :options="
                  groupList.map((item) => ({
                    label: item.stockgroupName,
                    value: item.stockgroupName,
                  }))
                "
                :render-label="renderLabel"
                clearable
                filterable
                placeholder="--"
                style="width: 100%"
              />
            </td>
          </tr>
        </tbody>
      </n-table>

      <!--      评级映射结果-->
      <ConcentraGroupMappingResult v-if="levelResult" :modelId="modelId" />
    </n-spin>

    <!--  同业对比  -->
    <n-modal
      v-model:show="peerComparisonModel"
      preset="card"
      style="width: 80%"
      title="同业对比详情"
    >
      <PeerComparison :ifConcentraGroupModel="true" :modelId="modelId" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, watch, h } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import StockLevelNumChart from '@/views/compentList/newConcentrationGroupingModel/StockLevelNumChart.vue';
  import PeerComparison from '@/views/myDesk/ratingStrategyModels/ratingStartegy/MappingLevelConfig/PeerComparison.vue';

  import ConcentraGroupMappingResult from '@/views/compentList/newConcentrationGroupingModel/ConcentraGroupMappingResult.vue';
  import {
    queryLevelMapping,
    saveConcentraGroupModelList,
    saveLevelMapping,
  } from '@/api/marginTrading/concentration/concentraGroupModelApi';
  import { NText, useMessage } from 'naive-ui';
  import { SaveOutline, EyeOutline, EyeOffOutline, RefreshOutline } from '@vicons/ionicons5';
  import { SecType } from '@/enums/secEnum';
  const userStore = useUserStore();
  const userLevel = userStore.getUserLevelCriteria.filter((item) => item.level != '未分类') || {};

  const saveLoading = ref(false);
  const tableLoading = ref(false);
  const peerComparisonModel = ref(false);
  const levelResult = ref(false);
  const message = useMessage();

  const levelList = ref<any>([]);
  const renderLabel = (option) => {
    return [
      h(
        NText,
        {
          style: {
            color: setLevelColor(option.value),
          },
        },
        {
          default: () => option.label,
        }
      ),
    ];
  };
  const props = defineProps({
    modelId: {
      type: String,
    },
    levelModelId: {
      type: String,
    },
    groupList: {
      type: Array,
      default: () => [],
    },
    secType: {
      type: SecType,
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
  });

  const handleViewResult = () => {
    levelResult.value = !levelResult.value;
  };
  //同业对比
  const peerComparisonBtn = () => {
    peerComparisonModel.value = true;
  };
  const query_LevelMapping = async () => {
    tableLoading.value = true;
    const { code, msg, data } = await queryLevelMapping(props.modelId, props.secType);
    if (code === 200) {
      if (data && data.length == 0) {
        levelList.value = userLevel.map((item) => ({
          modelLevel: item.level,
          targetConcentraGroup: null,
        }));
      } else {
        levelList.value = data;
      }
    } else {
      message.error(msg);
    }
    tableLoading.value = false;
  };
  //保存按钮
  const handleSave = async () => {
    if (levelList.value.filter((item) => item.targetConcentraGroup == null).length > 0) {
      message.error('映射评级不能为空！');
      return;
    }
    saveLoading.value = true;

    const { code, msg, data } = await saveLevelMapping(
      props.modelId,
      props.secType,
      props.levelModelId,
      levelList.value
    );
    saveLoading.value = false;

    if (code == 200) {
      message.success('保存成功');
      query_LevelMapping();
    } else {
      message.error(msg);
    }
  };
  watch(
    () => [props.modelId],
    () => {
      query_LevelMapping();
    },
    { deep: true }
  );
  onMounted(() => {});
</script>

<style scoped>
  /* 标签页内容优化样式 */
  :deep(.n-space) {
    margin-bottom: 16px;
  }

  /* 按钮样式优化 */
  :deep(.n-button) {
    height: 42px !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    border-radius: 8px;
    padding: 0 20px !important;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  :deep(.n-button .n-icon) {
    font-size: 18px !important;
    transition: all 0.2s ease-in-out;
  }

  :deep(.n-button:hover .n-icon) {
    transform: scale(1.1);
  }

  :deep(.n-button[size='large']) {
    height: 48px !important;
    font-size: 20px !important;
    padding: 0 24px !important;
  }

  :deep(.n-button[size='large'] .n-icon) {
    font-size: 20px !important;
    transition: all 0.2s ease-in-out;
  }

  :deep(.n-button[size='large']:hover .n-icon) {
    transform: scale(1.1);
  }

  /* 表格样式优化 */
  :deep(.n-table) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.n-table th) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-size: 16px !important;
    font-weight: 700 !important;
    padding: 12px 16px !important;
    color: #1a202c;
    border-bottom: 2px solid #e2e8f0;
  }

  :deep(.n-table td) {
    font-size: 15px !important;
    font-weight: 500 !important;
    padding: 10px 16px !important;
    border-bottom: 1px solid #f1f5f9;
  }

  :deep(.n-table tr:nth-child(even)) {
    background-color: #f8fafc;
  }

  /* 下拉选择器优化 */
  :deep(.n-select) {
    height: 36px !important;
  }

  :deep(.n-base-selection) {
    height: 36px !important;
    border-radius: 6px;
    border: 1px solid #d1d5db;
  }

  :deep(.n-base-selection-input) {
    font-size: 14px !important;
    font-weight: 500 !important;
  }

  :deep(.n-base-selection-placeholder) {
    font-size: 14px !important;
    color: #9ca3af;
  }

  /* 模态框优化 */
  :deep(.n-modal .n-card) {
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  :deep(.n-modal .n-card-header) {
    background: linear-gradient(135deg, #2d8cf0 0%, #409eff 100%);
    color: white;
    font-size: 18px !important;
    font-weight: 700 !important;
    padding: 16px 24px;
  }

  /* 加载状态优化 */
  :deep(.n-spin-content) {
    min-height: 200px;
  }
</style>
