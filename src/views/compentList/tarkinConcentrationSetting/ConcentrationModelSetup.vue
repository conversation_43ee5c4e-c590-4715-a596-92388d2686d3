<template>
  <!-- 集中度模型列表 -->
  <div class="concentration-model-setup">
    <n-grid class="model-grid" cols="12" item-responsive x-gap="20" y-gap="20">
      <n-grid-item v-for="(item, index) in dataList" :key="index" class="model-card-item" span="3">
        <n-card
          :class="[
            'model-card',
            modelId == item.modelId ? 'active-model-card' : '',
            item.ifModifyPermission === 1 ? 'readonly-model' : 'editable-model',
          ]"
          :hoverable="item.ifModifyPermission !== 1"
          @click="clickModel(item)"
        >
          <template #header>
            <div class="card-header-content">
              <n-h3 class="model-card-title">
                {{ item.modelName }}
              </n-h3>
              <div v-if="item.relatedLevelStrategyModelId" class="related-model-info">
                <span class="related-model-label">关联评级模型：</span>
                <n-popover trigger="hover">
                  <template #trigger>
                    <span class="related-model-name">{{ item.relatedLevelStrategyModelName }}</span>
                  </template>
                  <span>{{ item.relatedLevelStrategyModelName }}</span>
                </n-popover>
              </div>
            </div>
          </template>
          <template #header-extra>
            <n-space v-show="!buttonHiding" class="card-actions">
              <n-button
                :disabled="item.ifModifyPermission === 1"
                class="card-action-button edit-button"
                secondary
                size="medium"
                strong
                type="warning"
                @click.stop="editConcentration(item)"
              >
                <template #icon>
                  <n-icon>
                    <CreateOutline />
                  </n-icon>
                </template>
                编辑
              </n-button>
              <n-button
                :disabled="item.ifModifyPermission === 1"
                class="card-action-button delete-button"
                secondary
                size="medium"
                strong
                type="error"
                @click.stop="delUserDelistingl(item)"
              >
                <template #icon>
                  <n-icon>
                    <TrashOutline />
                  </n-icon>
                </template>
                删除
              </n-button>
            </n-space>
          </template>
          <div class="card-content">
            <div class="card-main-content">
              <n-ellipsis
                :tooltip="false"
                class="model-remark"
                expand-trigger="click"
                line-clamp="2"
              >
                <p class="remark-text">{{ item.remark }}</p>
              </n-ellipsis>
              <div class="model-info">
                <div class="info-item">
                  <n-icon class="info-icon">
                    <ClockCircleOutlined />
                  </n-icon>
                  <div class="info-content">
                    <span class="info-label">更新时间</span>
                    <span class="info-value">{{ item.updateTime }}</span>
                  </div>
                </div>
                <div class="info-item">
                  <n-icon class="info-icon">
                    <ClockCircleOutlined />
                  </n-icon>
                  <div class="info-content">
                    <span class="info-label">创建时间</span>
                    <span class="info-value">{{ item.createTime }}</span>
                  </div>
                </div>
                <div v-if="item.userName || item.createUser" class="info-item">
                  <n-icon class="info-icon">
                    <UserOutlined />
                  </n-icon>
                  <div class="info-content">
                    <span class="info-label">创建用户</span>
                    <span class="info-value">{{ item.userName || item.createUser || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="chart-container-positioned">
              <StockLevelNumChart :modelId="item.modelId" />
            </div>
            <!-- 权限状态标识 - 移动到卡片中间右侧 -->
            <div v-if="item.ifModifyPermission === 1" class="permission-badge-center">
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-tag class="readonly-tag" size="small" type="warning">
                    <template #icon>
                      <n-icon class="tooltip-icon">
                        <LockOutlined />
                      </n-icon>
                    </template>
                    只读模式
                  </n-tag>
                </template>
                <div class="permission-tooltip">
                  <n-icon class="tooltip-icon">
                    <ExclamationCircleOutlined />
                  </n-icon>
                  <span>此模型为只读模式，您没有编辑权限</span>
                </div>
              </n-tooltip>
            </div>
          </div>
          <template #action>
            <div v-show="!buttonHiding" class="card-switches">
              <div class="switch-group">
                <span class="switch-label">启用状态:</span>
                <n-switch
                  :disabled="item.ifModifyPermission === 1"
                  :value="item.enableStatus == 0"
                  class="status-switch"
                  @update:value="
                    (val) => {
                      updateEnableStatus(val, item);
                    }
                  "
                >
                  <template #checked> 启用</template>
                  <template #unchecked> 禁用</template>
                </n-switch>
              </div>
              <div class="switch-group">
                <span class="switch-label">主备状态:</span>
                <n-switch
                  :disabled="item.ifModifyPermission === 1"
                  :value="item.mainStatus == 0"
                  class="status-switch"
                  @update:value="
                    (val) => {
                      updateMainStatus(val, item);
                    }
                  "
                >
                  <template #checked> 启用</template>
                  <template #unchecked> 禁用</template>
                </n-switch>
              </div>
            </div>
          </template>
        </n-card>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, onMounted } from 'vue';
  import { NButton, useDialog, useMessage } from 'naive-ui';
  import { CreateOutline, TrashOutline } from '@vicons/ionicons5';
  import {
    ClockCircleOutlined,
    UserOutlined,
    LockOutlined,
    ExclamationCircleOutlined,
  } from '@vicons/antd';

  import {
    deleteConcentrationModel,
    getConcentrationModel,
  } from '@/api/marginTrading/concentration/concentrationModelApi';
  import { ConcentrationModelConfigDO } from '@/models/marginTrading/model/ConcentrationModel';
  import StockLevelNumChart from '@/views/compentList/newConcentrationGroupingModel/StockLevelNumChart.vue';

  const emit = defineEmits([
    'clickModel',
    'editConcentration',
    'updateEnableStatus',
    'updateMainStatus',
    'colseModel',
  ]);
  const props = defineProps({
    ifUser: { type: Boolean, default: false },
    buttonHiding: { type: Boolean, default: false },
    levelModelList: { type: Array, default: () => [] },
  });

  const dataList = ref<ConcentrationModelConfigDO[]>([]);
  const modelId = ref<string | any>('');
  const dialog = useDialog();
  const message = useMessage();

  const clickModel = (row: ConcentrationModelConfigDO) => {
    modelId.value = row.modelId;
    emit('clickModel', row);
  };
  const editConcentration = (row) => {
    emit('editConcentration', row);
  };

  const updateEnableStatus = (val, row) => {
    emit('updateEnableStatus', val, row);
  };
  const updateMainStatus = (val, row) => {
    emit('updateMainStatus', val, row);
  };

  // 获取关联评级模型名称
  const getRelatedModelName = (modelId) => {
    if (!modelId || !props.levelModelList) return '';
    const model = props.levelModelList.find((item) => item.modelId === modelId);
    return model ? model.modelName : '';
  };

  const delUserDelistingl = (row) => {
    const d = dialog.info({
      title: '提示',
      content: '你确定要删除' + row.modelName + '吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        d.loading = true;

        const { code, data, msg } = await deleteConcentrationModel(row.modelId);
        d.loading = false;

        if (code === 200) {
          message.success(msg);
          await get_ConcentrationModels();
          emit('colseModel');
        } else {
          message.error(msg);
        }
      },
      onNegativeClick: () => {},
    });
  };

  /**
   * 根据ID选择模型（内部选择，不触发emit事件）
   * 这个方法专门用于程序内部的模型选择，区别于用户点击的clickModel
   * 主要用于：
   * 1. 页面刷新后恢复之前选中的模型
   * 2. 自动刷新时保持当前选中的模型
   *
   * @param {number} targetModelId - 要选择的模型ID
   */
  const selectModelById = (targetModelId: number) => {
    const targetModel = dataList.value.find((item) => item.modelId === targetModelId);
    if (targetModel) {
      // 直接设置modelId，不调用clickModel，避免触发emit事件
      // 这样可以防止循环调用和不必要的状态更新
      modelId.value = targetModel.modelId;
      console.log('selectModelById: 内部选择模型', targetModel.modelId, targetModel.modelName);
    } else if (dataList.value.length > 0) {
      // 如果找不到目标模型（可能被删除了），选择第一个可用模型
      modelId.value = dataList.value[0].modelId;
      console.log(
        'selectModelById: 目标模型不存在，选择第一个',
        dataList.value[0].modelId,
        dataList.value[0].modelName
      );
    }
  };

  /**
   * 获取集中度模型列表
   * @param {boolean} autoSelectFirst - 是否自动选择第一个模型
   *   true: 初始加载时使用，会自动选择第一个模型
   *   false: 自动刷新时使用，不会自动选择，保持当前选中状态
   */
  const get_ConcentrationModels = async (autoSelectFirst = false) => {
    const { code, data, msg } = await getConcentrationModel(
      {
        current: 1,
        size: 1000,
      },
      true
    );
    if (code === 200) {
      dataList.value = data.records;
      // 只有在明确指定且没有任何选中模型时才自动选择第一个
      // 这样可以避免在用户已经选择了模型的情况下被重置
      if (autoSelectFirst && data.records && data.records.length > 0 && !modelId.value) {
        clickModel(data.records[0]);
      }
    }
  };
  onMounted(() => {
    get_ConcentrationModels(true); // 初始加载时允许自动选择第一个
  });
  defineExpose({ get_ConcentrationModels, selectModelById, dataList });
</script>

<style scoped>
  /* 整体容器样式 */
  .concentration-model-setup {
    padding: 0;
  }

  .model-grid {
    margin: 0;
  }

  /* 模型卡片项样式 */
  .model-card-item {
    cursor: pointer;
  }

  /* 模型卡片样式 - 压缩高度至70%，确保一致高度 */
  .model-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    box-shadow: 0 3px 14px rgba(45, 140, 240, 0.08);
    border: 2px solid transparent;
    overflow: hidden;
    height: 100%;
    min-height: 420px; /* 设置固定最小高度确保一致性 */
    display: flex;
    flex-direction: column;
    padding: 12px; /* 减少内边距 */
  }

  /* 确保卡片头部布局不会因内容长度而变形 */
  .model-card :deep(.n-card-header) {
    display: flex !important;
    align-items: flex-start !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
    min-height: 60px; /* 确保头部有足够高度 */
    padding: 16px 20px 12px 20px !important;
  }

  .model-card :deep(.n-card-header__main) {
    flex: 1 !important;
    min-width: 0 !important; /* 允许内容收缩 */
    margin-right: 16px !important;
  }

  .model-card :deep(.n-card-header__extra) {
    flex-shrink: 0 !important;
    margin-left: auto !important;
  }

  /* 激活状态样式 */
  .active-model-card {
    border-color: #2d8cf0 !important;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    box-shadow: 0 6px 22px rgba(45, 140, 240, 0.2);
  }

  /* 只读模型样式 */
  .readonly-model {
    border-left: 4px solid #d9d9d9;
    background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
    opacity: 0.85;
    cursor: default;
  }

  .readonly-model:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  /* 可编辑模型样式 */
  .editable-model {
    cursor: pointer;
  }

  .editable-model:hover {
    /*
transform: translateY(-2px);
*/
    box-shadow: 0 8px 25px rgba(45, 140, 240, 0.15);
  }

  /* 卡片头部内容 */
  .card-header-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    min-width: 0; /* 允许内容收缩 */
  }

  /* 卡片标题样式 - 增大字体，添加文本截断 */
  .model-card-title {
    font-size: 22px !important;
    font-weight: 700 !important;
    color: #1a202c;
    margin: 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }

  /* 权限标识样式 - 中间位置 */
  .permission-badge-center {
    position: absolute;
    top: 20%;
    right: 10px;
    transform: translateY(-50%);
    z-index: 10;
  }

  .readonly-tag {
    font-size: 12px !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.25) !important;
    padding: 5px 8px !important;
    min-height: 36px !important;
    border-radius: 10px !important;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    border: none !important;
    color: #ffffff !important;
  }

  .permission-tooltip {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
  }

  .tooltip-icon {
    color: #fff;
    font-size: 16px;
  }

  /* 关联模型信息样式 - 增大字体，添加文本截断 */
  .related-model-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 4px;
    max-width: 100%;
    overflow: hidden;
  }

  .related-model-label {
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    flex-shrink: 0;
  }

  .related-model-name {
    font-size: 14px;
    font-weight: 600;
    color: #2d8cf0;
    background: rgba(45, 140, 240, 0.1);
    padding: 3px 8px;
    border-radius: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }

  /* 卡片操作按钮样式 - 确保按钮不换行 */
  .card-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-start;
    min-width: 140px; /* 确保按钮区域有足够宽度 */
  }

  .card-action-button {
    height: 30px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    border-radius: 6px;
    padding: 0 10px !important;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .edit-button {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    border: none;
    color: white;
  }

  .delete-button {
    background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
    border: none;
    color: white;
  }

  /* 卡片内容样式 - 调整布局为相对定位 */
  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0;
    min-height: 180px; /* 确保足够高度容纳图表 */
  }

  .card-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-right: 120px; /* 为右下角图表留出空间 */
  }

  .model-remark {
    flex: 1;
  }

  .remark-text {
    font-size: 16px; /* 增大字体 */
    font-weight: 500;
    color: #4b5563;
    margin: 0;
    line-height: 1.4;
  }

  .model-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
  }

  .info-icon {
    color: #8c8c8c;
    font-size: 14px;
    min-width: 14px;
  }

  .info-content {
    display: flex;
    flex-direction: column;
    gap: 1px;
    flex: 1;
    line-height: 1.2;
  }

  .info-label {
    font-size: 12px;
    color: #8c8c8c;
    font-weight: 500;
  }

  .info-value {
    font-size: 14px;
    color: #262626;
    font-weight: 600;
  }

  /* 图表容器定位到右下角 */
  .chart-container-positioned {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 100px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 确保图表在激活状态下也可见 */
  .active-model-card .chart-container-positioned {
    background: rgba(240, 248, 255, 0.9);
    border-color: #2d8cf0;
  }

  /* 开关组样式 - 优化尺寸和间距 */
  .card-switches {
    display: flex;
    flex-direction: column;
    gap: 10px; /* 减少间距 */
    padding: 10px 0 6px 0; /* 减少内边距 */
  }

  .switch-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px; /* 减少间距 */
  }

  .switch-label {
    font-size: 14px; /* 减小字体 */
    font-weight: 600;
    color: #374151;
    white-space: nowrap;
  }

  .status-switch {
    flex-shrink: 0;
  }

  /* 优化开关样式 */
  .status-switch :deep(.n-switch) {
    --n-rail-height: 20px;
    --n-rail-width: 36px;
    --n-button-width: 16px;
    --n-button-height: 16px;
  }

  .status-switch :deep(.n-switch__checked) {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
  }

  .status-switch :deep(.n-switch__unchecked) {
    background: #d1d5db !important;
  }

  .status-switch :deep(.n-switch__button) {
    background: #ffffff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .model-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .model-card {
      min-height: 380px; /* 在中等屏幕上稍微减少高度 */
    }
  }

  @media (max-width: 768px) {
    .model-card {
      min-height: 350px; /* 在小屏幕上进一步减少高度 */
    }

    .model-card-title {
      font-size: 18px !important;
    }

    .card-action-button {
      height: 32px !important;
      font-size: 14px !important;
      padding: 0 8px !important;
    }

    .switch-label {
      font-size: 14px;
    }

    .card-switches {
      gap: 12px;
    }

    .card-actions {
      min-width: 120px; /* 在小屏幕上减少按钮区域宽度 */
    }

    .related-model-name {
      max-width: 100px; /* 在小屏幕上减少关联模型名称的最大宽度 */
    }
  }
</style>
