<template>
  <!--  塔金集中度设置    集中度模型设置-->

  <div>
    <n-card>
      <template #header>
        <n-h2 class="mb-0" prefix="bar" type="error">
          模型列表
          <n-popover>
            <template #trigger>
              <n-icon size="20">
                <HelpCircleOutline />
              </n-icon>
            </template>
            <div style="font-size: 18px"> 点击下方模型即可编辑。</div>
          </n-popover>
        </n-h2>
      </template>
      <template #header-extra>
        <n-button v-show="!buttonHiding" type="primary" @click="addNewRule"> 新增模型</n-button>
      </template>
      <ConcentrationModelSetup
        ref="ConcentrationModelSetupRef"
        :buttonHiding="buttonHiding"
        :ifUser="ifUser"
        @click-model="clickModel"
        @edit-concentration="editConcentration"
        @update-enable-status="updateEnableStatus"
        @update-main-status="updateMainStatus"
        @colse-model="modelName = null"
      />
    </n-card>
    <br />
    <n-card>
      <template #header>
        <n-h1 class="mb-0" prefix="bar">
          {{ currentModel.modelName }}
        </n-h1>
      </template>
      <div v-if="!loadCompleteLoading" class="text-center">
        <n-spin :show="loadCompleteLoading" />
      </div>
      <div :style="{ opacity: !loadCompleteLoading ? 0 : 1 }">
        <ConcentrationSettingCard
          :buttonHiding="buttonHiding"
          :ifUser="ifUser"
          :modelId="currentModel.modelId"
          @load-complete="loadComplete"
        />
      </div>
    </n-card>

    <n-modal
      v-model:show="addShowModal"
      :bordered="false"
      :title="addForm.modelId ? '编辑模型' : '新增模型'"
      class="custom-card w-[500px]"
      preset="card"
      size="huge"
    >
      <n-form ref="formRef" label-placement="left">
        <n-form-item label="模型名称">
          <n-input v-model:value="addForm.modelName" placeholder="请输入模型名称" />
        </n-form-item>
        <n-form-item label="模型备注">
          <n-input v-model:value="addForm.remark" placeholder="请输入备注" type="textarea" />
        </n-form-item>
      </n-form>
      <br />
      <br />
      <n-space justify="center">
        <n-button @click="addShowModal = false"> 取消</n-button>
        <n-button type="primary" @click="saveNewRule"> 确定</n-button>
      </n-space>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import ConcentrationModelSetup from '@/views/compentList/tarkinConcentrationSetting/ConcentrationModelSetup.vue';
  import ConcentrationSettingCard from '@/views/compentList/tarkinConcentrationSetting/ConcentrationSettingCard.vue';
  import { ref, onMounted, nextTick, reactive, watch } from 'vue';

  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { useMessage } from 'naive-ui';
  import { updateConcentrationModel } from '@/api/marginTrading/concentration/concentrationModelApi';
  import { ConcentrationModelConfigDO } from '@/models/marginTrading/model/ConcentrationModel';

  const props = defineProps({
    ifUser: { type: Boolean, default: false },
    buttonHiding: { type: Boolean, default: false },
  });

  const stockType = ref<string[]>();
  const modelId = ref<number | null>();
  const modelName = ref<string | null>();
  const addShowModal = ref(false);
  const loadCompleteLoading = ref(false);
  const showModal = ref(false);
  const currentModel = ref<any>({});
  const ConcentrationModelSetupRef = ref();
  const form = ref(false);
  const message = useMessage();
  const addForm = reactive<ConcentrationModelConfigDO>({
    remark: null,
    modelName: null,
    modelId: null,
    mainStatus: 1,
    enableStatus: 1,
    createTime: null,
    deleteStatus: null,
    updateTime: null,
    userId: null,
  });
  //新增模板
  const addNewRule = () => {
    addForm.modelName = null;
    addForm.modelId = null;
    addForm.remark = null;
    addForm.mainStatus = 1;
    addForm.enableStatus = 1;
    addShowModal.value = true;
  };
  //编辑模板
  const editConcentration = (row) => {
    addForm.modelName = row.modelName;
    addForm.modelId = row.modelId;
    addForm.remark = row.remark;
    addShowModal.value = true;
  };
  //加载完成
  const loadComplete = (val) => {
    loadCompleteLoading.value = val;
  };
  //新增模板确定
  const saveNewRule = async () => {
    if (!addForm.modelName) {
      message.success('请输入模型名称');
      return;
    }
    if (!addForm.remark) {
      message.success('请输入备注');
      return;
    }
    const { code, data, msg } = await updateConcentrationModel(addForm);
    if (code === 200) {
      message.success(msg);
      addShowModal.value = false;
      ConcentrationModelSetupRef.value?.get_ConcentrationModels();
      addForm.remark = null;
      addForm.modelName = null;
      addForm.modelId = null;
    } else {
      message.error(msg);
    }
  };

  //更新启用状态
  const updateEnableStatus = async (val, row) => {
    row.enableStatus = val ? 0 : 1;
    const { code, data, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels();

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  }; //更新主备状态
  const updateMainStatus = async (val, row) => {
    row.mainStatus = val ? 0 : 1;
    const { code, data, msg } = await updateConcentrationModel(row);
    ConcentrationModelSetupRef.value?.get_ConcentrationModels();

    if (code === 200) {
      message.success(msg);
    } else {
      message.error(msg);
    }
  };
  //点击某个模型
  const clickModel = (row) => {
    currentModel.value = row;
    //console.log(row)
  };
  onMounted(() => {});
</script>

<style scoped></style>
