<template>
  <!--基金详情-->
  <n-card size="small">
    <n-grid cols="10" item-responsive x-gap="10" y-gap="10">
      <n-grid-item span="10">
        <!--          基本信息-->
        <BasicInformation :secCode="secCode" :secName="secName" />
      </n-grid-item>
      <n-grid-item span="10">
        <!--          历史市场行情数据-->
        <HistoricalMarketData :secCode="secCode" :secName="secName" />
      </n-grid-item>
      <n-grid-item span="10">
        <!--          同业信息-->
        <SecPeerDataTable :secCode="secCode" :secName="secName" />
      </n-grid-item>
      <n-grid-item span="10">
        <n-tabs type="card" animated>
          <n-tab-pane name="规模变动" tab="规模变动" display-directive="show">
            <!--          规模变动-->
            <n-card :segmented="{ content: true, footer: 'soft' }">
              <template #header>
                <n-flex>
                  <n-h2 prefix="bar" class="mb-0"> 规模变动</n-h2>
                </n-flex>
              </template>
              <ScaleAlteration :secCode="secCode" :secName="secName" />
            </n-card>
          </n-tab-pane>
          <n-tab-pane name="历史净值" tab="历史净值" display-directive="if">
            <!--          历史净值-->
            <n-card :segmented="{ content: true, footer: 'soft' }">
              <template #header>
                <n-flex>
                  <n-h2 prefix="bar" class="mb-0"> 历史净值</n-h2>
                </n-flex>
              </template>
              <NetHistoricalValue :secCode="secCode" :secName="secName" />
            </n-card>
          </n-tab-pane>
          <n-tab-pane name="基金持仓" tab="基金持仓" display-directive="if">
            <FundPosition :secCode="secCode" :secName="secName" />
          </n-tab-pane>
        </n-tabs>
      </n-grid-item>

      <n-grid-item span="10" v-if="prefixHualong">
        <!--          持仓客户清单-->
        <n-card :segmented="{ content: true, footer: 'soft' }">
          <template #header>
            <n-flex>
              <n-h2 prefix="bar" class="mb-0"> 持仓客户清单</n-h2>
            </n-flex>
          </template>
          <n-blockquote>
            总计有
            <n-tag type="error"> 3 </n-tag>
            个信用客户持有这个票，持仓合计数 <n-tag>2300 </n-tag> 股，总市值
            <n-tag type="success">4.5 </n-tag> 亿元。
          </n-blockquote>
          <n-blockquote>
            总计有 <n-tag type="error">2 </n-tag> 个信用客户有这个票的负债合约，合计负债
            <n-tag type="success">2000 </n-tag> 万元；。
          </n-blockquote>
          <ListOfHoldingClients />
        </n-card>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script setup>
  import BasicInformation from '@/views/securityFileWindow/fundFile/BasicInformation .vue';
  import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue';
  import MarketChart from '@/views/securityFileWindow/fundFile/MarketChart.vue';
  import FinancialBarChart from '@/components/BarGraph/FinancialBarChart.vue';
  import ListOfHoldingClients from '@/views/securityFileWindow/fundFile/ListOfHoldingClients.vue';
  import ScaleAlteration from '@/views/securityFileWindow/fundFile/ScaleAlteration.vue';
  import FundPosition from '@/views/securityFileWindow/fundFile/FundPosition.vue';
  import NetHistoricalValue from '@/views/securityFileWindow/fundFile/NetHistoricalValue.vue';
  import HistoricalMarketData from '@/views/securityFileWindow/fundFile/HistoricalMarketData.vue';
  import { onMounted, ref, toRaw } from 'vue';
  import { useRoute } from 'vue-router';
  import { decrypt } from '@/utils/crypto/cryptoUtils';
  import { useGlobSetting } from '@/hooks/setting';
  const globSetting = useGlobSetting();
  const { prefixHualong } = globSetting;
  const secCode = ref('');
  const secName = ref('');
  const route = useRoute();

  onMounted(() => {
    // 获取地址栏参数
    let { Amount1, Amount2, Amount3 } = toRaw(route).params;
    secCode.value = Amount1;
    secName.value = '';
  });
</script>

<style scoped></style>
