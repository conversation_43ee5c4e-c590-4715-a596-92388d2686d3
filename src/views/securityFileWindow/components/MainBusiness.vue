<template>
  <div>
    <br />
    <n-card>
      <n-space>
        <n-select
          v-model:value="mainBusinessType"
          :options="options"
          clearable
          placeholder="请选择主营业务类型"
          style="width: 200px"
          @change="onSubmit"
        />
        <n-select
          v-model:value="financialDate"
          :options="options2"
          clearable
          placeholder="请选择财报日期"
          style="width: 200px"
          @change="onSubmit"
        />
      </n-space>
      <br />
      <n-data-table
        :columns="columns"
        :data="data"
        :loading="showLoading"
        :pagination="false"
        :single-line="false"
        max-height="500px"
        min-height="472px"
      />

      <n-space justify="center">
        <n-pagination
          v-show="total > 0"
          v-model:page="pageData.current"
          v-model:page-size="pageData.size"
          :item-count="total"
          :page-sizes="[10, 20, 50, 100, 300]"
          class="mt-5"
          show-size-picker
          @update:page="updatePage"
          @update:page-size="updatePageSize"
        >
          <template #suffix> 共 {{ total }} 条</template>
        </n-pagination>
      </n-space>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, h, watch, nextTick } from 'vue';
  import { NButton } from 'naive-ui';
  import { openStockArchives } from '@/utils/goToArchives';
  import { queryMainBusinessHistory, queryMainBusinessType } from '@/api/sec/secProfileApi';

  interface Props {
    stockId?: string;
    stockName?: string;
  }

  const props = defineProps<Props>();

  const total = ref(0);
  const pageData = reactive({
    size: 10,
    current: 1,
  });
  const mainBusinessType = ref(undefined);
  const financialDate = ref(undefined);
  const showLoading = ref(true);
  const options = ref([
    { label: '按行业分类', value: 1 },
    { label: '按产品分类', value: 2 },
    { label: '按地区分类', value: 3 },
  ]);
  const options2 = ref([
    { value: '%-03-31', label: '一季报' },
    { value: '%-06-30', label: '中报' },
    { value: '%-09-30', label: '三季报' },
    { value: '%-12-31', label: '年报' },
  ]);

  // 定义数据源
  const data = ref([]);

  // 定义一个函数，用于获取相同名称的最后一行的索引
  const getLastIndex = (index, names, types) => {
    const sameNameIndex = data.value.slice(index).findIndex((item) => {
      return names.some((name, i) => item[types[i]] !== name);
    });
    return sameNameIndex === -1 ? data.value.length : sameNameIndex;
  };

  // 定义表格列
  const columns = ref([
    {
      title: '证券代码',
      align: 'center',
      key: 'stockId',
      width: '100px',
      rowSpan: (rowData, rowIndex) => {
        return getLastIndex(rowIndex, [rowData.stockId], ['stockId']);
      },
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openStockArchives(row.stockId, row.stockName);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.stockId }
        );
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '证券名称',
      key: 'stockName',
      rowSpan: (rowData, rowIndex) => {
        return getLastIndex(
          rowIndex,
          [rowData.stockId, rowData.stockName],
          ['stockId', 'stockName']
        );
      },
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              //console.log(1);
              openStockArchives(row.stockId, row.stockName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.stockName }
        );
      },
    },
    {
      align: 'center',
      width: '170px',
      title: '财报日期',
      key: 'reportDate',
      rowSpan: (rowData, rowIndex) => {
        return getLastIndex(
          rowIndex,
          [rowData.stockId, rowData.stockName, rowData.reportDate],
          ['stockId', 'stockName', 'reportDate']
        );
      },
    },
    { align: 'center', width: '170px', title: '主营构成', key: 'itemName' },
    { align: 'center', width: '170px', key: 'mainBusinessIncome', title: '营业收入(万元)' },
    { align: 'center', width: '170px', key: 'mbiRatio', title: '占营业收入比例(%)' },
    { align: 'center', width: '170px', key: 'mainBusinessCost', title: '营业成本(万元)' },
    { align: 'center', width: '170px', key: 'mbcRatio', title: '占成本比例(%)' },
    { align: 'center', width: '170px', key: 'mainBusinessRprofit', title: '营业利润(万元)' },
    { align: 'center', width: '170px', key: 'mbrRatio', title: '占利润比例(%' },
    { align: 'center', width: '170px', key: 'grossRprofitRatio', title: '毛利率(%)' },
  ]);

  const get_MainBusinessHistory = () => {
    showLoading.value = true;
    var params = {
      stockId: props.stockId,
      current: pageData.current,
      size: pageData.size,
      mainBusinessType: mainBusinessType.value ? 2 : mainBusinessType.value,
      financialDate: !financialDate.value ? '%-12-31' : financialDate.value,
    };
    queryMainBusinessHistory(params)
      .then((res) => {
        showLoading.value = false;

        if (res.code === 200) {
          data.value = res.data.records;
          total.value = res.data.total;
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };
  const updatePage = (page) => {
    pageData.current = page;
    get_MainBusinessHistory();
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    get_MainBusinessHistory();
  };
  const get_MainBusinessType = async () => {
    const { data, code } = await queryMainBusinessType({ secCode: props.stockId });
    if (code === 200) {
      mainBusinessType.value = data[1];
      options.value = options.value.filter((item) => data.includes(item.value));
      get_MainBusinessHistory();
    }
  };
  watch(
    () => props.stockId,
    () => {
      get_MainBusinessType();
    },
    { immediate: true }
  );

  const onSubmit = () => {
    pageData.current = 1;
    nextTick(() => {
      get_MainBusinessHistory();
    });
  };
</script>

<!-- 定义样式 -->
<style scoped></style>
