<template>
  <!--  事件跟踪-->
  <n-card>
    <n-grid :x-gap="12" :y-gap="10" cols="6" item-responsive>
      <n-grid-item span="6">
        <n-card>
          <template #header>
            <n-flex>
              <n-h2 class="mb-0" prefix="bar"> {{ stockName }}({{ stockId }})</n-h2>
              <n-tag type="warning"> 所属板块：{{ plateName }} </n-tag>
            </n-flex>
          </template>
          <template #header-extra>
            <StockSearch @get-value="getStockValue" />
          </template>
        </n-card>
      </n-grid-item>
      <n-grid-item span="3">
        <n-card>
          <template #header>
            <n-flex>
              <n-h3 class="mb-0" prefix="bar"> 行情</n-h3>
            </n-flex>
          </template>
          <echatsLine :showLoading="showLoading" :xdata="xdata" :ydata="ydata" height="500px" />
        </n-card>
        <n-card>
          <template #header>
            <n-flex>
              <n-h3 class="mb-0" prefix="bar"> 基本指标</n-h3>
            </n-flex>
          </template>
          <n-descriptions :column="2" bordered label-placement="left">
            <n-descriptions-item label="(年报) 负债率 / 行业中位数(%)">
              <span
                :style="{
                  color: setColors(secInfoTracking.debtRatio <= secInfoTracking.debtRatioMidValue),
                }"
                >{{ secInfoTracking.debtRatio || '--' }}
              </span>
              <span
                :style="{
                  color: setColors(secInfoTracking.debtRatioMidValue < secInfoTracking.debtRatio),
                }"
                >({{ secInfoTracking.debtRatioMidValue || '--' }})
              </span>
            </n-descriptions-item>
            <n-descriptions-item label="(年报) 净利润 / 行业中位数(亿元)">
              <span
                :style="{
                  color: setColors(secInfoTracking.netProfit <= secInfoTracking.netProfitMidValue),
                }"
                >{{ secInfoTracking.netProfit || '--' }}
              </span>
              <span
                :style="{
                  color: setColors(secInfoTracking.netProfitMidValue < secInfoTracking.netProfit),
                }"
                >({{ secInfoTracking.netProfitMidValue || '--' }})
              </span>
            </n-descriptions-item>
            <n-descriptions-item label="控股股东质押比例(%)">
              {{ secInfoTracking.controlShareholderPledgeRatio || '--' }}
            </n-descriptions-item>
            <n-descriptions-item label="(年报) 毛利率 / 行业中位数(%)">
              <span
                :style="{
                  color: setColors(
                    secInfoTracking.grossMargin <= secInfoTracking.grossMarginMidValue
                  ),
                }"
                >{{ secInfoTracking.grossMargin || '--' }}
              </span>
              <span
                :style="{
                  color: setColors(
                    secInfoTracking.grossMarginMidValue < secInfoTracking.grossMargin
                  ),
                }"
                >({{ secInfoTracking.grossMarginMidValue || '--' }})
              </span>
            </n-descriptions-item>
            <n-descriptions-item label="(年报) 营业收入 / 行业中位数(%)">
              <span
                :style="{
                  color: setColors(secInfoTracking.income <= secInfoTracking.incomeMidValue),
                }"
                >{{ secInfoTracking.income || '--' }}
              </span>
              <span
                :style="{
                  color: setColors(secInfoTracking.incomeMidValue < secInfoTracking.income),
                }"
                >({{ secInfoTracking.incomeMidValue || '--' }})
              </span>
            </n-descriptions-item>

            <n-descriptions-item label="(年报) 负债合计 / 行业中位数(亿元)">
              <span
                :style="{ color: setColors(secInfoTracking.debt <= secInfoTracking.debtMidValue) }"
                >{{ secInfoTracking.debt || '--' }}
              </span>
              <span
                :style="{ color: setColors(secInfoTracking.debtMidValue < secInfoTracking.debt) }"
                >({{ secInfoTracking.debtMidValue || '--' }})
              </span>
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
      </n-grid-item>
      <n-grid-item span="3">
        <n-card>
          <template #header>
            <n-flex>
              <n-h3 class="mb-0" prefix="bar"> 事件回溯</n-h3>
            </n-flex>
          </template>
          <n-spin :show="lableLoading">
            <n-scrollbar style="max-height: 733px">
              <n-timeline>
                <n-timeline-item
                  v-for="(item, index) in lableList"
                  :key="index"
                  :time="item.date"
                  :title="item.infoDescribe"
                  :type="index % 2 == 0 ? 'success' : 'error'"
                />
              </n-timeline>
            </n-scrollbar>
          </n-spin>
        </n-card>
      </n-grid-item>
    </n-grid>
  </n-card>
  <div> </div>
</template>

<script setup>
  import StockSearch from '@/components/SecuritySearch/StockSearch.vue';
  import echatsLine from '@/views/securityFileWindow/eventTrace/echatsLine.vue';

  import { onMounted, ref, toRaw, computed } from 'vue';
  import { decrypt } from '@/utils/crypto';
  import { useRoute } from 'vue-router';
  import { formatTime } from '@/utils/common/date/dateUtil';
  import { setColors } from '@/utils/ui/color/LevelColor';
  import { getSecRiskWarningList } from '@/api/label/riskWarningApi';
  import { getHistoricalEventShow, getSecInfoTracking } from '@/api/sec/secProfileApi';
  import { createSecInfoTrackingVO } from '@/models/sec/secModels';
  const stockId = ref('');
  const stockName = ref('');
  const showLoading = ref(false);
  const lableLoading = ref(false);
  const dateValue = ref([]);
  const xdata = ref([]);
  const ydata = ref([]);
  const lableList = ref([]);
  const secInfoTracking = ref(createSecInfoTrackingVO());
  const route = useRoute();
  onMounted(() => {
    //开始时间
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3);
    dateValue.value = [formatTime(start), formatTime(end)];
    // 获取地址栏参数
    let { Amount1, Amount2, Amount3 } = toRaw(route).query;
    stockId.value = decrypt(Amount1);
    stockName.value = decrypt(Amount2);
    get_StockInfoTracking();
    get_SecRiskWarningList();
    get_HistoricalEventShow();
  });

  const plateName = computed(() => {
    if (stockId.value.substr(0, 1) === '8') {
      return '北交所';
    } else if (stockId.value.substr(0, 3) === '688') {
      return '科创板';
    } else if (stockId.value.substr(0, 3) === '300') {
      return '创业板';
    } else if (stockId.value.substr(0, 3) === '002') {
      return '中小板';
    } else {
      return '主板';
    }
  });

  const getStockValue = (id, name) => {
    if (!id) {
      let { Amount1, Amount2, Amount3 } = toRaw(route).query;
      stockId.value = decrypt(Amount1);
      stockName.value = decrypt(Amount2);
    } else {
      stockId.value = id;
      stockName.value = name;
    }

    get_StockInfoTracking();
    get_SecRiskWarningList();
    get_HistoricalEventShow();
  };
  //基本指南
  const get_StockInfoTracking = async () => {
    secInfoTracking.value = {};
    const { data, code } = await getSecInfoTracking(stockId.value);
    if (code === 200) {
      secInfoTracking.value = data;
    }
  };
  //事件回溯
  const get_SecRiskWarningList = async () => {
    lableLoading.value = true;
    lableList.value = [];
    const { data, code, msg } = await getSecRiskWarningList({
      secCode: stockId.value,
      endDate: dateValue.value[1],
      startDate: dateValue.value[0],
      current: 1,
      size: 1000,
    });
    lableLoading.value = false;
    if (code === 200) {
      lableList.value = data.records;
    }
  };
  //行情
  const get_HistoricalEventShow = async () => {
    showLoading.value = true;
    xdata.value = [];
    ydata.value = [];
    const { data, code, msg } = await getHistoricalEventShow({
      secCode: stockId.value,
      startDate: dateValue.value[0],
      endDate: dateValue.value[1],
    });
    showLoading.value = false;
    if (code === 200) {
      xdata.value = data.map((item) => item.date);
      ydata.value = fillNullsWithPrevious(data.map((item) => item.closes));
    }
  };
  const fillNullsWithPrevious = (arr) => {
    return arr.reduce((accumulator, currentValue, currentIndex, array) => {
      if (currentValue === null) {
        accumulator.push(accumulator[accumulator.length - 1]);
      } else {
        // 如果不是null，则添加到结果数组中
        accumulator.push(currentValue);
      }

      return accumulator;
    }, []);
  };
</script>

<style scoped></style>
