<template>
  <!--客户档案-->
  <n-card>
    <n-card>
      <template #header>
        <n-flex>
          <n-h2 class="mb-0" prefix="bar"> 基础信息</n-h2>
          <n-tag type="warning"> 剔除风险证券资不抵债</n-tag>
        </n-flex>
      </template>
      <n-flex>
        <n-grid :x-gap="12" :y-gap="10" cols="6" item-responsive>
          <n-grid-item span="1">
            <n-card class="h-[210px] inserted">
              <div class="h-full text-center items-center mt-6">
                <template v-for="i in 5">
                  <n-icon color="#007deb" size="50">
                    <Star />
                  </n-icon>
                  <div v-if="i == 3"><br /></div>
                </template>
              </div>
            </n-card>
          </n-grid-item>
          <n-grid-item span="5">
            <n-descriptions :column="5" bordered label-placement="left">
              <n-descriptions-item label="客户名称">
                {{ custName }}
              </n-descriptions-item>
              <n-descriptions-item label="客户代码">
                {{ custId }}
              </n-descriptions-item>
              <n-descriptions-item label="性别"> 男</n-descriptions-item>
              <n-descriptions-item label="年龄">
                <n-tag type="error"> 65岁</n-tag>
                <!--         <span  :style="{color:setColors(true)}"></span>-->
              </n-descriptions-item>
              <n-descriptions-item label="籍贯"> 江苏省南京市</n-descriptions-item>
              <n-descriptions-item label="开户时间"> 2020年5月18日</n-descriptions-item>
              <n-descriptions-item label="开户营业部">
                华泰证券南京新街口营业部
              </n-descriptions-item>
              <n-descriptions-item label="两融专员"> 李华</n-descriptions-item>
              <n-descriptions-item label="拓展人"> 王强</n-descriptions-item>
              <n-descriptions-item label="服务人员"> 赵敏</n-descriptions-item>
              <n-descriptions-item label="授信余额"> 5,000万元</n-descriptions-item>
              <n-descriptions-item label="可用保证金余额"> 3,000万元</n-descriptions-item>
              <n-descriptions-item label="维持担保比例">
                <n-tag type="success"> 150%</n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="负债余额"> 2,000万元</n-descriptions-item>
              <n-descriptions-item label="担保总资产"> 4,000万元</n-descriptions-item>
              <n-descriptions-item label="7个交易日到期的负债余额"> 1,000万元</n-descriptions-item>
              <n-descriptions-item label="7个交易日到期的不符合展期条件的负债余额">
                8,000万元
              </n-descriptions-item>
              <n-descriptions-item label="修正维保比例">
                <n-tag type="success"> 145%</n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="普通账户资产"> 2,000万元</n-descriptions-item>
            </n-descriptions>
          </n-grid-item>
        </n-grid>
      </n-flex>
    </n-card>
    <br />

    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="2">
        <n-card>
          <template #header>
            <n-flex>
              <n-h2 class="mb-0" prefix="bar" type="error"> 维保趋势</n-h2>
            </n-flex>
          </template>
          <echatsLine
            :showLoading="false"
            :xdata="[
              '2024-07-01',
              '2024-06-01',
              '2024-05-01',
              '2023-01-04',
              '2023-01-05',
              '2023-01-06',
              '2023-01-07',
            ]"
            :ydata="[120, 150, 135, 160, 140, 110, 170]"
            height="300px"
            unit="%"
          />
        </n-card>
      </n-grid-item>
      <n-grid-item span="2">
        <n-card>
          <template #header>
            <n-flex>
              <n-h2 class="mb-0" prefix="bar" type="error"> 授信变化趋势</n-h2>
            </n-flex>
          </template>
          <echatsLine
            :showLoading="false"
            :xdata="[
              ' 2024-07-01 ',
              '2024-06-01',
              '2024-05-01',
              '2024-04-01',
              '2023-01-01',
              '2023-01-02',
              '2023-01-03',
              '2023-01-04',
              '2023-01-05',
              '2023-01-06',
              '2023-01-07',
            ]"
            :ydata="[1120, 1120, 2315, 2260, 1260, 1240, 2140, 2140, 3120, 1110, 1270]"
            height="300px"
            unit="万元"
          />
        </n-card>
      </n-grid-item>
    </n-grid>
    <br />

    <n-card>
      <template #header>
        <n-flex>
          <n-h2 class="mb-0" prefix="bar"> 一、持仓分析</n-h2>
        </n-flex>
      </template>
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="4">
          <PlateConcentrationAnalysis />
        </n-grid-item>
        <n-grid-item span="4">
          <ConcentrationAnalysisOfEachCoupon />
        </n-grid-item>
      </n-grid>
    </n-card>
    <n-card>
      <template #header>
        <n-flex>
          <n-h2 class="mb-0" prefix="bar">二、负债分析</n-h2>
        </n-flex>
      </template>
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="4">
          <OverallIndebtedness />
        </n-grid-item>
        <n-grid-item span="4">
          <DetailOfLiabilities />
        </n-grid-item>
        <n-grid-item span="4">
          <DetailOfLiabilityContract />
        </n-grid-item>
      </n-grid>
    </n-card>
    <n-card>
      <template #header>
        <n-flex>
          <n-h2 class="mb-0" prefix="bar">三、持仓风险情况分析</n-h2>
        </n-flex>
      </template>
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="4">
          <FixedMaintenanceRatio />
        </n-grid-item>
        <n-grid-item span="4">
          <ThroughAccountPositionDetails />
        </n-grid-item>
      </n-grid>
    </n-card>
  </n-card>
</template>

<script setup>
  import { onMounted, ref, toRaw } from 'vue';
  import { decrypt } from '@/utils/crypto';
  import { Star } from '@vicons/ionicons5';
  import { useRoute } from 'vue-router';
  import PlateConcentrationAnalysis from '@/views/securityFileWindow/clientFile/PlateConcentrationAnalysis.vue';
  import OverallIndebtedness from '@/views/securityFileWindow/clientFile/OverallIndebtedness.vue';
  import ConcentrationAnalysisOfEachCoupon from '@/views/securityFileWindow/clientFile/ConcentrationAnalysisOfEachCoupon.vue';
  import DetailOfLiabilityContract from '@/views/securityFileWindow/clientFile/DetailOfLiabilityContract.vue';
  import DetailOfLiabilities from '@/views/securityFileWindow/clientFile/DetailOfLiabilities.vue';
  import FixedMaintenanceRatio from '@/views/securityFileWindow/clientFile/FixedMaintenanceRatio.vue';
  import ThroughAccountPositionDetails from '@/views/securityFileWindow/clientFile/ThroughAccountPositionDetails.vue';
  import echatsLine from '@/views/securityFileWindow/eventTrace/echatsLine.vue';

  const custId = ref('');
  const custName = ref('');
  const route = useRoute();

  onMounted(() => {
    // 获取地址栏参数
    let { Amount1, Amount2, Amount3 } = toRaw(route).query;
    custId.value = decrypt(Amount1);
    custName.value = decrypt(Amount2);
  });
</script>

<style scoped></style>
