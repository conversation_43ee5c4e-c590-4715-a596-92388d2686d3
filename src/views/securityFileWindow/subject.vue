<!--档案页面-->
<template>
  <div style="margin: 10px">
    <n-grid cols="10" item-responsive x-gap="10" y-gap="10">
      <n-grid-item span="10 400:10 600:10 1500:7">
        <!--          信息概览-->

        <InformationOverview
          @set-start-date="setStartDate"
          :stockId="stockId"
          :stockName="stockName"
        />
      </n-grid-item>
      <n-grid-item span="10 400:10 600:10 1500:3">
        <!--          融资买入筹码分布图-->
        <FinancingChip :stockId="stockId" :stockName="stockName" />
        <!--      同业详情-->
        <TradeDetails :stockId="stockId" :stockName="stockName" />
      </n-grid-item>

      <!--        风险标签-->
      <n-grid-item span="10">
        <RiskLabel :stockId="stockId" :stockName="stockName" />
      </n-grid-item>

      <!--        个股与行业矩阵分布-->
      <n-grid-item span="10">
        <n-card
          class="mb-2"
          :segmented="{
            content: true,
            footer: 'soft',
          }"
        >
          <template #header>
            <n-h2 prefix="bar" class="mb-0"> 个股与行业矩阵分布 </n-h2>
          </template>

          <n-tabs type="card" animated v-if="matrixNameList.length > 8">
            <n-tab-pane
              display-directive="if"
              :name="matrix.matrixName"
              :tab="matrix.matrixName"
              v-for="(matrix, index) in matrixNameList"
              :key="matrix.matrixName"
            >
              <RecordMatrix
                :stockId="stockId"
                :matrixType="matrix.matrixName"
                :matrixId="matrix.id"
              />
            </n-tab-pane>
          </n-tabs>

          <template v-else>
            <n-grid :cols="matrixNameList.length > 4 ? 3 : 4" item-responsive x-gap="10" y-gap="10">
              <n-grid-item
                span="4 400:4 600:2 1500:1"
                v-for="(matrix, index) in matrixNameList"
                :key="index"
              >
                <RecordMatrix
                  :stockId="stockId"
                  :matrixDateOption="
                    matrix.matrixDateStr.split(',').map((item) => ({ label: item, value: item }))
                  "
                  :matrixType="matrix.matrixName"
                  :matrixId="matrix.id"
                />
              </n-grid-item>
            </n-grid>
          </template>
        </n-card>
      </n-grid-item>
      <!--        财务分析-->
      <n-grid-item span="10">
        <n-card
          class="mb-2"
          :segmented="{
            content: true,
            footer: 'soft',
          }"
        >
          <template #header>
            <n-h2 prefix="bar" class="mb-0"> 财务分析 </n-h2>
          </template>
          <FinancialAnalysis :stockId="stockId" :stockName="stockName" />
        </n-card>
      </n-grid-item>

      <!--        大额融资与交易   我司融资负债/市值变化趋势   我司持仓累计股数变化-->
      <n-grid-item span="10">
        <n-grid x-gap="12" item-responsive :cols="6">
          <n-gi span="6 600:6 1500:2">
            <!--              大额融资与交易-->
            <LargeFnancingAndTrading :stockId="stockId" />
          </n-gi>
          <n-gi span="6 600:6 1500:2">
            <n-card
              class="mb-2"
              :segmented="{
                content: true,
                footer: 'soft',
              }"
            >
              <template #header>
                <n-h2 prefix="bar" class="mb-0"> 我司融资负债/市值变化趋势 </n-h2>
              </template>
              <MarketValueTrendEchart
                :data-list="dataObj.balMarketCapChange"
                height="240px"
                width="100%"
              />
            </n-card>
          </n-gi>
          <n-gi span="6 600:6 1500:2">
            <n-card
              class="mb-2"
              :segmented="{
                content: true,
                footer: 'soft',
              }"
            >
              <template #header>
                <n-h2 prefix="bar" class="mb-0"> 我司持仓累计股数变化 </n-h2>
              </template>
              <PositionChangeEchart
                :data-list="dataObj.shareholdingsChanges"
                height="240px"
                width="100%"
              />
            </n-card>
          </n-gi>
          <n-gi span="6 600:6 1500:2">
            <n-card
              class="mb-2"
              :segmented="{
                content: true,
                footer: 'soft',
              }"
            >
              <template #header>
                <n-h2 prefix="bar" class="mb-0"> 我司高集中度账户排名 </n-h2>
              </template>
              <highlyCentCustsTable :dataList="dataObj.highlyCentCusts" />
            </n-card>
          </n-gi>
          <n-gi span="6 600:6 1500:2">
            <n-card
              class="mb-2"
              :segmented="{
                content: true,
                footer: 'soft',
              }"
            >
              <template #header>
                <n-h2 prefix="bar" class="mb-0"> 我司客户融资余额排名 </n-h2>
              </template>
              <custFinaBalsTable :dataList="dataObj.custFinaBals" />
            </n-card>
          </n-gi>
          <n-gi span="6 600:6 1500:2">
            <n-card
              class="mb-2"
              :segmented="{
                content: true,
                footer: 'soft',
              }"
            >
              <template #header>
                <n-h2 prefix="bar" class="mb-0"> 我司客户融券余额排名 </n-h2>
              </template>
              <custShortSellBalsTable :dataList="dataObj.custShortSellBals" />
            </n-card>
          </n-gi>
        </n-grid>
      </n-grid-item>
      <!--        历史重大事件-->
      <n-grid-item span="10">
        <n-card
          class="mb-2"
          :segmented="{
            content: true,
            footer: 'soft',
          }"
        >
          <template #header>
            <n-h2 prefix="bar" class="mb-0"> 历史重大事件 </n-h2>
          </template>
          <MajorHistoricalEventEchart
            :informationData="informationData"
            :startDate="startDate"
            :stockId="stockId"
            :stockName="stockName"
            height="450px"
            width="100%"
          />
        </n-card>
      </n-grid-item>
      <n-grid-item span="10">
        <n-tabs type="card" animated>
          <n-tab-pane name="rzfzfx" display-directive="show">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(CreditCardOutlined)" />
                <span>融资负债分析</span>
              </n-space>
            </template>
            <FinancingLiabilityAnalysis :stockId="stockId" :stockName="stockName" />
          </n-tab-pane>
          <n-tab-pane name="cwfx" display-directive="if">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(BarChartOutlined)" />
                <span>财务分析</span>
              </n-space>
            </template>
            <FinancialAnalysisTab
              :informationData="informationData"
              :stockId="stockId"
              :stockName="stockName"
            />
          </n-tab-pane>
          <n-tab-pane v-if="prefixHualong" name="cwzy" display-directive="if">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(FileTextOutlined)" />
                <span>财务摘要</span>
              </n-space>
            </template>
            <n-card>
              <FinancialSummary :stockId="stockId" :stockName="stockName" />
            </n-card>
          </n-tab-pane>
          <n-tab-pane name="xsgp" display-directive="if">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(ShareAltOutlined)" />
                <span>相似股票</span>
              </n-space>
            </template>
            <n-card>
              <PhaseStock :stockId="stockId" :stockName="stockName" />
            </n-card>
          </n-tab-pane>
          <n-tab-pane name="yjxx" display-directive="show">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(WarningOutlined)" />
                <span>预警信息</span>
              </n-space>
            </template>
            <Warningdata :stockId="stockId" :stockName="stockName" />
          </n-tab-pane>
          <n-tab-pane name="peerAdjustmentHistory" display-directive="show">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(HistoryOutlined)" />
                <span>同业调整历史</span>
              </n-space>
            </template>
            <PeerAdjustmentHistory :stockId="stockId" :stockName="stockName" />
          </n-tab-pane>
          <n-tab-pane name="xwyqyfmgg" display-directive="show">
            <template #tab>
              <n-space align="center" :size="8">
                <component :is="renderIcon(NotificationOutlined)" />
                <span>新闻舆情与负面公告</span>
              </n-space>
            </template>
            <NegativeNewsData :stockId="stockId" :stockName="stockName" />
          </n-tab-pane>
        </n-tabs>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, toRaw, ref, provide } from 'vue';
  import { useRoute } from 'vue-router';
  import { decrypt } from '@/utils/crypto/cryptoUtils';
  import InformationOverview from '@/views/securityFileWindow/components/InformationOverview.vue';
  import FinancingChip from '@/views/securityFileWindow/components/marginTrading/FinancingChip.vue';
  import TradeDetails from '@/views/securityFileWindow/components/peerInfo/peerInfo.vue';
  import RiskLabel from '@/views/securityFileWindow/components/RiskLabel.vue';
  import RecordMatrix from '@/views/securityFileWindow/components/RecordMatrix.vue';
  import FinancialAnalysis from '@/views/securityFileWindow/components/SecFinancialAnalysis.vue';
  import LargeFnancingAndTrading from '@/views/securityFileWindow/components/LargeFnancingAndTrading.vue';
  import PositionChangeEchart from '@/views/securityFileWindow/components/PositionChangeEchart.vue';
  import MarketValueTrendEchart from '@/views/securityFileWindow/components/MarketValueTrendEchart.vue';
  import MajorHistoricalEventEchart from '@/views/securityFileWindow/components/MajorHistoricalEventEchart.vue';
  import custShortSellBalsTable from '@/views/securityFileWindow/components/custShortSellBalsTable.vue';
  import highlyCentCustsTable from '@/views/securityFileWindow/components/highlyCentCustsTable.vue';
  import custFinaBalsTable from '@/views/securityFileWindow/components/custFinaBalsTable.vue';
  import Warningdata from '@/views/securityFileWindow/components/Warningdata.vue';
  import PeerAdjustmentHistory from '@/views/securityFileWindow/components/PeerAdjustmentHistory.vue';
  import NegativeNewsData from '@/views/securityFileWindow/components/NegativeNewsData.vue';
  import FinancialSummary from '@/views/securityFileWindow/components/FinancialSummary.vue';
  import FinancingLiabilityAnalysis from '@/views/securityFileWindow/components/FinancingLiabilityAnalysis.vue';
  import FinancialAnalysisTab from '@/views/securityFileWindow/components/FinancialAnalysisTab.vue';
  import PhaseStock from '@/views/securityFileWindow/components/PhaseStock.vue';
  import { stockDocumentquery } from '@/views/hualong/api/backend.ts';
  import { useGlobSetting } from '@/hooks/setting';
  import { queryAccessibleMatrixConfigs } from '@/api/matrix';

  // 导入图标
  import {
    CreditCardOutlined,
    BarChartOutlined,
    FileTextOutlined,
    ShareAltOutlined,
    WarningOutlined,
    HistoryOutlined,
    NotificationOutlined
  } from '@vicons/antd';
  import { renderIcon } from '@/utils';

  const globSetting = useGlobSetting();

  const { prefixHualong } = globSetting;
  const route = useRoute();
  const stockId = ref('');
  const stockName = ref('');
  const startDate = ref('');
  const matrixNameList = ref([]);
  const informationData = ref(null);
  const dataObj = ref({
    balMarketCapChange: [],
    custFinaBals: [],
    custShortSellBals: [],
    highlyCentCusts: [],
    shareholdingsChanges: [],
  });
  provide('informationData', informationData.value);

  const setStartDate = (val, info) => {
    //console.log(JSON.stringify(info));
    informationData.value = info;
    provide('informationData', informationData.value);

    startDate.value = val;
  };

  onMounted(() => {
    // 获取地址栏参数
    let { Amount1, Amount2, Amount3 } = toRaw(route).query as any;
    stockId.value = decrypt(Amount1);
    stockName.value = decrypt(Amount2);
    get_AllEnabledMatrix();
    getstockDocumentquery();
  });

  //获取矩阵名称
  const get_AllEnabledMatrix = () => {
    queryAccessibleMatrixConfigs().then((r) => {
      if (r.code === 200) {
        matrixNameList.value = r.data;
      }
    });
  };
  //获取股票档案
  const getstockDocumentquery = () => {
    try {
      stockDocumentquery({ stkCode: stockId.value }).then((r) => {
        if (r.code === 200) {
          dataObj.value = r.data;
        }
      });
    } catch (e) {}
  };
</script>

<style>
  .levelImg {
    height: 200px;
    width: 200px;
    margin-left: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    font-weight: bold;
    opacity: 0.7;
  }

  #stockCard .n-card > .n-card-header {
    padding: 12px 5px 0px 10px !important;
  }
</style>
