<template>
  <!--我的证券池-->
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space v-if="groupId">
            <n-form-item label="证券代码">
              <SecuritySearchFund @get-value="getStockValue" />
            </n-form-item>
            <n-form-item>
              <n-space>
                <n-button type="info" @click="onSubmit"> 查询 </n-button>
                <n-button type="success" @click="addBtn"> 添加 </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :columns="columns"
          :data="dataTable"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1800"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChange"
        />

        <n-space justify="center">
          <n-pagination
            v-model:page="pageData.current"
            v-model:page-size="pageData.size"
            :item-count="total"
            :page-sizes="[10, 20, 50, 100, 300]"
            class="mt-5"
            show-size-picker
            @update:page="updatePage"
            @update:page-size="updatePageSize"
          >
            <template #suffix> 共 {{ total }} 条 </template>
          </n-pagination>
        </n-space>
      </n-grid-item>
    </n-grid>
    <n-modal
      v-model:show="showModal"
      :bordered="false"
      class="custom-card w-[450px]"
      preset="card"
      size="huge"
      title="添加股票池"
    >
      <SecuritySearchFund width="100%" @get-value="getSecCode" />
      <template #footer>
        <n-space justify="center">
          <n-button @click="showModal = false"> 取消 </n-button>
          <n-button type="info" @click="okButton"> 确定 </n-button>
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, h, onMounted, watch, computed } from 'vue';
  import { NButton, NTag, useDialog, useMessage } from 'naive-ui';
  import SecuritySearchFund from '@/components/SecuritySearch/SecuritySearchFund.vue';
  import { queryUserSecPool, addToUserSecPool, delFromUserSecPool } from '@/api/sec/userSecPoolApi';
  import { useSorter } from '@/composables/useSorter';
  import type { PageRequest } from '@/models/common/baseRequest';
  import type { UserSecPoolVO } from '@/models/sec/secModels';
  import { useUserStore } from '@/store/modules/user';
  import { setTableLevelColor } from '@/utils/ui/color/LevelColor';
  import { openEventTrace, openStockArchives } from '@/utils/goToArchives';
  const pageData = reactive<PageRequest>({
    size: 10,
    current: 1,
    ascOrDesc: '',
    orderBy: '',
  });
  const userStore = useUserStore();
  const ifSystemUser = userStore.getIfSystemUser;
  const { sorting, handleSorterChange } = useSorter(pageData);
  const props = defineProps({ groupId: String });

  watch(
    () => sorting.value,
    () => {
      queryData();
    },
    { deep: true }
  );
  const message = useMessage();

  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        type: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        fixed: 'left',

        width: '100px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        fixed: 'left',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },

      { align: 'center', width: '100px', title: '行业', key: 'industryName' },
      {
        align: 'center',
        width: '100px',
        title: '系统评级',
        key: 'level',
        sorter: true,
        render(row, index): any {
          return setTableLevelColor(row.level);
        },
      },
      {
        align: 'center',
        sorter: true,
        width: '120px',
        title: '同业评级区间',
        key: 'peerLevelRange',
      },
      {
        align: 'center',
        sorter: true,
        width: '120px',
        title: '同业折算率区间',
        key: 'peerHaircutRange',
      },
      { align: 'center', sorter: true, width: '140px', title: '最新市值(亿)', key: 'totalAmount' },
      { align: 'center', sorter: true, width: '140px', title: '最新收盘价(元)', key: 'closePrice' },
      { align: 'center', sorter: true, width: '100px', title: '滚动市盈率', key: 'rollingPe' },
      { align: 'center', sorter: true, width: '100px', title: '最新日期', key: 'date' },
      {
        align: 'center',
        width: !ifSystemUser ? '130px' : '0',
        title: '用户评级',
        key: 'userLevel',
      },
      {
        title: '操作',
        key: 'actions',
        fixed: 'right',
        width: 130,
        render(row) {
          return [
            h(
              NButton,
              {
                type: 'info',
                size: 'small',
                strong: true,
                secondary: true,
                onClick: () => {
                  openEventTrace(row.secCode, row.secName);
                },
              },
              { default: () => '风险报告' }
            ),
            ' ',
            h(
              NButton,
              {
                strong: true,
                secondary: true,
                type: 'error',
                size: 'small',
                onClick: () => {
                  del_FromUserSecPool(row);
                },
              },
              { default: () => '删除' }
            ),
          ];
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const total = ref(0);
  const loading = ref(false);
  const showModal = ref(false);
  const dataTable = ref<UserSecPoolVO>([]);
  const dialog = useDialog();

  const formInline = reactive({
    secCode: '',
  });
  //获取证券代码
  const getStockValue = (id) => {
    pageData.secCode = id;
  };
  //添加股票池
  const addBtn = () => {
    showModal.value = true;
  };
  const getSecCode = (id) => {
    formInline.secCode = id;
  };
  //添加提交按钮
  const okButton = async () => {
    if (!formInline.secCode) {
      return message.error('请选择证券！');
    }
    const { code, msg } = await addToUserSecPool({ ...formInline }, props.groupId);
    if (code == 200) {
      showModal.value = false;
      message.success(msg);
      queryData();
    } else {
      message.error(msg);
    }
  };
  //删除
  const del_FromUserSecPool = async (row: UserSecPoolVO) => {
    dialog.warning({
      title: '提示',
      content: '此操作将移除' + row.secName + ', 是否继续?',
      positiveText: '确定',
      negativeText: '不确定',
      onPositiveClick: async () => {
        const { code, msg } = await delFromUserSecPool(row.secCode || '', props.groupId);
        if (code == 200) {
          message.success('删除成功');
          queryData();
        } else {
          message.error(msg);
        }
      },
    });
  };
  //查询
  const queryData = async () => {
    loading.value = true;
    let { code, data, msg } = await queryUserSecPool(
      { ...pageData, ...sorting.value },
      props.groupId
    );
    loading.value = false;
    if (code == 200) {
      dataTable.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };
  onMounted(() => {});

  watch(
    () => props.groupId,
    () => {
      queryData();
    },
    { deep: true }
  );

  //查询按钮
  const onSubmit = () => {
    pageData.current = 1;
    queryData();
  };
  const updatePage = (page) => {
    pageData.current = page;
    queryData();
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    queryData();
  };
</script>

<style scoped></style>
