<!--策略池证券信息表格 - 担保品评级策略池/证券列表，担保品标签策略池/证券列表-->
<template>
  <div>
    <div style="float: right; padding-bottom: 5px">
      <StockSearch @get-value="handleSecuritySearch" />
    </div>

    <n-data-table
      :bordered="false"
      :columns="stockTableColumns"
      :data="stockTableData"
      :loading="isLoading"
      :scroll-x="4500"
      :single-line="false"
      @update:sorter="handleSorterChange"
    />
    <br />
    <n-space justify="center">
      <n-pagination
        v-show="total > 0"
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条</template>
      </n-pagination>
    </n-space>

    <!-- 同业详情弹框 -->
    <n-modal v-model:show="showDetailModal" class="w-3/4">
      <SecPeerDataTable :secCode="selectedStockId" :secName="selectedStockName" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { toRefs, ref, onMounted, reactive } from 'vue';
  import { DataTableColumns } from 'naive-ui';
  import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue';
  import { PageRequest } from '@/models/common/baseRequest';
  import { StockTableInfoVO } from '@/models/common/utilModels';
  // 导入表格列工厂函数
  import {
    createSecurityCodeColumn,
    createSecurityNameColumn,
    createBasicColumn,
    createLevelColumn,
    createPeerRangeColumn,
    createFinancialColumn,
    createComparisonColumn,
    createGrossMarginGroupColumn,
    createNetProfitGroupColumn,
    createIncomeGroupColumn,
  } from '@/utils/ui/table/TableColumnFactory';
  import { getStrategySecInfoTable } from '@/api/levelStrategy/levelStrategyPoolApi';
  // 定义 props
  interface Props {
    param?: any;
    loading?: boolean;
  }

  const props = defineProps<Props>();
  const { param } = toRefs(props);

  // 响应式数据
  const stockTableData = ref<StockTableInfoVO[]>([]);
  const sortOrder = ref<'asc' | 'desc' | ''>('');
  const sortField = ref('');
  const selectedStockId = ref('');
  const selectedStockName = ref('');
  const isLoading = ref(true);
  const showDetailModal = ref(false);
  const total = ref(0);
  const pageData = reactive<PageRequest>({
    size: 10,
    current: 1,
    ascOrDesc: '',
    orderBy: '',
    secCode: '',
  });

  /**
   * 显示股票详情弹窗
   */
  const handleShowStockDetail = (row: StockTableInfoVO) => {
    showDetailModal.value = true;
    selectedStockId.value = row.stockId;
    selectedStockName.value = row.stockName;
  };

  /**
   * 证券搜索处理
   */
  const handleSecuritySearch = (secCode: string) => {
    pageData.secCode = secCode;
    pageData.current = 1;
    fetchPolicyPoolSecuritiesData();
  };
  /**
   * 策略池证券信息表格列配置
   */
  const stockTableColumns: DataTableColumns<StockTableInfoVO> = [
    // 证券基本信息列
    createSecurityCodeColumn(),
    createSecurityNameColumn(),
    createBasicColumn('行业', 'industryName'),
    createBasicColumn('二级行业', 'industryNameTwo'),

    // 评级列
    createLevelColumn('策略评级', 'afterLevel'),
    createLevelColumn('塔金评级', 'level'),

    // 同业分类区间列
    createPeerRangeColumn(handleShowStockDetail),

    // 基础财务数据列
    createFinancialColumn({ title: '总市值(亿元)', key: 'totalAmount', width: '160px' }),
    createFinancialColumn({ title: '股价(元)', key: 'closes', width: '110px' }),
    createFinancialColumn({ title: '财务评分', key: 'financialScore', width: '120px' }),
    createFinancialColumn({ title: '综合评分', key: 'totalScore', width: '120px' }),

    // 比较数据列
    createComparisonColumn({ title: '市净率', key: 'pb', medianKey: 'midPb' }),
    createComparisonColumn({ title: '市盈率', key: 'pe', medianKey: 'midPe' }),

    // 财务指标列
    createFinancialColumn({ title: '每股净资产(元/股)', key: 'netAssetValuePerShare' }),
    createFinancialColumn({ title: '扣非净利润(万元)', key: 'kfnetprofit', width: '160px' }),

    // 毛利率分组列
    createGrossMarginGroupColumn(),

    // 扣非净利润分组列
    createNetProfitGroupColumn(),

    // 营业收入分组列
    createIncomeGroupColumn(),

    // 其他财务指标列
    createFinancialColumn({
      title: '3年营收负债增速差',
      key: 'incomeDebtSubThreeYear',
      width: '170px',
    }),
    createFinancialColumn({
      title: '5年营收负债增速差',
      key: 'incomeDebtSubFiveYear',
      width: '170px',
    }),
    createFinancialColumn({ title: '5年亏损次数', key: 'losses' }),

    // 标签相关列
    createBasicColumn('标签数量', 'count', { sorter: true, width: '120px' }),
    createFinancialColumn({ title: '高风险标签数量', key: 'highRiskLabelCount', width: '160px' }),
    createBasicColumn('标签内容', 'labels', { ellipsis: { tooltip: true } }),
  ];

  /**
   * 处理表格排序变化
   */
  const handleSorterChange = (sorter: any) => {
    if (!sorter.order) {
      sortOrder.value = '';
      sortField.value = '';
    } else {
      sortOrder.value = sorter.order === 'ascend' ? 'asc' : 'desc';
      sortField.value = sorter.columnKey;
    }
    pageData.current = 1;
    fetchPolicyPoolSecuritiesData();
  };

  /**
   * 获取策略池证券信息数据
   */
  const fetchPolicyPoolSecuritiesData = async () => {
    isLoading.value = true;
    const { code, data } = await getStrategySecInfoTable(param.value.id, param.value.labelName, {
      ...pageData,
      ascOrDesc: sortOrder.value,
      orderBy: sortField.value,
    });
    isLoading.value = false;

    if (code === 200) {
      stockTableData.value = data.records;
      total.value = data.total;
    }
  };

  onMounted(() => {
    fetchPolicyPoolSecuritiesData();
  });

  /**
   * 更新页码
   */
  const updatePage = (page: number) => {
    pageData.current = page;
    fetchPolicyPoolSecuritiesData();
  };

  /**
   * 更新页面大小
   */
  const updatePageSize = (pageSize: number) => {
    pageData.current = 1;
    pageData.size = pageSize;
    fetchPolicyPoolSecuritiesData();
  };
</script>

<style scoped></style>
