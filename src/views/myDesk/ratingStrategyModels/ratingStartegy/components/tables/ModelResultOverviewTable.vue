<!--  塔金风险策略研究/查看已评级、查看未评级、查看评级差异 表格-->
<template>
  <div>
    <!-- 搜索和导出区域 -->
    <div style="float: right; padding-bottom: 5px">
      <div style="display: flex; align-items: center; gap: 12px">
        <!-- 导出按钮 -->
        <n-button type="warning" @click="handleExport">
          {{ '导出Excel' }}
          <template #icon>
            <n-icon>
              <CloudDownloadOutline />
            </n-icon>
          </template>
        </n-button>
        <n-space>
          <SecurityInfoSearch @on-security-selected="onSecuritySelected" />
        </n-space>
      </div>
    </div>

    <n-data-table
      :key="props.queryType"
      :columns="createColumns"
      :data="stockData"
      :loading="loading"
      :scroll-x="5000"
      :single-line="false"
      @update:sorter="handleSorterChangeWrapper"
    />
    <br />
    <n-space justify="center">
      <n-pagination
        v-show="total > 0"
        v-model:page="pageRequest.current"
        v-model:page-size="pageRequest.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条</template>
      </n-pagination>
    </n-space>

    <!--  同业详情弹框  -->

    <n-modal v-model:show="showDetailModal" class="w-3/4">
      <SecPeerDataTable :secCode="stockIdTwo" :secName="stockName" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { DataTableColumn, NIcon } from 'naive-ui';
  import { DataTableColumns } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import { ModelLevelInfoType } from '@/enums/levelSrtEnum';
  import type { StockTableInfoVO } from '@/models/common/utilModels';
  import useEnhancedPageQuery from '@/hooks/useEnhancedPageQuery';

  // 导入表格列工厂函数
  import {
    createBasicColumn,
    createComparisonColumn,
    createFinancialColumn,
    createGrossMarginGroupColumn,
    createIncomeGroupColumn,
    createLevelColumn,
    createMarketValueGroupColumn,
    createNetProfitGroupColumn,
    createPeerRangeColumn,
    createSecurityCodeColumn,
    createSecurityNameColumn,
    renderLevelValue,
  } from '@/utils/ui/table/TableColumnFactory';
  import {
    exportModelSecLevelSrtInfoByCondition,
    getModelSecLevelSrtInfoByCondition,
  } from '@/api/levelStrategy/model/levelStrategyModelApi';
  import { CloudDownloadOutline } from '@vicons/ionicons5';
  import { exportExcel } from '@/api/system/https';

  /**
   * 组件 Props 接口定义
   * @interface Props
   * @description 定义组件接收的属性类型
   */
  interface Props {
    /** 查询类型配置 */
    queryType: ModelLevelInfoType;
    /** 模型ID */
    modelId: null | number;
    /** 过滤手动评级 */
    filterManualLevel?: boolean;
  }

  /**
   * 搜索表单类型定义
   * @interface SearchForm
   * @description 定义搜索表单的数据结构
   */
  interface SearchForm {
    secCode: string | null;
  }

  /**
   * 组件 Props 定义
   * @description 使用 withDefaults 设置默认值
   */
  const props = withDefaults(defineProps<Props>(), {
    filterManualLevel: false,
  });

  // 用户状态管理
  const userStore = useUserStore();
  const ifSystemUser: boolean = userStore.getIfSystemUser;

  // 其他状态
  const stockIdTwo: Ref<string> = ref('');
  const stockName: Ref<string> = ref('');
  const showDetailModal: Ref<boolean> = ref(false);
  const filterEqualLevel: Ref<boolean> = ref(false);

  /**
   * API 适配器函数
   * @description 将 useEnhancedPageQuery 的参数格式适配为原 API 的多参数格式
   */
  const queryFunctionAdapter = async (params: any) => {
    const pageRequest = {
      current: params.current,
      size: params.size,
      ascOrDesc: params.ascOrDesc,
      orderBy: params.orderBy,
      secCode: params.secCode,
    };

    return await getModelSecLevelSrtInfoByCondition(
      pageRequest,
      props.queryType,
      Number(props.modelId),
      filterEqualLevel.value,
      props.filterManualLevel
    );
  };

  /**
   * 使用增强版分页查询 Hook
   * @description 自动管理分页、加载、错误处理等状态
   */
  const {
    dataList: stockData,
    loading,
    total,
    pageRequest,
    onSubmit,
    updatePage,
    updatePageSize,
    handleSorterChangeWrapper,
    updateSearchForm,
    refresh,
  } = useEnhancedPageQuery<StockTableInfoVO[], SearchForm>(queryFunctionAdapter, {
    autoInit: true,
    initialFormData: {
      secCode: null,
    },
    showErrorMessage: true,
  });

  /**
   * 证券搜索处理
   * @description 处理证券搜索输入，使用 Hook 提供的搜索功能
   * @param secCode 证券代码
   * @param _secName 证券名称（未使用，但保持接口一致性）
   */
  const onSecuritySelected = (secCode: string, _secName: string | null): void => {
    updateSearchForm('secCode', secCode);
    onSubmit();
  };

  /**
   * 显示同业详情弹框
   * @description 点击同业分类区间时显示详情
   * @param row 表格行数据
   */
  const getDetailModal = (row: StockTableInfoVO): void => {
    showDetailModal.value = true;
    stockIdTwo.value = row.stockId || '';
    stockName.value = row.stockName || '';
  };

  // ==================== 策略专用列创建函数 ====================

  /**
   * 创建系统策略列
   */
  const createSystemStrategyColumn = (): DataTableColumn<StockTableInfoVO> => ({
    title: '系统策略',
    key: 'oldReason',
    align: 'center',
    width: '140px',
    ellipsis: { tooltip: true },
    render(row) {
      return renderLevelValue(row.oldReason || '');
    },
  });

  /**
   * 创建用户策略列
   */
  const createUserStrategyColumn = (): DataTableColumn<StockTableInfoVO> => ({
    title: '用户策略',
    key: 'oldReason',
    align: 'center',
    width: '140px',
    ellipsis: { tooltip: true },
    render(row) {
      return renderLevelValue(row.oldReason || '');
    },
  });

  /**
   * 创建当前策略列
   */
  const createCurrentStrategyColumn = (): DataTableColumn<StockTableInfoVO> => ({
    title: '当前策略',
    key: 'newReason',
    align: 'center',
    width: '140px',
    ellipsis: { tooltip: true },
    render(row) {
      return renderLevelValue(row.newReason || '');
    },
  });

  /**
   * 创建模型评级结果列
   */
  const createModelRatingColumn = (): DataTableColumn<StockTableInfoVO> => ({
    title: '模型评级结果',
    key: 'afterLevel',
    align: 'center',
    width: '140px',
    sorter: true,
    render(row) {
      return renderLevelValue(row.afterLevel || '');
    },
  });

  /**
   * 创建用户评级列（带权限控制）
   */
  const createUserRatingColumn = (): DataTableColumn<StockTableInfoVO> => ({
    title: '用户评级',
    key: 'userLevel',
    align: 'center',
    width: '140px',
    sorter: true,
    render(row) {
      return renderLevelValue(row.userLevel || '');
    },
  });

  /**
   * 表格列配置
   * @description 动态生成表格列配置
   */
  const createColumns = computed((): DataTableColumns<StockTableInfoVO> => {
    const cols: DataTableColumns<StockTableInfoVO> = [
      // 基础列
      createSecurityCodeColumn(),
      createSecurityNameColumn(),
      createBasicColumn('行业', 'industryName', { width: '140px' }),
      createBasicColumn('二级行业', 'industryNameTwo', { width: '140px' }),

      // 条件显示的策略列
      ...(props.queryType === ModelLevelInfoType.DIFFERENCE_RATED_SEC
        ? [createSystemStrategyColumn()]
        : []),
      ...(props.queryType === ModelLevelInfoType.DIFFERENCE_USER_RATED_SEC
        ? [createUserStrategyColumn()]
        : []),
      ...(props.queryType === ModelLevelInfoType.DIFFERENCE_RATED_SEC ||
      props.queryType === ModelLevelInfoType.DIFFERENCE_USER_RATED_SEC
        ? [createCurrentStrategyColumn()]
        : []),

      // 评级列
      createModelRatingColumn(),
      createLevelColumn('系统评级', 'level', { width: '140px', sorter: true }),
      // ...(ifSystemUser ? [] : [createUserRatingColumn()]),

      // 同业分类区间列
      createPeerRangeColumn(getDetailModal),

      // 市值分组列
      createMarketValueGroupColumn(),

      // 基础财务数据列
      createFinancialColumn({ title: '股价(元)', key: 'closes', width: '100px' }),
      createFinancialColumn({ title: '财务评分', key: 'financialScore', width: '100px' }),
      createFinancialColumn({ title: '综合评分', key: 'totalScore', width: '100px' }),

      // 比较数据列
      createComparisonColumn({ title: '市净率', key: 'pb', medianKey: 'midPb', width: '140px' }),
      createComparisonColumn({ title: '市盈率', key: 'pe', medianKey: 'midPe', width: '140px' }),

      // 详细财务指标列
      createFinancialColumn({
        title: '每股净资产(元/股)',
        key: 'netAssetValuePerShare',
        width: '140px',
      }),
      createFinancialColumn({
        title: '扣非净利润(万元)',
        key: 'kfnetprofit',
        width: '160px',
      }),

      // 财务指标分组列
      createGrossMarginGroupColumn(),
      createNetProfitGroupColumn(),
      createIncomeGroupColumn(),

      // 其他财务指标
      createFinancialColumn({
        title: '3年营收负债增速差',
        key: 'incomeDebtSubThreeYear',
        width: '150px',
      }),
      createFinancialColumn({
        title: '5年营收负债增速差',
        key: 'incomeDebtSubFiveYear',
        width: '150px',
      }),
      createFinancialColumn({ title: '5年亏损次数', key: 'losses' }),

      // 标签相关列
      createBasicColumn('标签数量', 'count', { sorter: true }),
      createFinancialColumn({ title: '高风险标签数量', key: 'highRiskLabelCount' }),
      createBasicColumn('标签内容', 'labels', {
        ellipsis: { tooltip: true },
        width: '250px',
      }),
    ];

    // 过滤掉宽度为 '0' 的列（隐藏列）
    return cols.filter((col) => {
      const column = col as any;
      return column.width !== '0';
    });
  });

  /**
   * 导出 Excel 按钮点击事件处理函数
   * @description 导出 Excel 文件
   */
  const handleExport = async () => {
    try {
      const { queryType, modelId, filterManualLevel } = props;
      // 获取当前查询参数
      const exportParams = {
        ...pageRequest,
        queryType,
        modelId: Number(modelId),
        filterEqualLevel: filterEqualLevel.value,
        filterManualLevel,
      };

      // 调用导出函数
      await exportExcel(exportParams, exportModelSecLevelSrtInfoByCondition);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  /**
   * 监听 props 变化，自动刷新数据
   * @description 当查询类型、模型ID或过滤条件变化时，重新获取数据
   */
  watch(
    [
      () => props.queryType,
      () => props.modelId,
      () => props.filterManualLevel,
      () => filterEqualLevel.value,
    ],
    () => {
      refresh();
    },
    { deep: true }
  );
</script>

<style scoped></style>
