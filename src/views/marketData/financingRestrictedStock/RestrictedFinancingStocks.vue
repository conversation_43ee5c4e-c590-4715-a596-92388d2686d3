<!--市场数据/融资受限股票-->
<template>
  <n-card bordered>
    <n-grid cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="交易日期">
              <n-date-picker
                v-model:formatted-value="formInline.tradingDate"
                :is-date-disabled="disableDateValue"
                type="date"
                update-value-on-close
                value-format="yyyy-MM-dd"
              />
            </n-form-item>
            <n-form-item label="证券名称">
              <SecuritySearchFund show-sec-category width="230px" @get-value="getStockId" />
            </n-form-item>
            <!--            <n-form-item label="证券品种">-->
            <!--              <n-select-->
            <!--                clearable-->
            <!--                style="width: 200px"-->
            <!--                v-model:value="formInline.secType"-->
            <!--                :options="secOptions"-->
            <!--              />-->
            <!--            </n-form-item>-->

            <n-form-item label="证券分类">
              <n-select
                v-model:value="formInline.secCategory"
                :options="secCategoryOptions"
                clearable
                style="width: 200px"
              />
            </n-form-item>

            <n-form-item label="交易市场">
              <n-select
                v-model:value="formInline.market"
                :options="exchangeTypeOptions"
                clearable
                filterable
                placeholder="请选择"
                style="width: 200px"
              />
            </n-form-item>
            <n-form-item>
              <n-space>
                <n-button attr-type="button" type="info" @click="onSubmit"> 查询</n-button>
                <n-button :loading="exportLoading" type="warning" @click="exportBtn">
                  导出
                </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :columns="columns"
          :data="dataTableList"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1500"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChangeWrapper"
        />

        <n-space justify="center">
          <n-space justify="center">
            <n-pagination
              v-model:page="pageRequest.current"
              v-model:page-size="pageRequest.size"
              :item-count="total"
              :page-sizes="[10, 50, 100, 300]"
              class="mt-5"
              show-size-picker
              @update:page="updatePage"
              @update:page-size="updatePageSize"
            >
              <template #suffix> 共 {{ total }} 条</template>
            </n-pagination>
          </n-space>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref } from 'vue';
  import { NButton, NTag, useMessage } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import SecuritySearchFund from '@/components/SecuritySearch/SecuritySearchFund.vue';
  import { getEnumOptions } from '@/enums/baseEnum';
  import { exportExcel } from '@/api/system/https';
  import { SecType, SecTypeInfo } from '@/enums/secEnum';
  import { openStockArchives } from '@/utils/goToArchives';
  import { formatTime } from '@/utils/common/date/dateUtil';
  import { getSelectableDates } from '@/api/peerSec/peerBondInfoApi';
  import { PeerSecDataTypeEnum } from '@/enums/peerSec/peerBondInfoEnum';
  import { getSecTypeAndSubTypes } from '@/api/sec/secInfoApi';
  import { getFinancingRestrictedStock } from '@/api/marketData/financingRestrictedStockApi';
  import { getFinancialColor } from '@/utils/ui/color/LevelColor';

  const secTypeAndSubTypes = ref<any>([]);

  const secOptions = getEnumOptions(SecTypeInfo);
  const secCategoryOptions = computed(() => {
    //如果等于null，将3个合并
    if (formInline.secType == null && secTypeAndSubTypes.value.length > 0) {
      return [
        ...secTypeAndSubTypes.value
          .filter((item) => item.secType == SecType.STOCK)[0]
          .secCategory.map((item) => ({ label: item, value: item })),
        ...secTypeAndSubTypes.value
          .filter((item) => item.secType == SecType.BOND)[0]
          .secCategory.map((item) => ({ label: item, value: item })),
        ...secTypeAndSubTypes.value
          .filter((item) => item.secType == SecType.FUND)[0]
          .secCategory.map((item) => ({ label: item, value: item })),
      ];
    } else if (secTypeAndSubTypes.value.length == 0) {
      return [];
    }

    return secTypeAndSubTypes.value
      .filter((item) => item.secType == formInline.secType)[0]
      .secCategory.map((item) => ({ label: item, value: item }));
  });

  const get_SecTypeAndSubTypes = async () => {
    const { code, msg, data } = await getSecTypeAndSubTypes();
    if (code === 200) {
      secTypeAndSubTypes.value = data;
    }
  };

  const exchangeTypeOptions = ref([
    { label: '上交所', value: '上交所' },
    { label: '深交所', value: '深交所' },
    { label: '北交所', value: '北交所' },
  ]);

  const message = useMessage();
  const userStore = useUserStore();
  const dataTableList = ref<any>([]);
  const dataOptions = ref([]);
  const exportLoading = ref(false);

  const setStatus = (type) => {
    if (type != 0 && type != 1) {
      return type;
    }
    return h(
      NTag,
      {
        type: type == 0 ? 'success' : 'error',
      },
      {
        default: () => (type == 0 ? '是' : '否'),
      }
    );
  };

  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        key: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      { align: 'center', width: '110px', title: '交易日期', key: 'tradingDate' },

      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '100px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '交易市场',
        key: 'market',
      },

      { align: 'center', width: '100px', title: '证券分类', key: 'secCategory' },
      {
        align: 'center',
        width: '100px',
        title: '是否融资受限',
        key: 'ifFinancingRestricted',
        render(row) {
          return setStatus(row.ifFinancingRestricted);
        },
      },

      {
        align: 'center',
        width: '110px',
        title: '前一交易日担保物比例(%)',
        sorter: true,
        key: 'lastTradeDayAssureRatio',
      },
      {
        align: 'center',
        width: '110px',
        title: '前一交易日静态市盈率',
        sorter: true,
        key: 'lastTradeDayStaticPe',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.lastTradeDayStaticPe) },
            },
            { default: () => row.lastTradeDayStaticPe }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '是否融资标的',
        key: 'ifFinancingTarget',
        render(row) {
          return setStatus(row.ifFinancingTarget);
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const loading = ref(true);

  const formInline = reactive({
    tradingDate: null,
    secCode: null,
    secType: null,
    secCategory: null,
    market: null,
  });
  const getStockId = (id, name) => {
    formInline.secCode = id;
  };
  //只能选peerSecDataOptions中的日期
  const disableDateValue = (date: Date) => {
    return !dataOptions.value.some((item) => item.value === formatTime(date, 'YYYY-MM-DD'));
  };
  //获取当前类型所有可选日期
  const get_SelectableDates = async () => {
    const { code, data, msg } = await getSelectableDates(PeerSecDataTypeEnum.collateral);
    if (code == 200) {
      dataOptions.value = data.map((item) => ({ label: item, value: item }));
      if (data && data.length > 0) {
        formInline.tradingDate = data[0];
        get_FinancingRestrictedStock(pageRequest);
      }
    }
  };
  // 查询
  const get_FinancingRestrictedStock = async (pageRequest: PageRequest) => {
    loading.value = true;
    let { code, data, msg } = await getFinancingRestrictedStock({
      ...pageRequest,
      ...formInline,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  // 导出
  const exportBtn = async () => {
    let { token } = userStore.getUserInfo;
    exportLoading.value = true;
    await exportExcel(
      {
        ...formInline,
      },
      '/mrgTrd/market/exportFinancingRestrictedStock',
      token
    );
    exportLoading.value = false;
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_FinancingRestrictedStock);

  onMounted(() => {
    get_SecTypeAndSubTypes();
    get_SelectableDates();
  });

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
