<template>
  <!--  按基金规模大小-->
  <div class="min-h-[230px]">
    <div v-for="(item, index) in fundConfigData" :key="index">
      <n-h3 class="mb-2" prefix="bar" type="success">
        <n-space>
          <n-select
            v-model:value="item.fundScaleCalculateDirection"
            :consistent-menu-width="false"
            :disabled="ifModifyPermission === 1"
            :options="calculateOptions"
            class="w-[90px]"
          />
          <n-input-number
            v-model:value="item.fundScaleLeftRange"
            :disabled="ifModifyPermission === 1"
            :show-button="false"
            class="w-[80px]"
          >
            <template #suffix> 亿 </template>
          </n-input-number>

          <n-button
            v-if="item.fundScaleCalculateDirection === '区间'"
            secondary
            strong
            type="tertiary"
          >
            至
          </n-button>
          <n-input-number
            v-if="item.fundScaleCalculateDirection === '区间'"
            v-model:value="item.fundScaleRightRange"
            :disabled="ifModifyPermission === 1"
            :show-button="false"
            class="w-[80px]"
          >
            <template #suffix> 亿 </template>
          </n-input-number>

          <n-button
            v-if="item.ifAdjustOut === CommonStatus.DISABLE"
            secondary
            strong
            type="tertiary"
          >
            折算率
          </n-button>
          <n-input-number
            v-if="item.ifAdjustOut === CommonStatus.DISABLE"
            v-model:value="item.designateHaircut"
            :disabled="ifModifyPermission === 1"
            :show-button="false"
            class="w-[110px]"
          />

          <n-select
            v-model:value="item.ifAdjustOut"
            :consistent-menu-width="false"
            :disabled="ifModifyPermission === 1"
            :options="[
              { label: '调出担保证券', value: CommonStatus.ENABLE },
              { label: '折算率', value: CommonStatus.DISABLE },
            ]"
            class="w-[140px]"
            @update:value="() => (item.designateHaircut = null)"
          />
          <n-button
            :disabled="ifModifyPermission === 1"
            :title="ifModifyPermission === 1 ? '无删除权限' : '删除规则'"
            icon-placement="right"
            secondary
            strong
            type="error"
            @click="deleteRule(index)"
          >
            <template #icon>
              <n-icon>
                <Close />
              </n-icon>
            </template>
          </n-button>
        </n-space>
      </n-h3>
    </div>
    <n-button
      :disabled="ifModifyPermission === 1"
      :title="ifModifyPermission === 1 ? '无添加权限' : '添加规则'"
      class="w-full"
      secondary
      strong
      type="success"
      @click="addRule"
    >
      添加规则
    </n-button>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { LabelValueType } from '@/models/common/basic/commonModels';
  import { Close } from '@vicons/ionicons5';
  import { CommonStatus } from '@/enums/baseEnum';

  const props = defineProps({
    calculateRuleType: Number,
    defaultValue: Object,
    fundType: String,
    fundCalculateType: String,
    ifModifyPermission: {
      type: Number,
      default: 0,
    },
  });

  const calculateOptions = ref<LabelValueType[]>([
    { label: '大于', value: '大于' },
    { label: '大于等于', value: '大于等于' },
    { label: '小于', value: '小于' },
    { label: '小于等于', value: '小于等于' },
    { label: '区间', value: '区间' },
  ]);

  const fundConfigData = ref<any>([]);

  const addRule = () => {
    // 检查权限
    if (props.ifModifyPermission === 1) {
      return;
    }

    fundConfigData.value.push({
      calculateRuleType: props.calculateRuleType,
      fundCalculateType: props.fundCalculateType,
      fundType: props.fundType,
      fundScaleCalculateDirection: '小于', //基金规模计算方向
      fundScaleLeftRange: 0, //基金规模（亿元）左区间
      fundScaleRightRange: 0, //基金规模（亿元）右边区间
      designateHaircut: 0, //满足当前规则对应的指定折算率
      ifAdjustOut: CommonStatus.DISABLE, //是否调出
    });
  };

  const deleteRule = (index: number) => {
    // 检查权限
    if (props.ifModifyPermission === 1) {
      return;
    }

    fundConfigData.value.splice(index, 1);
  };

  watch(
    () => props.defaultValue,
    (newArr) => {
      fundConfigData.value = [];
      if (newArr) {
        newArr.forEach((item) => {
          fundConfigData.value.push({
            calculateRuleType: item.calculateRuleType,
            fundType: item.fundType,
            fundScaleCalculateDirection: item.fundScaleCalculateDirection,
            fundCalculateType: item.fundCalculateType,
            fundScaleLeftRange: item.fundScaleLeftRange,
            ifAdjustOut: item.ifAdjustOut,
            fundScaleRightRange: item.fundScaleRightRange,
            designateHaircut: item.designateHaircut,
          });
        });
      }
    },
    { deep: true }
  );

  //将fundConfigData暴露出去
  const getFundConfigData = () => {
    return fundConfigData.value;
  };
  defineExpose({ getFundConfigData });
</script>

<style scoped></style>
