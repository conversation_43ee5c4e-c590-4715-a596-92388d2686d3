<!--两融客户分析/客户管理-->
<template>
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="客户名称">
              <CustSearch />
            </n-form-item>
            <n-form-item>
              <n-space>
                <n-button type="info" @click="onSubmit"> 查询 </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :checked-row-keys="selectData"
          :columns="columns"
          :data="dataTable"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1600"
          :single-line="false"
          bordered
          @update:checked-row-keys="handleCheck"
        />

        <n-space justify="center">
          <n-pagination
            v-model:page="pageData.current"
            v-model:page-size="pageData.size"
            :item-count="total"
            :page-sizes="[10, 20, 50, 100, 300]"
            class="mt-5"
            show-size-picker
            @update:page="updatePage"
            @update:page-size="updatePageSize"
          >
            <template #suffix> 共 {{ total }} 条 </template>
          </n-pagination>
        </n-space>
      </n-grid-item>
    </n-grid>
    <n-modal
      v-model:show="showModal"
      :bordered="false"
      class="custom-card w-[450px]"
      preset="card"
      size="huge"
    >
      <template #header>
        <n-h4 class="mb-0" prefix="bar">
          {{ applyRow ? '审核中(' + applyRow.secName + ')' : '一键审核' }}
        </n-h4>
      </template>
      {{ applyRow ? '申请理由:' + applyRow.comment : `您一共选中了${selectData.length}条数据。` }}
      <template #footer>
        <n-space justify="center">
          <n-button @click="showModal = false"> 取消 </n-button>
          <n-button type="info" @click="confirmApply(2)"> 审核通过 </n-button>
          <n-button type="error" @click="confirmApply(3)"> 审核拒绝 </n-button>
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script setup>
  import { ref, reactive, h, onMounted } from 'vue';
  import { NButton, NTag, useMessage } from 'naive-ui';
  import { openClientFile } from '../../../utils/goToArchives';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { formatTime } from '../../../utils/common/date/dateUtil';
  import { multiCensorAdmissionRecords } from '@/api/marginTrading/collateral/collateralBusiness';
  const message = useMessage();

  const columns = [
    {
      title: '序号',
      align: 'center',
      type: 'index',
      width: '70px',
      render(row, index) {
        return index + 1;
      },
    },
    {
      title: '客户代码',
      align: 'center',
      key: 'custId',
      width: '150px',
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openClientFile(row.custId, row.custName);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.custId }
        );
      },
    },
    {
      title: '客户名称',
      align: 'center',
      key: 'secCode',
      width: '150px',
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openClientFile(row.custId, row.custName);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.custName }
        );
      },
    },
    { align: 'center', title: '总资产(万元)', key: 'val1' },
    { align: 'center', title: '负债余额(万元)', key: 'val3' },
    { align: 'center', title: '持仓市值(亿元)', key: 'val4' },
    { align: 'center', title: '维保比例(%)', key: 'val5' },
  ];
  const total = ref(3);
  const loading = ref(true);
  const showModal = ref(false);

  const applyRow = ref(null);
  const dataTable = ref([
    {
      custId: '660001',
      custName: '客户名称1',
      val1: '5,660',
      val2: '1,000',
      val3: '1,000',
      val4: '2210',
      val5: '120',
    },
    {
      custId: '660002',
      custName: '客户名称2',
      val1: '2,000',
      val2: '2,000',
      val3: '4,000',
      val4: '280',
      val5: '150',
    },
    {
      custId: '660003',
      custName: '客户名称3',
      val1: '8,100',
      val2: '1,000',
      val3: '3,000',
      val4: '830',
      val5: '150',
    },
  ]);
  const selectData = ref([]);

  //操作
  const applyBtn = (row) => {
    applyRow.value = row;
    showModal.value = true;
  };

  const confirmApply = async (val) => {
    let arr = [];
    if (applyRow.value) {
      arr.push({ id: applyRow.value.id, censorStatus: val });
    } else {
      selectData.value.forEach((item) => {
        arr.push({ id: item, censorStatus: val });
      });
    }
    const { code, msg, data } = await multiCensorAdmissionRecords(arr);
    if (code == 200) {
      showModal.value = false;
      message.success(msg);
      list_CollateralAdmissionCensor();
      selectData.value = [];
    } else {
      message.error(msg);
    }
  };
  const applyOptions = [
    {
      label: '待审核',
      value: '1',
    },
    {
      label: '审核通过',
      value: '2',
    },
    {
      label: '审核拒绝',
      value: '3',
    },
  ];

  const formInline = reactive({
    status: null,
  });
  const pageData = reactive({
    size: 10,
    current: 1,
  });
  //查询
  const list_CollateralAdmissionCensor = async () => {
    // loading.value=true;
    // let {code, data,msg} = await listCollateralAdmissionCensor({...formInline,...pageData});
    loading.value = false;
    //
    // if(code==200){
    //   collateralHaircutItem.value=data.records;
    //   total.value=data.total;
    // }else{
    //   message.error(msg)
    // }
  };
  onMounted(() => {
    list_CollateralAdmissionCensor();
  });
  //选中行
  const handleCheck = (rowKeys) => {
    selectData.value = rowKeys;
    //console.log(rowKeys);
  };

  const onSubmit = () => {
    pageData.current = 1;
    list_CollateralAdmissionCensor();
  };
  const updatePage = (page) => {
    pageData.current = page;
    list_CollateralAdmissionCensor();
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    list_CollateralAdmissionCensor();
  };
</script>

<style scoped></style>
