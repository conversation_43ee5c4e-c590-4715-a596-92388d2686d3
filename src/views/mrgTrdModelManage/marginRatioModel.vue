<!--两融业务模型管理/保证金比例模型管理/保证金比例模型（兴业版本）-->
<template>
  <div>
    <!--  保证金比例模型-->
    <n-card bordered>
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <!-- 模型列表 -->
        <n-grid-item span="4">
          <n-card>
            <template #header>
              <n-h2 class="mb-0" prefix="bar" type="error"> 模型列表</n-h2>
            </template>
            <template #header-extra>
              <n-button v-show="!buttonHiding" attr-type="button" type="info" @click="onCreateRule">
                <template #icon>
                  <n-icon :component="AddOutline" />
                </template>
                添加模型
              </n-button>
            </template>
            <n-spin :show="modelsLoading">
              <n-grid id="ratingStrategyModels" cols="12" item-responsive x-gap="12" y-gap="12">
                <n-grid-item
                  v-for="(item, index) in modelsList"
                  :key="index"
                  class="cursor-pointer"
                  span="3"
                >
                  <n-card
                    :class="[
                      'model-card',
                      currentModelId == item.modelId ? 'model-card--active' : '',
                      item.enableStatus == 0 ? 'model-card--enabled' : 'model-card--disabled',
                    ]"
                    hoverable
                    @click="changeActiveModel(item)"
                  >
                    <template #header>
                      <div class="model-header">
                        <div class="model-title-section">
                          <n-h3 class="model-title">
                            <n-ellipsis :line-clamp="2">
                              {{ item.modelName }}
                            </n-ellipsis>
                          </n-h3>
                          <!-- 状态指示器 -->
                          <div class="model-status-indicators">
                            <n-tag
                              v-if="item.enableStatus == 0"
                              :bordered="false"
                              class="status-tag status-tag--enabled"
                              size="small"
                              type="success"
                            >
                              <template #icon>
                                <n-icon :component="CheckmarkOutline" />
                              </template>
                              启用中
                            </n-tag>
                            <n-tag
                              v-else
                              :bordered="false"
                              class="status-tag status-tag--disabled"
                              size="small"
                              type="default"
                            >
                              <template #icon>
                                <n-icon :component="CloseOutline" />
                              </template>
                              已禁用
                            </n-tag>
                            <n-tag
                              v-if="item.mainStatus == 0"
                              :bordered="false"
                              class="status-tag status-tag--main"
                              size="small"
                              type="warning"
                            >
                              <template #icon>
                                <n-icon :component="StarOutline" />
                              </template>
                              主模型
                            </n-tag>
                          </div>
                        </div>
                      </div>
                    </template>

                    <!-- 操作按钮区域 -->
                    <div v-show="!buttonHiding" class="model-actions">
                      <div class="model-switches">
                        <div class="switch-item">
                          <span class="switch-label">启用状态</span>
                          <n-switch
                            :value="item.enableStatus == 0"
                            class="custom-switch"
                            @update:value="
                              (val) => {
                                updateEnableStatus(val, item);
                              }
                            "
                          >
                            <template #checked> 启用</template>
                            <template #unchecked> 禁用</template>
                          </n-switch>
                        </div>
                        <div class="switch-item">
                          <span class="switch-label">主模型</span>
                          <n-switch
                            :value="item.mainStatus == 0"
                            class="custom-switch"
                            @update:value="
                              (val) => {
                                updateMainStatus(val, item);
                              }
                            "
                          >
                            <template #checked> 主模型</template>
                            <template #unchecked> 非主模型</template>
                          </n-switch>
                        </div>
                      </div>
                      <div class="model-buttons">
                        <n-button
                          class="action-button action-button--edit"
                          secondary
                          size="small"
                          strong
                          type="info"
                          @click.stop="editModel(item)"
                        >
                          <template #icon>
                            <n-icon :component="CreateOutline" />
                          </template>
                          修改
                        </n-button>
                        <n-button
                          class="action-button action-button--delete"
                          secondary
                          size="small"
                          strong
                          type="error"
                          @click.stop="deleteModel(item)"
                        >
                          <template #icon>
                            <n-icon :component="TrashOutline" />
                          </template>
                          删除
                        </n-button>
                      </div>
                    </div>

                    <!-- 备注信息 -->
                    <div class="model-description">
                      <n-ellipsis :tooltip="false" expand-trigger="click" line-clamp="2">
                        <n-text class="description-text" depth="3">
                          <template v-if="item.remark">
                            {{ item.remark }}
                          </template>
                          <template v-else>
                            <span class="no-description">暂无备注信息</span>
                          </template>
                        </n-text>
                      </n-ellipsis>
                    </div>

                    <template #action>
                      <div class="model-footer">
                        <div class="time-info">
                          <div class="time-item">
                            <n-icon :component="PersonOutline" class="time-icon" />
                            <span class="time-label">模型用户：</span>
                            <span class="time-value">{{ item.userName }}</span>
                          </div>
                          <div class="time-item">
                            <n-icon :component="CalculatorOutline" class="time-icon" />
                            <span class="time-label">最近计算：</span>
                            <span class="time-value">{{ item.calculateTime || '未计算' }}</span>
                          </div>
                          <div class="time-item">
                            <n-icon :component="TimeOutline" class="time-icon" />
                            <span class="time-label">更新时间：</span>
                            <span class="time-value">{{ item.updateTime }}</span>
                          </div>
                          <div class="time-item">
                            <n-icon :component="TimeOutline" class="time-icon" />
                            <span class="time-label">创建时间：</span>
                            <span class="time-value">{{ item.createTime }}</span>
                          </div>
                        </div>
                      </div>
                    </template>
                  </n-card>
                </n-grid-item>
              </n-grid>
            </n-spin>
            <br />

            <!--          <n-space justify="center">-->
            <!--            <n-button @click="saveFinanceRules" type="success" class="mt-2">保存规则</n-button>-->
            <!--          </n-space>-->
          </n-card>
        </n-grid-item>

        <!-- 融资保证金比例模型 -->
        <n-grid-item span="4">
          <n-card>
            <template #header>
              <n-h1 class="mb-0" prefix="bar">
                {{ currentMarginRatioModel }}
              </n-h1>
            </template>
            <template #header-extra>
              <n-space>
                <n-button
                  :disabled="ifStockCalculating == 0"
                  :type="showModelResult ? 'info' : 'warning'"
                  @click="handleModelResult"
                >
                  <template #icon>
                    <n-icon :component="BarChartOutline" />
                  </template>
                  {{
                    ifStockCalculating == 0
                      ? '模型结果正在计算中'
                      : showModelResult
                      ? '查看配置信息'
                      : '查看模型结果'
                  }}
                </n-button>
                <n-button
                  :type="showHistoryCompare ? 'info' : 'warning'"
                  @click="handleHistoryCompare"
                >
                  <template #icon>
                    <n-icon :component="GitCompareOutline" />
                  </template>
                  {{ showHistoryCompare ? '查看配置信息' : '模型历史比较结果' }}
                </n-button>
                <n-button
                  v-if="ifStockCalculating != CommonStatus.ENABLE"
                  :loading="calculateStockLoading"
                  secondary
                  strong
                  type="success"
                  @click="handleStockCalculateModel"
                >
                  <template #icon>
                    <n-icon :component="PlayOutline" />
                  </template>
                  {{ calculateStockLoading ? '计算中' : '点击计算' }}
                </n-button>
              </n-space>
            </template>

            <n-tabs v-show="!showModelResult && !showHistoryCompare" type="card">
              <template #suffix>
                <n-button
                  :loading="calculateModelNonScoreModelLoading"
                  secondary
                  strong
                  type="success"
                  @click="calculateModelNonScoreModelBtn"
                >
                  <template #icon>
                    <n-icon :component="FlashOutline" />
                  </template>
                  点击计算(不计算得分模型)
                </n-button>
              </template>
              <n-tab-pane name="股票" tab="股票">
                <n-card>
                  <template #header>
                    <n-space>
                      <n-button
                        :disabled="ifStockCalculating == 0"
                        :type="stockModelResult ? 'info' : 'warning'"
                        @click="handleStockModelResult"
                      >
                        <template #icon>
                          <n-icon :component="AnalyticsOutline" />
                        </template>
                        {{
                          ifStockCalculating == 0
                            ? '模型结果正在计算中'
                            : stockModelResult
                            ? '查看配置信息'
                            : '查看模型结果'
                        }}
                      </n-button>
                    </n-space>
                  </template>
                  <div v-if="!stockModelResult">
                    <n-tabs type="line">
                      <template #suffix>
                        当前引用的折算率模型： {{ currentMarginRatioModel }}
                      </template>
                      <n-tab-pane name="综合得分配置" tab="综合得分配置">
                        <ScoreGroupConfig ref="groupConfigRef" :model-id="currentModelId" />
                      </n-tab-pane>
                      <n-tab-pane name="评级策略" tab="评级策略">
                        <RatingStrategyConfig
                          ref="ratingStrategyConfigRef"
                          :model-id="currentModelId"
                        />
                      </n-tab-pane>
                      <n-tab-pane name="保证金比例配置" tab="保证金比例配置">
                        <n-grid :cols="4" x-gap="12">
                          <n-gi :span="2">
                            <MarginRatioConfig
                              ref="marginRatioConfigRef"
                              :marginRatioType="MarginRatioTypeEnum.FINANCING"
                              :model-id="currentModelId"
                            />
                          </n-gi>
                          <n-gi :span="2">
                            <MarginRatioConfig
                              ref="marginRatioConfigRef"
                              :marginRatioType="MarginRatioTypeEnum.SHORT_SELL"
                              :model-id="currentModelId"
                            />
                          </n-gi>
                        </n-grid>
                      </n-tab-pane>
                    </n-tabs>
                  </div>

                  <!--    模型结果-->
                  <MarginRatioModelResult v-if="stockModelResult" :model-id="currentModelId" />
                </n-card>
              </n-tab-pane>
              <n-tab-pane name="基金和债券" tab="基金和债券">
                <n-tabs animated type="segment">
                  <n-tab-pane name="融资保证金比例模型" tab="融资保证金比例模型">
                    <FinancingMarginRatioModel :modelId="currentModelId" />
                  </n-tab-pane>
                  <n-tab-pane name="融券保证金比例模型" tab="融券保证金比例模型">
                    <ShortSellingMarginRatioModel :modelId="currentModelId" />
                  </n-tab-pane>
                </n-tabs>
              </n-tab-pane>
            </n-tabs>

            <!--    模型结果-->
            <MarginRatioModelCalculateResult v-if="showModelResult" :model-id="currentModelId" />

            <!--    模型历史比较结果展示-->
            <div v-if="showHistoryCompare" class="model-history-compare-wrapper">
              <MarginRatioModelHistoryCompare
                :calculate-time="currentModel?.calculateTime"
                :model-id="currentModelId"
              />
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 新增规则弹窗 -->
    <n-modal
      v-model:show="showModal"
      class="custom-card"
      preset="card"
      size="huge"
      title="新增规则"
    >
      <n-form label-placement="left" label-width="120px">
        <n-form-item label="市值下限">
          <n-input-number v-model:value="newRule.minMarketCap" placeholder="请输入市值下限" />
        </n-form-item>
        <n-form-item label="市值上限">
          <n-input-number v-model:value="newRule.maxMarketCap" placeholder="请输入市值上限" />
        </n-form-item>
        <n-form-item label="融资保证金比例">
          <n-input-number v-model:value="newRule.financeMarginRatio" placeholder="请输入比例" />
        </n-form-item>
        <n-form-item label="评级范围">
          <n-input v-model:value="newRule.level" placeholder="请输入评级范围 (逗号分隔)" />
        </n-form-item>
      </n-form>

      <template #footer>
        <n-space justify="center">
          <n-button @click="showModal = false">
            <template #icon>
              <n-icon :component="CloseOutline" />
            </template>
            取消
          </n-button>
          <n-button :loading="loading" type="info" @click="saveNewRule">
            <template #icon>
              <n-icon :component="CheckmarkOutline" />
            </template>
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
    <n-modal
      v-model:show="addModalShow"
      :bordered="false"
      :title="addForm.modelId ? '编辑模型' : '添加模型'"
      class="custom-card w-[500px]"
      preset="card"
      size="huge"
    >
      <n-form ref="formRef" label-placement="left" label-width="90px">
        <n-form-item label="模型名称">
          <n-input v-model:value="addForm.modelName" placeholder="请输入模型名称" />
        </n-form-item>
        <n-form-item label="模型备注">
          <n-input v-model:value="addForm.remark" placeholder="请输入备注" type="textarea" />
        </n-form-item>
        <n-form-item label="关联折算率模型">
          <n-select
            v-model:value="addForm.relateHaircutModelId"
            :options="collateralHaircutModelsList"
            clearable
            label-field="modelName"
            value-field="modelId"
          />
        </n-form-item>
      </n-form>
      <br />
      <br />
      <n-space justify="center">
        <n-button @click="addModalShow = false">
          <template #icon>
            <n-icon :component="CloseOutline" />
          </template>
          取消
        </n-button>
        <n-button type="primary" @click="saveNewRatioModel">
          <template #icon>
            <n-icon :component="CheckmarkOutline" />
          </template>
          确定
        </n-button>
      </n-space>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { NButton, NInput, NSelect, useDialog, useMessage } from 'naive-ui';
  import {
    getMarginRatioModelConfigList,
    saveMarginRatioModelConfig,
  } from '@/api/marginTrading/underlying/underlyingModel';
  import { useUserStore } from '@/store/modules/user';
  import { LevelStrategyModelConfig } from '@/models/level/levelStrategyModels';
  import FinancingMarginRatioModel from '@/views/mrgTrdModelManage/marginRatioModel/FinancingMarginRatioModel.vue';
  import MarginRatioModelResult from '@/views/mrgTrdModelManage/marginRatioModel/MarginRatioModelResult.vue';
  import MarginRatioModelCalculateResult from '@/views/mrgTrdModelManage/marginRatioModel/MarginRatioModelCalculateResult.vue';
  import MarginRatioConfig from '@/views/mrgTrdModelManage/marginRatioModel/MarginRatioConfig.vue';
  import ShortSellingMarginRatioModel from '@/views/mrgTrdModelManage/marginRatioModel/ShortSellingMarginRatioModel.vue';
  import ScoreGroupConfig from '@/views/mrgTrdModelManage/marginRatioModel/ScoreGroupConfig.vue';
  import RatingStrategyConfig from '@/views/mrgTrdModelManage/marginRatioModel/RatingStrategyConfig.vue';
  import MarginRatioModelHistoryCompare from '@/views/mrgTrdBusinessModel/integratedMarginRatioModel/MarginRatioModelHistoryCompare.vue';
  import { MarginRatioModelConfigDO } from '@/models/marginTrading/underlying/underlyingModels';
  import { CommonStatus, MarginRatioTypeEnum } from '@/enums/baseEnum';
  import { PageRequest } from '@/models/common/baseRequest';
  import { collateralHaircutModels } from '@/api/marginTrading/collateral/collateralHaircutConfiguration';
  import {
    calculateModelNonScoreModel,
    calculateModelResult,
  } from '@/api/tailored/xingye/marginRatioModel/marginRatioModelResultApi';
  import {
    AddOutline,
    AnalyticsOutline,
    BarChartOutline,
    CalculatorOutline,
    CalendarOutline,
    CheckmarkOutline,
    CloseOutline,
    CreateOutline,
    FlashOutline,
    GitCompareOutline,
    PersonOutline,
    PlayOutline,
    StarOutline,
    TimeOutline,
    TrashOutline,
  } from '@vicons/ionicons5';

  defineOptions({ name: 'MarginRatioModel' });
  const userStore = useUserStore();
  const userLevel = userStore.getUserLevelCriteria || {};
  const message = useMessage();
  const stockModelResult = ref(false);
  const showHistoryCompare = ref(false);

  const props = defineProps({
    ifUser: { type: Boolean, default: false },
    buttonHiding: { type: Boolean, default: false },
  });

  //模型列表
  const modelsList = ref<MarginRatioModelConfigDO[]>([]);
  const modelName = ref<string | null>(null);
  const relateHaircutModelId = ref<number | null>(null);
  const ifStockCalculating = ref<CommonStatus>(null);
  const dialog = useDialog();

  // 当前模型ID
  const currentModelId = ref<number | null>(1);

  // 数据表格数据
  const financeRules = ref([]);

  const loading = ref(false);
  const showModelResult = ref(false);
  const modelsLoading = ref(false);
  const calculateModelNonScoreModelLoading = ref(false);

  const addModalShow = ref(false);
  const calculateStockLoading = ref(false);
  const calculateLoading = ref(false);
  const levelOptions = ref<any>([]);
  const collateralHaircutModelsList = ref<any>([]);
  //新增编辑模型对象
  const addForm = reactive({
    createTime: null,
    enableStatus: 0,
    modelId: null,
    modelName: null,
    remark: null,
    updateTime: null,
    relateHaircutModelId: null,
  });
  // 新增规则弹窗
  const showModal = ref(false);
  const newRule = reactive({
    minMarketCap: null,
    maxMarketCap: null,
    financeMarginRatio: null,
    level: '',
  });

  // 计算模型
  const handleCalculateModel = async () => {
    // calculateLoading.value = true;
    // const { code, msg, data } = await calculateConcentraGroupModel(currentModel.value.modelId);
    // calculateLoading.value = false;
    //
    // if (code === 200) {
    //   message.success(msg);
    //   location.reload();
    // } else {
    //   message.error(msg);
    // }
  };
  // 模型结果
  const handleModelResult = async () => {
    showModelResult.value = !showModelResult.value;
    // 如果显示模型结果，则隐藏历史比较
    if (showModelResult.value) {
      showHistoryCompare.value = false;
    }
  };

  // 模型历史比较结果
  const handleHistoryCompare = async () => {
    showHistoryCompare.value = !showHistoryCompare.value;
    // 如果显示历史比较，则隐藏模型结果
    if (showHistoryCompare.value) {
      showModelResult.value = false;
    }
  };

  // 模型结果
  const handleStockModelResult = async () => {
    stockModelResult.value = !stockModelResult.value;
  };
  //当前引用的折算率模型
  const currentMarginRatioModel = computed(() => {
    //获取collateralHaircutModelsList中对应modelId的modelName
    const modelName = collateralHaircutModelsList.value.find(
      (item: any) => item.modelId === relateHaircutModelId.value
    )?.modelName;
    return modelName || '';
  });

  // 当前选中的模型
  const currentModel = computed(() => {
    return modelsList.value.find((model) => model.modelId === currentModelId.value);
  });

  const get_MarginRatioModelConfigList = async () => {
    modelsLoading.value = true;
    const { code, msg, data } = await getMarginRatioModelConfigList(props.ifUser);
    modelsLoading.value = false;

    if (code === 200) {
      modelsList.value = data;
      if (data && data.length > 0) {
        relateHaircutModelId.value = data[0].relateHaircutModelId;
        ifStockCalculating.value = data[0].ifCalculating;

        currentModelId.value = data[0].modelId;
      }
    } else {
      message.error(msg);
    }
  };

  //查询查询用户担保品折算率模型
  const get_CollateralHaircutModels = async (pageRequest: PageRequest) => {
    let { code, data, msg } = await collateralHaircutModels(pageRequest, true);

    if (code === 200) {
      collateralHaircutModelsList.value = data.records;
    } else {
      message.error(msg);
    }
  };
  // 计算模型
  const handleStockCalculateModel = async () => {
    calculateStockLoading.value = true;
    const { code, msg, data } = await calculateModelResult(currentModelId.value);
    calculateStockLoading.value = false;

    if (code === 200) {
      message.success(msg);
      location.reload();
    } else {
      message.error(msg);
    }
  };
  // 计算模型
  const calculateModelNonScoreModelBtn = async () => {
    calculateModelNonScoreModelLoading.value = true;
    const { code, msg, data } = await calculateModelNonScoreModel(currentModelId.value);
    calculateModelNonScoreModelLoading.value = false;

    if (code === 200) {
      message.success(msg);
      setTimeout(() => {
        location.reload();
      }, 1000);
    } else {
      message.error(msg);
    }
  };
  // 保存新增规则
  const saveNewRule = async () => {
    if (!newRule.financeMarginRatio || (!newRule.minMarketCap && !newRule.maxMarketCap)) {
      message.error('请填写完整的规则信息！');
      return;
    }
    financeRules.value.push({ ...newRule, ruleId: Date.now() });
    showModal.value = false;
  };
  //保存模型
  const saveNewRatioModel = () => {
    if (!addForm.modelName) {
      message.error('请输入模型名称');
      return;
    }
    /*if (!addForm.remark) {
    message.error('请输入模型备注');
    return;
  }*/
    save_MarginRatioModelConfig(addForm);
  };
  //调用保存接口
  const save_MarginRatioModelConfig = async (params: any) => {
    const { code, msg, data } = await saveMarginRatioModelConfig(params['modelId'], params);

    if (code === 200) {
      message.success(msg);
      addModalShow.value = false;
      get_MarginRatioModelConfigList();
    } else {
      message.error(msg);
    }
  };

  /**
   * 切换模型状态
   */
  const updateEnableStatus = (status: boolean, row: any) => {
    save_MarginRatioModelConfig({
      ...row,
      enableStatus: status ? CommonStatus.ENABLE : CommonStatus.DISABLE,
    });
  };
  /**
   * 切换模型状态
   */
  const updateMainStatus = (status: boolean, row: any) => {
    save_MarginRatioModelConfig({
      ...row,
      mainStatus: status ? CommonStatus.ENABLE : CommonStatus.DISABLE,
    });
  };

  //点击用户模型
  const changeActiveModel = (row: LevelStrategyModelConfig) => {
    currentModelId.value = row.modelId;
    relateHaircutModelId.value = row.relateHaircutModelId;
    ifStockCalculating.value = row.ifCalculating;
    modelName.value = row.modelName;
    // 切换模型时隐藏结果和历史比较
    showModelResult.value = false;
    showHistoryCompare.value = false;
    stockModelResult.value = false;
  };
  //新增规则
  const onCreateRule = () => {
    addModalShow.value = true;
  };
  //修改模型
  const editModel = (row: any) => {
    addForm.modelName = row.modelName;
    addForm.modelId = row.modelId;
    addForm.remark = row.remark;
    addForm.enableStatus = row.enableStatus;
    addForm.relateHaircutModelId = row.relateHaircutModelId;
    addModalShow.value = true;
  };
  //删除模型
  const deleteModel = (row: any) => {
    dialog.info({
      title: '提示',
      content: '你确定要删除' + row.modelName + '吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        console.log(1);
        save_MarginRatioModelConfig({
          ...row,
          deleteStatus: 1,
        });
      },
      onNegativeClick: () => {},
    });
  };
  onMounted(() => {
    userLevel.forEach((item: any) => {
      levelOptions.value.push({ label: item.level, value: item.level });
    });
    get_MarginRatioModelConfigList();
    get_CollateralHaircutModels({ current: 1, size: 1000 } as PageRequest);
  });
</script>

<style scoped>
  /* ===== 模型卡片基础样式 ===== */
  .model-card {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    border-radius: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  .model-card:hover {
    border-color: rgba(24, 160, 251, 0.2);
  }

  .model-card--active {
    border-color: #18a0fb !important;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 4px 20px rgba(24, 160, 251, 0.15), 0 2px 8px rgba(24, 160, 251, 0.1);
  }

  .model-card--active:hover {
    border-color: #18a0fb;
  }

  .model-card--enabled {
    border-left: 4px solid #10b981;
  }

  .model-card--disabled {
    border-left: 4px solid #6b7280;
    opacity: 0.8;
  }

  /* ===== 模型头部样式 ===== */
  .model-header {
    padding: 0;
  }

  .model-title-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .model-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
  }

  .model-card--active .model-title {
    color: #1e40af;
  }

  .model-status-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .status-tag {
    font-size: 11px;
    font-weight: 500;
    border-radius: 6px;
    padding: 2px 6px;
  }

  .status-tag--enabled {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
  }

  .status-tag--disabled {
    background: #f3f4f6;
    color: #6b7280;
  }

  .status-tag--main {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
  }

  /* ===== 操作区域样式 ===== */
  .model-actions {
    margin-top: 12px;
    padding: 12px;
    background: rgba(248, 250, 252, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .model-card--active .model-actions {
    background: rgba(239, 246, 255, 0.8);
    border-color: rgba(147, 197, 253, 0.3);
  }

  .model-switches {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
  }

  .switch-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
  }

  .switch-item:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .switch-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  .custom-switch {
    flex-shrink: 0;
  }

  .model-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .action-button {
    flex: 1;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .action-button--edit:hover {
    opacity: 0.8;
  }

  .action-button--delete:hover {
    opacity: 0.8;
  }

  /* ===== 描述信息样式 ===== */
  .model-description {
    margin: 12px 0;
    padding: 8px 10px;
    background: rgba(249, 250, 251, 0.8);
    border-radius: 6px;
    border-left: 3px solid #e5e7eb;
  }

  .description-text {
    font-size: 14px;
    line-height: 1.5;
    color: #6b7280;
  }

  .no-description {
    font-style: italic;
    color: #9ca3af;
  }

  /* ===== 底部信息样式 ===== */
  .model-footer {
    padding: 0;
  }

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .time-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 3px 0;
    font-size: 12px;
  }

  .time-icon {
    color: #6b7280;
    font-size: 14px;
    flex-shrink: 0;
  }

  .time-label {
    color: #6b7280;
    font-weight: 500;
    min-width: 80px;
  }

  .time-value {
    color: #374151;
    font-weight: 400;
  }

  /* ===== 响应式设计 ===== */
  @media (max-width: 1200px) {
    .model-switches {
      gap: 8px;
    }

    .switch-item {
      padding: 6px 10px;
    }

    .model-buttons {
      flex-direction: column;
      gap: 6px;
    }
  }

  @media (max-width: 768px) {
    .model-card {
      border-radius: 12px;
    }

    .model-title {
      font-size: 16px;
    }

    .model-actions {
      padding: 12px;
    }

    .status-tag {
      font-size: 11px;
      padding: 3px 6px;
    }

    .time-item {
      font-size: 12px;
    }
  }

  /* ===== 模型历史比较展示样式 ===== */
  .model-history-compare-wrapper {
    margin-top: 20px;
    background: #ffffff;
    border-radius: 16px;
    padding: 24px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  /* ===== 动画效果 ===== */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .model-card {
    animation: fadeInUp 0.3s ease-out;
  }

  /* ===== 兼容旧样式 ===== */
  .activeStyle {
    border: 2px solid #18a0fb !important;
  }
</style>
