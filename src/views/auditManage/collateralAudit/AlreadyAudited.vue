<!--  审核管理/担保品审核/已审核-->
<template>
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="调整日期">
              <n-date-picker
                v-model:formatted-value="adjustDate"
                type="datetimerange"
                update-value-on-close
                value-format="yyyy-MM-dd HH:mm:ss"
                @update:formatted-value="handleAdjustDateChange"
              />
            </n-form-item>
            <n-form-item label="证券名称">
              <SecuritySearchFund ref="securitySearchRef" @get-value="getStockValue" />
            </n-form-item>

            <n-form-item label="调整类型">
              <n-select
                v-model:value="formInline.adjustTypes"
                :options="adjustTypesOptions"
                clearable
                filterable
                multiple
                placeholder="请选择"
                style="width: 220px"
              />
            </n-form-item>
            <n-form-item>
              <n-space>
                <n-button type="info" @click="onSubmit"> 查询</n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-card size="small">
          <template #header>
            <n-radio-group
              v-if="props.censorType === AdmissionCensorType.CENSOR_PASS"
              v-model:value="commitCounterStatus"
              name="radiogroup"
              @change="handleCensorStatusChange"
            >
              <n-space>
                <n-radio :value="CommitCounterStatus.NOT_SUBMITTED"> 待提交柜台 </n-radio>
                <n-radio :value="CommitCounterStatus.SUBMITTED">
                  已提交柜台<span class="opacity-60">{{ commitCounterTime }}</span>
                </n-radio>
              </n-space>
            </n-radio-group>
          </template>
          <template #header-extra>
            <n-space v-if="!props.hideButton">
              <n-button
                v-if="commitCounterStatus === CommitCounterStatus.SUBMITTED"
                secondary
                size="small"
                strong
                type="warning"
                @click="unsubmitToCounterBtn"
              >
                重新添加到待提交
              </n-button>

              <n-checkbox
                v-if="
                  commitCounterStatus === CommitCounterStatus.NOT_SUBMITTED && !compareCounterModal
                "
                v-model:checked="filterDuplicateRecord"
                class="mt-[3px]"
                @update:checked="handleUpdateChecked"
              >
                只看重复记录
              </n-checkbox>
              <n-button
                v-if="commitCounterStatus === CommitCounterStatus.NOT_SUBMITTED"
                size="small"
                type="success"
                @click="compareCounterDataBtn"
              >
                {{ compareCounterModal ? '关闭' : '比较柜台数据' }}
              </n-button>
              <n-button
                v-if="commitCounterStatus === CommitCounterStatus.NOT_SUBMITTED"
                size="small"
                type="info"
                @click="subToCounterBtn(null)"
                >提交至柜台</n-button
              >
              <n-button
                v-if="commitCounterStatus === CommitCounterStatus.NOT_SUBMITTED"
                secondary
                size="small"
                strong
                type="info"
                @click="submitToCounterModal = true"
                >全部提交至柜台</n-button
              >
            </n-space>
          </template>
          <CompareCounterDataTable
            v-if="compareCounterModal && commitCounterStatus === CommitCounterStatus.NOT_SUBMITTED"
            :params="{ startTime: adjustDate[0], endTime: adjustDate[1] }"
          />
          <div
            v-show="
              !compareCounterModal || commitCounterStatus !== CommitCounterStatus.NOT_SUBMITTED
            "
          >
            <n-data-table
              :key="tableKey"
              :columns="columns"
              :data="dataTableList"
              :loading="loading"
              :max-height="550"
              :min-height="550"
              :pagination="false"
              :row-key="(item) => item.adjustId"
              :scroll-x="1800"
              :single-line="false"
              bordered
              @update:checked-row-keys="handleCheck"
              @update:sorter="handleSorterChangeWrapper"
            />

            <n-space justify="center">
              <n-space justify="center">
                <n-pagination
                  v-model:page="pageRequest.current"
                  v-model:page-size="pageRequest.size"
                  :item-count="total"
                  :page-sizes="[10, 20, 50, 100, 300]"
                  class="mt-5"
                  show-size-picker
                  @update:page="updatePage"
                  @update:page-size="updatePageSize"
                >
                  <template #suffix> 共 {{ total }} 条</template>
                </n-pagination>
              </n-space>
            </n-space>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-modal
      v-model:show="submitToCounterModal"
      :bordered="false"
      class="custom-card w-[450px]"
      preset="card"
      size="huge"
      title="全部提交至柜台"
    >
      <n-p class="opacity-60">当前时间：{{ adjustDate[0] }} 至 {{ adjustDate[1] }}</n-p>
      <template #footer>
        <n-space justify="center">
          <n-button @click="submitToCounterModal = false"> 取消</n-button>
          <n-button :loading="censorLoading" type="info" @click="submitToCounter(null)">
            确定</n-button
          >
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script lang="ts" setup>
  import { nextTick, h, onMounted, reactive, ref, computed, toRefs, inject } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns, useDialog } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import { openStockArchives } from '@/utils/goToArchives';
  import { addDaysToDate, adjustDatesTo15, isDateAfterCurrent } from '@/utils/common/date/dateUtil';
  import {
    getCollateralCancelRecord,
    getCollateralCommitCounter,
    getCollateralList,
    resetCollateralResetCommitStatus,
  } from '@/api/audit/collateralAudit';
  import SecuritySearchFund from '@/components/SecuritySearch/SecuritySearchFund.vue';
  import CompareCounterDataTable from '@/views/auditManage/collateralAudit/CompareCounterDataTable.vue';
  import {
    AdjustTypeInfo,
    AdmissionCensorType,
    CommitCounterStatus,
    AdmissionCensorTypeInfo,
  } from '@/enums/marginTrading/collateralEnum';
  import { CommonStatus, getEnumOptions, getEnumProperties } from '@/enums/baseEnum';
  import { exportExcel } from '@/api/system/https';
  import { getDefaultTimeRange } from '@/api/tailored/xingye/adjustCompare/collateralApi';
  import { commitCounterStaticPe } from '@/api/audit/staticPeReviewApi';
  defineOptions({
    name: 'CollateralAlreadyAudited',
  });
  const props = defineProps<{
    censorType: { type: number; default: 0 };
    hideButton: { type: boolean; default: false };
    showBackoutButton: { type: boolean; default: false };
  }>();

  const emit = defineEmits(['adjustDateDefaul']);

  const { hideButton, showBackoutButton } = toRefs(props);
  const message = useMessage();
  const userStore = useUserStore();

  // 注入父组件提供的刷新方法
  const refreshCommitCounterCount = inject('refreshCommitCounterCount') as Function;
  const typeOptions = ref([]);
  const adjustTypesOptions = ref(getEnumOptions(AdjustTypeInfo));
  const adjustDate = ref<any>(null);
  const tableKey = ref<any>(0);
  const commitCounterTime = ref<any>('');
  const adjustDateDefaul = ref<any>(null);
  const compareCounterModal = ref<any>(false);
  const filterDuplicateRecord = ref<any>(false);
  const submitToCounterModal = ref<any>(false);
  const censorLoading = ref<any>(false);
  const dialog = useDialog();
  const dataTableList = ref<any>([]);
  const stockOptions = ref<any>([]);
  const handleAdjustDateChange = (value) => {
    adjustDate.value = adjustDatesTo15(
      value,
      adjustDateDefaul.value ? adjustDateDefaul.value[0].split(' ')[1] : null
    );
  };
  const selectData = ref([]);
  const commitCounterStatus = ref(CommitCounterStatus.NOT_SUBMITTED);

  const columns = computed(() => {
    const cols = [
      {
        type: 'selection',
      },
      {
        title: '序号',
        align: 'center',
        key: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      { align: 'center', width: '110px', title: '调整日期', sorter: true, key: 'adjustDate' },

      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '120px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      {
        align: 'center',
        title: '担保品调整信息',
        key: 'attrs',
        children: [
          {
            align: 'center',
            width: '110px',
            title: '调整方式',
            key: 'adjustType',
            render(row) {
              const { label } = getEnumProperties(
                AdjustTypeInfo,
                row.collateralAdjustInfo?.adjustType || ''
              );

              return label;
            },
          },
          {
            align: 'center',
            width: '110px',
            title: '调整前折算率',
            key: 'beforeHaircut',
            render(row) {
              return row.collateralAdjustInfo?.beforeHaircut;
            },
          },
          {
            align: 'center',
            width: '110px',
            title: '调整后折算率',
            key: 'afterHaircut',
            render(row) {
              return row.collateralAdjustInfo?.afterHaircut;
            },
          },
        ],
      },
      {
        align: 'center',
        width:
          commitCounterStatus.value == CommitCounterStatus.NOT_SUBMITTED &&
          props.censorType !== AdmissionCensorType.CENSOR_FAIL
            ? '0'
            : '130px',
        title: '提交柜台时间',
        key: 'commitCounterTime',
      },
      { align: 'center', width: '110px', title: '申请用户', key: 'commitPerson' },
      { align: 'center', width: '110px', title: '审核用户', key: 'censorPerson' },

      {
        align: 'center',
        width: '140px',
        title: '审核状态',
        key: 'censorStatus',
        render(row) {
          const { label } = getEnumProperties(AdmissionCensorTypeInfo, row.censorStatus || '');
          return h(
            NTag,
            {
              type: row.censorStatus === AdmissionCensorType.CENSOR_PASS ? 'success' : 'error',
            },
            {
              default: () => label,
            }
          );
        },
      },

      { align: 'center', width: '110px', title: '申请理由', key: 'commitReason' },
      { align: 'center', width: '110px', title: '审核理由', key: 'censorReason' },
      { align: 'center', width: '130px', title: '申请时间', key: 'commitTime', sorter: true },
      { align: 'center', width: '140px', sorter: true, title: '审核时间', key: 'censorTime' },
      {
        title: '操作',
        key: 'actions',
        align: 'center',
        width:
          commitCounterStatus.value == CommitCounterStatus.NOT_SUBMITTED &&
          props.censorType !== AdmissionCensorType.CENSOR_FAIL &&
          !props.hideButton &&
          !props.showBackoutButton
            ? '200px'
            : '0',
        fixed: 'right',
        render(row) {
          return [
            h(
              NButton,
              {
                type: 'warning',
                strong: true,
                tertiary: true,
                size: 'small',
                onClick: () => backoutBtn(row),
              },
              { default: () => '撤销' }
            ),
            ' ',
            h(
              NButton,
              {
                type: 'info',
                strong: true,
                tertiary: true,
                size: 'small',
                onClick: () => subToCounterBtn(row),
              },
              { default: () => '提交柜台' }
            ),
          ];
        },
      },
      {
        title: '操作',
        key: 'actions',
        align: 'center',
        width: props.hideButton && props.showBackoutButton ? '120px' : '0',
        fixed: 'right',
        render(row) {
          return [
            h(
              NButton,
              {
                type: 'warning',
                strong: true,
                tertiary: true,
                size: 'small',
                onClick: () => backoutBtn(row),
              },
              { default: () => '撤销' }
            ),
          ];
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const loading = ref(true);

  const formInline = reactive({
    secCode: null,
    startTime: null,
    endTime: null,
    censorType: props.censorType,
    adjustTypes: null,
  });

  //重新添加到待提交
  const unsubmitToCounterBtn = () => {
    if (selectData.value.length == 0) {
      message.error('请选择一条数据');
      return;
    }
    const d = dialog.warning({
      title: '提示',
      content: `已选中${selectData.value.length}条数据，是否撤销至未提交柜台?`,
      positiveText: '确定',
      negativeText: '不确定',
      onPositiveClick: async () => {
        d.loading = true;

        // 🎯 显示处理提示信息
        const loadingMessage = message.loading('正在撤销至未提交柜台...', {
          duration: 0,
        });

        try {
          let { code, msg, data } = await resetCollateralResetCommitStatus(selectData.value);

          // 🎯 关闭加载提示
          loadingMessage.destroy();

          if (code == 200) {
            message.success(msg || '撤销成功');
            get_CollateralList(pageRequest);

            // 🎯 刷新待提交柜台数量
            if (refreshCommitCounterCount) {
              await refreshCommitCounterCount();
            }
          } else {
            message.error(msg || '撤销失败，请重试');
          }
        } catch (error) {
          // 🎯 关闭加载提示
          loadingMessage.destroy();

          console.error('撤销操作失败:', error);
          message.error('网络异常，请检查网络连接后重试');
        } finally {
          d.loading = false;
        }
      },
    });
  };

  const handleUpdateChecked = () => {
    nextTick(() => {
      pageRequest.current = 1;
      get_CollateralList(pageRequest);
    });
  };
  //比较柜台数据
  const compareCounterDataBtn = () => {
    compareCounterModal.value = !compareCounterModal.value;
  };

  //查询
  const get_CollateralList = async (pageRequest: PageRequest) => {
    loading.value = true;
    if (adjustDate.value && adjustDate.value?.length > 0) {
      formInline.startTime = adjustDate.value[0];
      formInline.endTime = adjustDate.value[1];
    } else {
      formInline.startTime = null;
      formInline.endTime = null;
    }
    let { code, data, msg } = await getCollateralList({
      ...pageRequest,
      ...formInline,
      filterDuplicateRecord:
        commitCounterStatus.value === CommitCounterStatus.NOT_SUBMITTED
          ? filterDuplicateRecord.value
          : false,
      commitCounterStatus:
        props.censorType === AdmissionCensorType.CENSOR_PASS ? commitCounterStatus.value : null,
    });
    loading.value = false;
    if (code === 200) {
      if (data.records && data.records.length > 0 && data.records[0].commitCounterTime) {
        commitCounterTime.value = `(${data.records[0].commitCounterTime})`;
      }
      //console.log(data.records);
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_CollateralList);
  //获取时间范围
  const get_DefaultTimeRange = async () => {
    const { code, msg, data } = await getDefaultTimeRange();
    if (code === 200) {
      if (data && data.length > 0) {
        adjustDate.value = [data[0], data[1]];
        adjustDateDefaul.value = [data[0], data[1]];
        emit('adjustDateDefaul', adjustDateDefaul.value);
        get_CollateralList(pageRequest);
      }
    }
  };
  onMounted(() => {
    get_DefaultTimeRange();
  });
  const handleCensorStatusChange = () => {
    nextTick(() => {
      pageRequest.current = 1;
      selectData.value = [];
      tableKey.value++;

      get_CollateralList(pageRequest);
    });
  };
  //撤销
  const backoutBtn = (row) => {
    const d = dialog.warning({
      title: '提示',
      content: '是否撤销当前担保品调整申请记录?',
      positiveText: '确定',
      negativeText: '不确定',
      onPositiveClick: async () => {
        d.loading = true;

        // 🎯 显示撤销处理提示信息
        const loadingMessage = message.loading('正在撤销担保品调整申请...', {
          duration: 0,
        });

        try {
          let { code, msg, data } = await getCollateralCancelRecord(row.adjustId);

          // 🎯 关闭加载提示
          loadingMessage.destroy();

          selectData.value = [];
          tableKey.value++;

          if (code == 200) {
            message.success(msg || '撤销成功');
            get_CollateralList(pageRequest);

            // 🎯 刷新待提交柜台数量
            if (refreshCommitCounterCount) {
              await refreshCommitCounterCount();
            }
          } else {
            message.error(msg || '撤销失败，请重试');
          }
        } catch (error) {
          // 🎯 关闭加载提示
          loadingMessage.destroy();

          console.error('撤销申请失败:', error);
          message.error('网络异常，请检查网络连接后重试');
        } finally {
          d.loading = false;
        }
      },
    });
  };
  //选中行
  const handleCheck = (rowKeys) => {
    selectData.value = rowKeys;
  };
  //单选提交至柜台 row null:多选  row:当前行
  const subToCounterBtn = (row) => {
    if (row == null) {
      if (selectData.value.length == 0) {
        message.error('请选择一条数据');
        return;
      }
      dialog.info({
        title: '提交',
        content: `已选中${selectData.value.length}条数据，是否提交至柜台?`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          submitToCounter(selectData.value);
        },
        onNegativeClick: () => {},
      });
    } else {
      dialog.info({
        title: '提交',
        content: `是否将${row.secName}提交至柜台?`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          submitToCounter([row.adjustId]);
        },
        onNegativeClick: () => {},
      });
    }
  };

  //批量提交至柜台
  const submitToCounter = async (adjustIds) => {
    if (!adjustDate.value || adjustDate.value.length === 0) {
      message.error('日期不能为空');
      return;
    }

    // 🎯 显示加载提示信息
    const loadingMessage = message.loading('正在调用柜台中...', {
      duration: 0, // 持续显示直到手动关闭
    });

    censorLoading.value = true;

    try {
      const { code, data, msg } = await getCollateralCommitCounter(
        adjustDate.value[0],
        adjustDate.value[1],
        null,
        adjustIds
      );

      // 🎯 关闭加载提示
      loadingMessage.destroy();

      if (code === 200) {
        selectData.value = [];
        tableKey.value++;

        // 🎯 显示成功提示
        message.success(msg || '提交柜台成功');
        await get_CollateralList(pageRequest);
        submitToCounterModal.value = false;

        // 🎯 刷新待提交柜台数量
        if (refreshCommitCounterCount) {
          await refreshCommitCounterCount();
        }
      } else {
        // 🎯 显示错误提示
        message.error(msg || '提交柜台失败，请重试');
      }
    } catch (error) {
      // 🎯 关闭加载提示
      loadingMessage.destroy();

      // 🎯 处理网络错误或其他异常
      console.error('提交柜台接口调用失败:', error);
      message.error('网络异常，请检查网络连接后重试');
    } finally {
      // 🎯 确保 loading 状态被重置
      censorLoading.value = false;
    }
  };
  //证券
  const getStockValue = (val) => {
    formInline.secCode = val;
  };
  const exportBtn = () => {
    let { token } = userStore.getUserInfo || {};
    exportExcel({}, '/margin-trading/exportMarginTradingExportStock', token);
  };
  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
