<!--日志管理/同步数据库历史日志/同步数据库任务-->
<template>
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="同步任务日期">
              <n-date-picker
                v-model:formatted-value="formInline.taskDate"
                class="w-[200px] mr-4"
                clearable
                placeholder="请选择同步任务日期"
                type="date"
                update-value-on-close
                value-format="yyyy-MM-dd"
              />
            </n-form-item>
            <n-form-item label="任务状态">
              <n-select
                v-model:value="formInline.taskStatus"
                :options="taskStatusEnumOptions"
                clearable
                placeholder="请选择执行状态"
                style="width: 240px"
              />
            </n-form-item>
            <n-form-item label="同步库类型">
              <n-select
                v-model:value="formInline.dbType"
                :options="dbTypeOptionsOptions"
                clearable
                placeholder="请选择同步数据库类型"
                style="width: 240px"
              />
            </n-form-item>
            <n-form-item label="环境配置">
              <n-select
                v-model:value="formInline.profileType"
                :options="profileTypeOptions"
                clearable
                placeholder="请选择环境配置"
                style="width: 240px"
              />
            </n-form-item>
            <!--            <n-form-item>-->
            <!--              <n-space>-->
            <!--                <n-button attr-type="button" type="info" @click="onSubmit"> 查询</n-button>-->
            <!--              </n-space>-->
            <!--            </n-form-item>-->
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :columns="columns"
          :data="dataTableList"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1100"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChangeWrapper"
        />

        <n-space justify="center">
          <n-space justify="center">
            <n-pagination
              v-model:page="pageRequest.current"
              v-model:page-size="pageRequest.size"
              :item-count="total"
              :page-sizes="[10, 20, 50, 100, 300]"
              class="mt-5"
              show-size-picker
              @update:page="updatePage"
              @update:page-size="updatePageSize"
            >
              <template #suffix> 共 {{ total }} 条</template>
            </n-pagination>
          </n-space>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref, watch } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns, useDialog } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import {
    deleteHaircutManualAdjust,
    listExportSecCollateralInfos,
  } from '@/api/marginTrading/collateral/collateralBusiness';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import { deleteSyncDataTask, getSyncDataTaskList } from '@/api/system/log';
  import { getEnumOptions, getEnumProperties, ProfileTypeInfo } from '@/enums/baseEnum';
  import { DbTypeInfo, SysManageTaskStatusInfo, TaskStatusInfo } from '@/enums/sysManageEnum';
  defineOptions({
    name: 'SyncDataTask',
  });
  const message = useMessage();
  const userStore = useUserStore();
  const typeOptions = ref([]);
  const dataTableList = ref<any>([]);
  const taskStatusEnumOptions = getEnumOptions(TaskStatusInfo);
  const dbTypeOptionsOptions = getEnumOptions(DbTypeInfo);
  const profileTypeOptions = getEnumOptions(ProfileTypeInfo);
  const dialog = useDialog();

  const columns: DataTableColumns = [
    // {
    //   title: '序号',
    //   align: 'center',
    //   key: 'index',
    //   width: '50px',
    //   render(row, index) {
    //     return index + 1;
    //   },
    // },
    {
      title: '任务id',
      align: 'center',
      key: 'taskId',
      width: '60px',
    },
    {
      title: '项目id',
      align: 'center',
      key: 'projectId',
      width: '80px',
    },
    {
      align: 'center',
      width: '110px',
      sorter: true,
      title: '同步库类型',
      key: 'dbType',
      render(row) {
        const { label } = getEnumProperties(DbTypeInfo, row.dbType || '');
        return label;
      },
    },
    {
      align: 'center',
      sorter: true,
      width: '80px',
      title: '环境配置',
      key: 'profileType',
      render(row) {
        const { label } = getEnumProperties(ProfileTypeInfo, row.profileType || '');
        return label;
      },
    },
    { align: 'center', sorter: true, width: '110px', title: '同步任务日期', key: 'taskDate' },
    { align: 'center', sorter: true, width: '110px', title: '同步表数量', key: 'syncTableCount' },
    {
      title: '执行状态',
      align: 'center',
      sorter: true,
      key: 'taskStatus',
      width: '100px',
      render(row) {
        const { label, color } = getEnumProperties(SysManageTaskStatusInfo, row.taskStatus || '');
        return h(
          'span',
          {
            style: { color },
          },
          label
        );
      },
    },
    { align: 'center', width: '110px', sorter: true, title: '开始时间', key: 'taskStartTime' },
    { align: 'center', width: '110px', sorter: true, title: '结束时间', key: 'taskEndTime' },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      align: 'center',
      width: 80,
      render(row) {
        return h(
          NButton,
          {
            type: 'error',
            size: 'medium',
            onClick: () => {
              deleteBtn(row);
            },
          },
          '删除'
        );
      },
    },
  ];

  const loading = ref(true);

  const formInline = reactive({
    taskDate: null,
    dbType: null,
    taskStatus: null,
    profileType: null,
    queryDate: null,
  });
  watch(
    () => formInline,
    () => {
      onSubmit();
    },
    { deep: true }
  );
  const deleteBtn = async (row) => {
    const d = dialog.info({
      title: '提示',
      content: '你确定要删除当前数据吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        d.loading = true;
        const { code, data, msg } = await deleteSyncDataTask(row.taskId);
        d.loading = false;
        if (code === 200) {
          get_SyncDataTaskList(pageRequest);
          message.success(msg);
        } else {
          message.error(msg);
        }
      },
    });
  };
  //查询
  const get_SyncDataTaskList = async (pageRequest: PageRequest) => {
    loading.value = true;
    let { code, data, msg } = await getSyncDataTaskList({
      ...pageRequest,
      ...formInline,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_SyncDataTaskList);

  onMounted(() => {
    get_SyncDataTaskList(pageRequest);
  });

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
