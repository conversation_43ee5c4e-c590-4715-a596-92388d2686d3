<!--  官网与柜台业务参数差异-->
<template>
  <n-card bordered>
    <n-grid cols="4" item-responsive :x-gap="12" :y-gap="10">
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="证券名称">
              <SecuritySearchFund @get-value="getStockId" width="100%" />
            </n-form-item>
            <n-form-item label="业务类型">
              <n-select
                style="width: 234px"
                v-model:value="formInline.businessParamType"
                :options="[
                  { label: businessParamType.rzrq, value: businessParamType.rzrq },
                  { label: businessParamType.kcd, value: businessParamType.kcd },
                ]"
              />
            </n-form-item>

            <n-form-item>
              <n-space>
                <n-button attr-type="button" type="info" @click="search"> 查询</n-button>
                <n-p class="mt-[6px] opacity-80"> 最新采集日期：{{ latestDataTime || '-' }} </n-p>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :row-key="(item) => item.id"
          :max-height="550"
          :min-height="550"
          :loading="loading"
          :scroll-x="1100"
          :columns="columns"
          :data="dataTableList"
          :pagination="false"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChangeWrapper"
        />

        <n-space justify="center">
          <n-space justify="center">
            <n-pagination
              class="mt-5"
              v-model:page="pageRequest.current"
              v-model:page-size="pageRequest.size"
              @update:page="updatePage"
              @update:page-size="updatePageSize"
              :item-count="total"
              :page-sizes="[10, 20, 50, 100, 300]"
              show-size-picker
            >
              <template #suffix> 共 {{ total }} 条</template>
            </n-pagination>
          </n-space>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script setup lang="ts">
  import { computed, h, onMounted, reactive, ref } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import {
    getCrawlerLatestDataTime,
    getWebsiteCounterBusinessParameterDifference,
  } from '@/api/tailored/xingye/WebsiteCounterBusinessParameterDifferenceApi';
  import {
    businessParamType,
    XyzqWebsiteCounterDifferVO,
  } from '@/enums/xingye/WebsiteCounterBusinessParameterDifferenceEnum';
  import { openStockArchives } from '@/utils/goToArchives';
  import SecuritySearchFund from '../../../../components/SecuritySearch/SecuritySearchFund.vue';

  const message = useMessage();
  const userStore = useUserStore();
  const typeOptions = ref([]);
  const businessParamValue = ref<any>(null);
  const latestDataTime = ref(null);
  const dataTableList = ref<XyzqWebsiteCounterDifferVO[]>([]);

  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        key: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '120px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '120px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.rzrq ? '0' : '110px',
        title: '官网折算率值',
        key: 'websiteHaircut',
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.rzrq ? '0' : '110px',
        title: '柜台折算率值',
        key: 'counterHaircut',
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.kcd ? '0' : '110px',
        title: '官网融资保证金比例',
        key: 'websiteFinancingMarginRatio',
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.kcd ? '0' : '110px',

        title: '柜台融资保证金比例',
        key: 'counterFinancingMarginRatio',
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.kcd ? '0' : '110px',

        title: '官网融券保证金比例',
        key: 'websiteShortMarginRatio',
      },
      {
        align: 'center',
        width: businessParamValue.value === businessParamType.kcd ? '0' : '110px',

        title: '柜台融券保证金比例',
        key: 'counterShortMarginRatio',
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const loading = ref(true);

  const formInline = reactive({
    secCode: null,
    businessParamType: businessParamType.rzrq,
  });

  //查询
  const get_WebsiteCounterBusinessParameterDifference = async (pageRequest: PageRequest) => {
    businessParamValue.value = formInline.businessParamType;
    loading.value = true;
    let { code, data, msg } = await getWebsiteCounterBusinessParameterDifference({
      ...pageRequest,
      ...formInline,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };
  const getStockId = (id, name) => {
    formInline.secCode = id;
  };

  const get_CrawlerLatestDataTime = async (businessParamType) => {
    const { data, msg, code } = await getCrawlerLatestDataTime(businessParamType);
    latestDataTime.value = null;
    if (code === 200) {
      latestDataTime.value = data;
    } else {
      message.error(msg);
    }
  };

  const search = () => {
    get_CrawlerLatestDataTime(formInline.businessParamType);
    onSubmit();
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_WebsiteCounterBusinessParameterDifference);

  onMounted(() => {
    get_WebsiteCounterBusinessParameterDifference(pageRequest);
    get_CrawlerLatestDataTime(formInline.businessParamType);
  });

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
