<!--  发布官网公告/集中度分组-->
<template>
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="调整日期">
              <n-date-picker
                v-model:formatted-value="adjustDate"
                type="datetimerange"
                update-value-on-close
                value-format="yyyy-MM-dd HH:mm:ss"
                @update:formatted-value="handleAdjustDateChange"
              />
            </n-form-item>
            <n-form-item label="证券名称">
              <SecuritySearchFund ref="securitySearchRef" @get-value="getStockValue" />
            </n-form-item>
            <n-form-item label="是否过滤调整前与调整后集中度分组相同的记录" label-width="170px">
              <n-select
                v-model:value="formInline.ifFilterIdenticalConcentraGroupRecord"
                :options="[
                  { label: '过滤', value: true },
                  { label: '不过滤', value: false },
                ]"
                clearable
                filterable
                placeholder="不过滤"
                style="width: 230px"
              />
            </n-form-item>
            <!--            <n-form-item label="调整类型">-->
            <!--              <n-select-->
            <!--                multiple-->
            <!--                filterable-->
            <!--                clearable-->
            <!--                style="width: 220px"-->
            <!--                v-model:value="formInline.adjustTypes"-->
            <!--                placeholder="请选择"-->
            <!--                :options="adjustTypesOptions"-->
            <!--              />-->
            <!--            </n-form-item>-->
            <n-form-item>
              <n-space>
                <n-button attr-type="button" type="info" @click="onSubmit"> 查询</n-button>
                <n-button
                  :loading="noticeLoading"
                  tertiary
                  type="info"
                  @click="generateAnnouncement"
                >
                  生成待发布公告
                </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-card size="small">
          <div>
            <n-data-table
              :columns="columns"
              :data="dataTableList"
              :default-checked-row-keys="selectData"
              :loading="loading"
              :max-height="550"
              :min-height="550"
              :pagination="false"
              :row-key="(item) => item.adjustId"
              :scroll-x="1200"
              :single-line="false"
              bordered
              @update:sorter="handleSorterChangeWrapper"
            />

            <n-space justify="center">
              <n-pagination
                v-model:page="pageRequest.current"
                v-model:page-size="pageRequest.size"
                :item-count="total"
                :page-sizes="[10, 20, 50, 100, 300]"
                class="mt-5"
                show-size-picker
                @update:page="updatePage"
                @update:page-size="updatePageSize"
              >
                <template #suffix> 共 {{ total }} 条</template>
              </n-pagination>
            </n-space>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-modal
      v-model:show="showModal"
      :bordered="false"
      class="custom-card mt-4 w-[50%]"
      preset="card"
      size="huge"
    >
      <template #header>
        <n-h2 class="mb-0" prefix="bar"> 待发布公告内容</n-h2>
      </template>
      <n-card>
        <template #header>
          <n-input v-model:value="noticeTitle" placeholder="请输入公告标题" type="text" />
        </template>
        <template #action>
          <n-space justify="end">
            <n-button quaternary size="small" type="info" @click="ifEditTemplate = !ifEditTemplate">
              {{ ifEditTemplate ? '关闭' : '编辑内容' }}
            </n-button>
          </n-space>
        </template>

        <n-scrollbar style="max-height: 60vh">
          <EmailTemplate
            v-if="ifEditTemplate"
            ref="emailTemplateRef"
            :defaultlValue="announcementContent"
            CLASS="w-full z-50"
            @update-value="updateTemplateValue"
          />
          <div v-else v-html="announcementContent"></div>
        </n-scrollbar>
      </n-card>

      <template #footer>
        <n-space justify="center">
          <n-button @click="showModal = false"> 关闭</n-button>
          <n-button type="primary" @click="onConfirmPublish"> 确定发布</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref } from 'vue';
  import { NButton, useDialog, useMessage } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import SecuritySearchFund from '../../../../../components/SecuritySearch/SecuritySearchFund.vue';
  import { openStockArchives } from '@/utils/goToArchives';
  import { adjustDatesTo15 } from '@/utils/common/date/dateUtil';
  import {
    AdjustTypeInfo,
    AdmissionCensorType,
    CommitCounterStatus,
  } from '@/enums/marginTrading/collateralEnum';
  import { getEnumOptions } from '@/enums/baseEnum';
  import { getCentralizationGroupList } from '@/api/audit/centralizationAudit';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { getDefaultTimeRange } from '@/api/tailored/xingye/adjustCompare/collateralApi';
  import {
    generateNotice,
    publishNotice,
  } from '@/api/tailored/xingye/marginTradingSecuritiesLending/publishOfficialWebsiteAnnouncementApi';
  import { ColumnTypeEnum } from '@/enums/xingye/publishOfficialWebsiteAnnouncementEnum';

  const props = defineProps({
    censorType: { type: Number, default: 0 },
    hideButton: { type: Boolean, default: false },
  });
  const message = useMessage();
  const userStore = useUserStore();
  const adjustTypesOptions = ref(getEnumOptions(AdjustTypeInfo));
  const dialog = useDialog();
  const commitCounterStatus = ref(CommitCounterStatus.SUBMITTED);
  const commitCounterTime = ref('');
  const showModal = ref<any>(false);
  const ifEditTemplate = ref<any>(false);
  //公告内容
  const announcementContent = ref<any>('');
  const censorLoading = ref(false);
  const filterDuplicateRecord = ref(false);
  const adjustDate = ref<any>(null);
  const adjustDateDefaul = ref<any>(null);
  const dataTableList = ref<any>([]);
  const stockOptions = ref<any>([]);

  const selectData = ref([]);

  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        key: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      { align: 'center', width: '120px', title: '调整日期', sorter: true, key: 'adjustDate' },

      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '120px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '120px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      {
        align: 'center',
        title: '集中度分组调整信息',
        key: 'attrs',
        children: [
          {
            align: 'center',
            title: '调整前集中度分组',
            key: 'beforeConcentrationGroup',
            render(row) {
              return h(
                'span',
                {
                  style: {
                    color: setLevelColor(
                      row.concentrationGroupAdjustInfo?.beforeConcentrationGroup
                    ),
                  },
                },
                { default: () => row.concentrationGroupAdjustInfo?.beforeConcentrationGroup }
              );
            },
          },
          {
            align: 'center',
            title: '调整后集中度分组',
            key: 'afterConcentrationGroup',
            render(row) {
              return h(
                'span',
                {
                  style: {
                    color: setLevelColor(row.concentrationGroupAdjustInfo?.afterConcentrationGroup),
                  },
                },
                { default: () => row.concentrationGroupAdjustInfo?.afterConcentrationGroup }
              );
            },
          },
        ],
      },
      {
        align: 'center',
        title: '提交柜台时间',
        key: 'commitCounterTime',
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const loading = ref(true);
  const noticeLoading = ref(false);
  const noticeTitle = ref<any>('');

  const formInline = reactive({
    secCode: null,
    startTime: null,
    endTime: null,
    censorType: props.censorType,
    adjustTypes: null,
  });
  //模板更新
  const updateTemplateValue = (value) => {
    announcementContent.value = value;
  };
  //查询
  const get_CentralizationGroupList = async (pageRequest: PageRequest) => {
    loading.value = true;
    if (adjustDate.value && adjustDate.value?.length > 0) {
      formInline.startTime = adjustDate.value[0];
      formInline.endTime = adjustDate.value[1];
    }
    let { code, data, msg } = await getCentralizationGroupList({
      ...pageRequest,
      ...formInline,
      ifNoticeCommitted: false,

      filterDuplicateRecord:
        commitCounterStatus.value === CommitCounterStatus.NOT_SUBMITTED
          ? filterDuplicateRecord.value
          : false,
      commitCounterStatus:
        props.censorType === AdmissionCensorType.CENSOR_PASS ? commitCounterStatus.value : null,
    });
    loading.value = false;
    if (code === 200) {
      if (data.records && data.records.length > 0 && data.records[0].commitCounterTime) {
        commitCounterTime.value = `(${data.records[0].commitCounterTime})`;
      }
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_CentralizationGroupList);
  const handleAdjustDateChange = (value) => {
    adjustDate.value = adjustDatesTo15(
      value,
      adjustDateDefaul.value ? adjustDateDefaul.value[0].split(' ')[1] : null
    );
  };
  //获取时间范围
  const get_DefaultTimeRange = async () => {
    const { code, msg, data } = await getDefaultTimeRange();
    if (code === 200) {
      if (data && data.length > 0) {
        adjustDate.value = [data[0], data[1]];
        adjustDateDefaul.value = [data[0], data[1]];
        get_CentralizationGroupList(pageRequest);
      }
    }
  };
  onMounted(() => {
    get_DefaultTimeRange();
  });

  //生成待发布公告
  const generateAnnouncement = async () => {
    noticeLoading.value = true;
    const { code, msg, data } = await generateNotice({
      startTime: formInline.startTime,
      endTime: formInline.endTime,
      columnType: ColumnTypeEnum.collateralMarginSecuritiesAdjustment,
      businessType: ColumnTypeEnum.concentrationGroupingAdjustment,
    });
    noticeLoading.value = false;

    if (code === 200) {
      showModal.value = true;
      announcementContent.value = data.noticeContent;
      noticeTitle.value = data.noticeTitle;
      message.success(msg);
    } else {
      message.error(msg);
    }
  };

  //发布
  const onConfirmPublish = async () => {
    if (!noticeTitle.value) {
      message.error('请输入公告标题');
      return;
    }

    const { code, msg, data } = await publishNotice({
      startTime: formInline.startTime,
      endTime: formInline.endTime,
      businessType: ColumnTypeEnum.concentrationGroupingAdjustment,
      columnType: ColumnTypeEnum.collateralMarginSecuritiesAdjustment,
      noticeContent: announcementContent.value,
      noticeTitle: noticeTitle.value,
    });

    if (code === 200) {
      message.success(msg);
      showModal.value = false;
      get_CentralizationGroupList(pageRequest);
    } else {
      message.error(msg);
    }
  };

  //证券
  const getStockValue = (val) => {
    formInline.secCode = val;
  };

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
