<template>
  <!--  可充抵保证金证券-->
  <n-card bordered>
    <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
      <n-grid-item span="4">
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="证券名称">
              <SecuritySearchFund width="100%" @get-value="getStockId" />
            </n-form-item>

            <n-form-item>
              <n-space>
                <n-button attr-type="button" type="info" @click="onSubmit"> 查询</n-button>
                <n-button :loading="exportLoading" type="warning" @click="exportBtn">
                  导出
                </n-button>
                <n-button secondary strong type="success" @click="openPublishBtn">
                  发布可充抵保证金证券查询
                </n-button>
              </n-space>
            </n-form-item>
          </n-space>
        </n-form>
      </n-grid-item>
      <n-grid-item span="4">
        <n-data-table
          :columns="columns"
          :data="dataTableList"
          :loading="loading"
          :max-height="550"
          :min-height="550"
          :pagination="false"
          :row-key="(item) => item.id"
          :scroll-x="1200"
          :single-line="false"
          bordered
          @update:sorter="handleSorterChangeWrapper"
        />

        <n-space justify="center">
          <n-space justify="center">
            <n-pagination
              v-model:page="pageRequest.current"
              v-model:page-size="pageRequest.size"
              :item-count="total"
              :page-sizes="[10, 20, 50, 100, 300]"
              class="mt-5"
              show-size-picker
              @update:page="updatePage"
              @update:page-size="updatePageSize"
            >
              <template #suffix> 共 {{ total }} 条</template>
            </n-pagination>
          </n-space>
        </n-space>
      </n-grid-item>
    </n-grid>
  </n-card>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, reactive, ref } from 'vue';
  import { NButton, NTag, useMessage, DataTableColumns, useDialog } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import usePageQuery from '@/hooks/usePageQuery';
  import { PageRequest } from '@/models/common/baseRequest';
  import {
    getCollateralQueryList,
    publishCollateralQueryList,
    publishUnderlyingQueryList,
  } from '@/api/tailored/xingye/marginTradingSecuritiesLending/businessParametersQueryApi';
  import {
    XyzqWebsiteCollateralQueryVO,
    XyzqWebsiteUnderlyingQueryVO,
  } from '@/models/tailored/xingye/marginTradingSecuritiesLending/businessParametersQueryModels';
  import { openStockArchives } from '@/utils/goToArchives';
  import SecuritySearchFund from '../../../../../components/SecuritySearch/SecuritySearchFund.vue';
  import { exportExcel } from '@/api/system/https';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { TABS_ROUTES } from '@/store/mutation-types';

  const dialog = useDialog();

  const message = useMessage();
  const userStore = useUserStore();
  const typeOptions = ref([]);
  const exportLoading = ref(false);
  const dataTableList = ref<XyzqWebsiteCollateralQueryVO[]>([]);
  defineOptions({
    name: 'CollateralMarginSecurities',
  });
  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        key: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '140px',
        sorter: true,
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '140px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      { align: 'center', title: '折算率', sorter: true, key: 'haircut' },
      {
        align: 'center',
        title: '分组',
        sorter: true,
        key: 'concentraGroup',
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.concentraGroup) },
            },
            { default: () => row.concentraGroup }
          );
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const loading = ref(true);

  const formInline = reactive({
    secCode: null,
  });

  //查询
  const get_CollateralQueryList = async (pageRequest: PageRequest) => {
    loading.value = true;
    let { code, data, msg } = await getCollateralQueryList({
      ...pageRequest,
      ...formInline,
    });
    loading.value = false;

    if (code === 200) {
      dataTableList.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };
  const getStockId = (id, name) => {
    formInline.secCode = id;
  };

  //发布查询
  const openPublishBtn = () => {
    const d = dialog.info({
      title: '提示',
      content: '确定发布可充抵保证金证券查询列表吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        d.loading = true;
        const { code, msg, data } = await publishCollateralQueryList();
        d.loading = false;
        if (code === 200) {
          get_CollateralQueryList(pageRequest);

          message.success(msg);
        } else {
          message.error(msg);
        }
      },
    });
  };

  const exportBtn = async () => {
    let { token } = userStore.getUserInfo as any;
    exportLoading.value = true;
    await exportExcel({ ...formInline }, '/xyzq/dataPush/exportCollateralQueryData', token);
    exportLoading.value = false;
  };

  const { pageRequest, total, onSubmit, updatePage, updatePageSize, handleSorterChangeWrapper } =
    usePageQuery(get_CollateralQueryList);

  onMounted(() => {
    get_CollateralQueryList(pageRequest);
  });

  handleSorterChangeWrapper(columns);
</script>

<style scoped></style>
