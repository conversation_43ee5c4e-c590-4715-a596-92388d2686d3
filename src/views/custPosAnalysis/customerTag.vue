<template>
  <!--  客户标签-->
  <div class="customer-tag-container">
    <TagSelector :tags="tagData" @update:selected="handleTagsChange" />
    <br />
    <!--    客户信息表格-->
    <n-card size="large">
      <template #header>
        <n-h2 prefix="bar" class="mb-0"> 客户详细信息 </n-h2>
      </template>
      <!-- 表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.customerCode"
        @update:page="handlePageChange"
        striped
        size="medium"
      />
      <br />
      <br />
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, h, reactive } from 'vue';
  import { NCard, NDataTable, NButton, NSpace, NTag, NEmpty } from 'naive-ui';
  import TagSelector from './CustomerTag/TagSelector.vue';
  import { openClientFile } from '@/utils/goToArchives';

  defineOptions({
    name: 'CustomerTag',
  });

  // 标签数据
  const tagData = ref([
    {
      label: '基本信息',
      id: 1,
      type: '基本标签',

      children: [
        { label: '年龄' },
        { label: '性别' },
        { label: '职业' },
        { label: '教育程度' },
        { label: '地域' },
        { label: '婚姻状况' },
        { label: '本月生日' },
      ],
    },
    {
      label: '账户信息',
      id: 2,
      type: '基本标签',

      children: [
        { label: '账户类型' },
        { label: '开户时间' },
        { label: '账户状态' },
        { label: '账户等级' },
      ],
    },
    {
      label: '资产信息',
      id: 3,
      type: '基本标签',

      children: [
        { label: '总资产' },
        { label: '持仓分布' },
        { label: '账户余额' },
        { label: '资产规模区间' },
      ],
    },
    {
      label: '交易行为',
      id: 4,
      type: '基本标签',

      children: [
        { label: '交易频率' },
        { label: '交易品种' },
        { label: '交易时间偏好' },
        { label: '交易金额' },
        { label: '交易方向' },
      ],
    },
    {
      label: '投资偏好',
      id: 5,
      type: '基本标签',

      children: [
        { label: '风险偏好' },
        { label: '投资期限' },
        { label: '投资目标' },
        { label: '行业偏好' },
        { label: '产品偏好' },
      ],
    },
    {
      label: '财务信息',
      id: 6,
      type: '基本标签',

      children: [
        { label: '收入水平' },
        { label: '负债情况' },
        { label: '现金流' },
        { label: '投资经验' },
      ],
    },
    {
      label: '行为特征',
      id: 7,
      type: '基本标签',

      children: [
        { label: '登录频率' },
        { label: '使用设备' },
        { label: 'APP使用时长' },
        { label: '资讯偏好' },
      ],
    },
    {
      label: '服务偏好',
      id: 8,
      type: '基本标签',

      children: [{ label: '服务渠道偏好' }, { label: '产品使用情况' }, { label: '活动参与度' }],
    },
    {
      label: '生命周期阶段',
      id: 9,
      type: '基本标签',

      children: [
        { label: '新客户' },
        { label: '活跃客户' },
        { label: '休眠客户' },
        { label: '流失客户' },
      ],
    },
    {
      label: '社交属性',
      type: '基本标签',

      id: 10,
      children: [{ label: '社交圈层' }, { label: '推荐关系' }, { label: '社群参与度' }],
    },
    {
      label: '信用风险',
      id: 11,
      type: '风险标签',
      children: [{ label: '融资融券违约' }],
    },
    {
      label: '财务风险',
      id: 12,
      type: '风险标签',

      children: [{ label: '资金不足' }, { label: '高负债率' }, { label: '现金流紧张' }],
    },
    {
      label: '交易行为风险',
      id: 13,
      type: '风险标签',

      children: [
        { label: '异常交易' },
        { label: '大额资金进出' },
        { label: '频繁撤单' },
        { label: '关联账户交易' },
      ],
    },
    {
      label: '投资风险',
      id: 14,
      type: '风险标签',

      children: [
        { label: '高风险偏好' },
        { label: '频繁交易' },
        { label: '追涨杀跌' },
        { label: '集中持仓' },
        { label: '历史亏损比例高' },
      ],
    },
  ]);

  // 选中的标签
  const selectedTags = ref([]);

  // 处理标签变更
  const handleTagsChange = (tags) => {
    selectedTags.value = tags;
    fetchTableData(); // 标签变化时重新获取表格数据
  };

  // 表格数据相关
  const loading = ref(false);
  const tableData = ref([]);

  // 分页设置
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 50],
    onChange: (page) => {
      pagination.page = page;
    },
    onUpdatePageSize: (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
    },
    prefix: ({ itemCount }) => h('span', {}, `共 ${itemCount} 条`),
  });

  // 表格列定义
  const columns = [
    {
      title: '客户代码',
      key: 'customerCode',
      align: 'center',
      sorter: 'default',
      width: 120,
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openClientFile(row.customerCode, row.customerName);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.customerCode }
        );
      },
    },
    {
      title: '客户名称',
      key: 'customerName',
      align: 'center',
      width: 150,
      render(row) {
        return h(
          NButton,
          {
            onClick: () => {
              openClientFile(row.customerCode, row.customerName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.customerName }
        );
      },
    },
    {
      title: '营业部',
      key: 'department',
      align: 'center',
    },
    {
      title: '持仓市值(万元)',
      align: 'center',
      key: 'marketValue',
      sorter: (row1, row2) => row1.marketValue - row2.marketValue,
      render(row) {
        return h(
          'span',
          {
            style: {
              color: row.marketValue > 500 ? '#18a058' : '',
            },
          },
          row.marketValue.toLocaleString()
        );
      },
    },
    {
      title: '负债余额(万元)',
      align: 'center',
      key: 'debt',
      sorter: (row1, row2) => row1.debt - row2.debt,
      render(row) {
        return h(
          'span',
          {
            style: {
              color: row.debt > 300 ? '#d03050' : '',
            },
          },
          row.debt
        );
      },
    },
    {
      title: '维保比例(%)',
      align: 'center',
      key: 'maintenanceRatio',
      sorter: (row1, row2) => row1.maintenanceRatio - row2.maintenanceRatio,
      render(row) {
        let color = '';
        if (row.maintenanceRatio < 150) {
          color = '#d03050'; // 红色
        } else if (row.maintenanceRatio < 200) {
          color = '#f0a020'; // 黄色
        } else {
          color = '#18a058'; // 绿色
        }

        return h(
          'span',
          {
            style: { color },
          },
          row.maintenanceRatio.toFixed(2)
        );
      },
    },
    {
      title: '日期',
      align: 'center',
      key: 'date',
      sorter: (row1, row2) => new Date(row1.date) - new Date(row2.date),
    },
  ];

  // 生成模拟数据
  const generateMockData = (count = 20) => {
    const departments = [
      '上海营业部',
      '北京营业部',
      '广州营业部',
      '深圳营业部',
      '杭州营业部',
      '成都营业部',
    ];
    const today = new Date();
    const mockData = [];

    for (let i = 0; i < count; i++) {
      const customerCode = `C${100000 + i}`;
      const marketValue = Math.round(Math.random() * 1000.8 + 100.54);
      const debt = Math.round(Math.random() * 1000.5 + 100.54);
      const maintenanceRatio = (marketValue / (debt || 1)) * 100;

      // 生成过去30天内的随机日期
      const randomDate = new Date(today);
      randomDate.setDate(today.getDate() - Math.floor(Math.random() * 30));
      const formattedDate = `${randomDate.getFullYear()}-${String(
        randomDate.getMonth() + 1
      ).padStart(2, '0')}-${String(randomDate.getDate()).padStart(2, '0')}`;
      //生成一些真实的客户名称
      let customerNameList = [
        '张玉洁',
        '李小红',
        '王小明',
        '赵小刚',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
        '杨小丽',
        '周小伟',
        '陈小芳',
        '吴小军',
        '郑小强',
        '黄小琴',
        '周小艳',
      ];
      mockData.push({
        customerCode,
        customerName: customerNameList[i],
        department: departments[Math.floor(Math.random() * departments.length)],
        marketValue: ((marketValue / (debt || 1)) * 300).toFixed(2),
        debt: ((marketValue / (debt || 1)) * 200).toFixed(2),
        maintenanceRatio,
        date: formattedDate,
      });
    }

    return mockData;
  };

  // 模拟从服务器获取数据
  const fetchTableData = () => {
    loading.value = true;

    // 模拟API请求延迟
    setTimeout(() => {
      // 如果有选择标签，根据标签过滤数据
      if (selectedTags.value.length > 0) {
        // 这里模拟根据标签过滤，实际应用中应该通过API查询
        // 简单模拟：假设每次筛选会减少数据量
        const filteredCount = Math.max(5, 50 - selectedTags.value.length * 5);
        tableData.value = generateMockData(filteredCount);
      } else {
        // 没有标签筛选时显示全部数据
        tableData.value = generateMockData();
      }

      loading.value = false;
    }, 500);
  };

  // 导出数据功能
  const exportData = () => {
    // 实际项目中应调用导出API或使用前端导出库
    // 这里只做一个简单的模拟
    alert('数据导出功能将在实际环境中实现');
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
  };

  // 监听标签变化
  watch(
    selectedTags,
    () => {
      pagination.page = 1; // 重置到第一页
    },
    { deep: true }
  );
</script>

<style scoped>
  .customer-tag-container {
    padding: 16px;
    background-color: #f9f9f9;
    min-height: 100%;
  }

  .customer-table-section {
    margin-top: 20px;
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }

  .selected-filters {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #f2f3f5;
    border-radius: 6px;
  }

  .filter-label {
    font-weight: bold;
    margin-right: 8px;
  }

  .no-filters {
    padding: 8px 16px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }
</style>
