/**
 * @file: peerApi.ts
 * @description: 用于管理同业券商数据的API接口
 * @date: 2024/8/15
 * @author: <PERSON>
 */

import { http } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import qs from 'qs';
import { PageRequest } from '@/models/common/baseRequest';
import {
  ChartAnalysisVO,
  PeerAdjustInfoVO,
  PeerFileAdjustInfoVO,
  PeerSecAdjustHisDO,
  PeerSecuritiesSetting,
} from '@/models/peerData/peerDataModels';
import { TableCellVO } from '@/models/marginTrading/model/ConcentrationModel';
import { TypeCountVO } from '@/models/common/utilModels';
import { PeerNameVO } from '@/models/peerData/peerDailyReportModels';
import { PeerAdjustCountVO } from '@/models/peerData/peerFileModels';

const globSetting = useGlobSetting();
const apiPrefix = '/tTygd';
let { prefix } = globSetting;
prefix = prefix + apiPrefix;
const prefixExport = globSetting.prefixExport;

/**
 * 获取同业券商调整类型
 * @param type
 */
export function getPeerAdjustTypes(type?: string) {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: prefix + '/getPeerAdjustTypes',
      method: 'GET',
      params: { type },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 已录入的券商列表
 */
export function getPeerAgencyList(params) {
  return http.request<BasicResponseModel<PeerNameVO[]>>(
    {
      url: prefix + '/getPeerAgencyList',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 查看调整类型明细列表
 * @param params
 */
export function queryPeerAdjustInfo(params) {
  return http.request<PaginatedResponseModel<PeerAdjustInfoVO[]>>(
    {
      url: prefix + '/peerAdjust/queryPeerAdjustInfo',
      method: 'get',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 同业证券调整/获取当前类型的调整统计信息
 * @param date
 * @param source
 * @param type
 */
export function queryPeerAdjustTypeCount(
  startDate: string,
  endDate: string,
  peerEnName: string,
  dataType: string
) {
  return http.request<BasicResponseModel<TypeCountVO[]>>(
    {
      url: prefix + '/peerAdjust/queryPeerAdjustTypeCount',
      method: 'GET',
      params: { startDate, endDate, peerEnName, dataType },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 同业证券调整/调整视图分析
 */

export function peerAdjustmentChartAnalysis(params) {
  return http.request<BasicResponseModel<ChartAnalysisVO>>(
    {
      url: prefix + '/peerAdjust/peerAdjustmentChartAnalysis',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 同业证券调整/获取同业业务数据的历史可选日期
 * @param peerEnName 同业券商名称
 * @param dataType 业务类型
 * @returns
 */
export function getPeerSecDataHistoryDate(peerEnName: string, dataType: string) {
  return http.request<BasicResponseModel<TypeCountVO[]>>(
    {
      url: prefix + '/getPeerSecDataHistoryDate',
      method: 'GET',
      params: { peerEnName, dataType },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 融资融券相关/同业券商数据/证券档案
 * 查看当前证券的同业调整历史详情表格
 * @param params
 */
export function querySecPeerAdjustHistory(params) {
  return http.request<PaginatedResponseModel<PeerSecAdjustHisDO[]>>(
    {
      url: prefix + '/querySecPeerAdjustHistory?',
      method: 'GET',
      params: qs.stringify(params, { arrayFormat: 'repeat' }),
    },
    {
      isTransformResponse: false,
    }
  );
}

// tags = {"塔金同业情报中心", "同业策略"}, summary = "标的券和担保品评级分析")
export function queryPeerLevelAnalysis(peerEnName: string, type: string) {
  return http.request<BasicResponseModel<ChartAnalysisVO>>(
    {
      url: prefix + '/peerStrategy/queryPeerLevelAnalysis',
      method: 'GET',
      params: { peerEnName, type },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 融资融券相关/同业券商数据/券商档案
 * 券商某个日期的调整详情表格
 * @param pageRequest
 * @param source
 * @param adjustType
 * @param date
 */
export function getPeerAdjustSpecific(
  pageRequest: PageRequest,
  source: string,
  adjustType: string,
  date: string
) {
  return http.request<PaginatedResponseModel<PeerFileAdjustInfoVO[]>>(
    {
      url: prefix + '/getPeerAdjustSpecific',
      method: 'GET',
      params: { ...pageRequest, source, adjustType, date },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 融资融券相关/同业券商数据/同业策略
 * 获取券商集中度管理表格坐标参考系
 *
 * @param source 同业券商名称
 */
export function queryPeerConcentrationManagementChartRef(source: string) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/peerStrategy/queryPeerConcentrationManagementChartRef',
      method: 'GET',
      params: { source },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 融资融券相关/同业券商数据/同业策略
 * 获取券商的证券分组对应集中度表格
 *
 * @param source 同业券商名称
 */
export function queryPeerConcentrationManagement(source: string) {
  return http.request<BasicResponseModel<TableCellVO[]>>(
    {
      url: prefix + '/peerStrategy/queryPeerConcentrationManagement',
      method: 'GET',
      params: { source },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取同业券商的基本信息
 * 同业情报中心/同业个券信息
 *
 */
export function getPeerSecuritiesBaseInfos(params: any) {
  return http.request<BasicResponseModel<PeerSecuritiesSetting[]>>(
    {
      url: prefix + '/getPeerSecuritiesBaseInfos',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 券商档案
 * 同业券商项调整趋势类型
 */

export function getPeerAdjustTypeTrend(params) {
  return http.request<BasicResponseModel<PeerAdjustCountVO[]>>(
    {
      url: prefix + '/getPeerAdjustTypeTrend?',
      method: 'GET',
      params: qs.stringify(params, { arrayFormat: 'repeat' }),
    },
    {
      isTransformResponse: false,
    }
  );
}

/*@Operation(tags = {"证券档案", "同业数据概览"}, summary = "导出当前证券的同业券商数据")*/
export function securityFilesGetPeerDataInfoExcel() {
  return prefixExport + apiPrefix + '/exportSecurityPeerData';
}

export function getSecFilePeerOverview(params) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getSecFilePeerOverview',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

export function getSecPeerMaintenanceToConcentration(params) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getSecPeerMaintenanceToConcentration',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
