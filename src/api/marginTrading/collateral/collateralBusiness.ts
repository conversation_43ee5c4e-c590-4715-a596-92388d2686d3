/**
 * @file: collateralBusiness
 * @description: 担保品业务相关接口
 * @date: 2024/9/3
 * @author: <PERSON>
 */
import { http } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import qs from 'qs';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import {
  CollAdmissionCensorVO,
  CollateralExportModel,
  CollateralHaircutItemDO,
  CollateralHaircutItemDTO,
  CollateralInfoVO,
  CollHaircutAdjustWarningVO,
  CollHaircutManualAdjustDO,
  CollHaircutManualAdjustVO,
  MrgTrdAdmissionDO,
} from '@/models/marginTrading/collateralModels';
import { LabelInfoVO } from '@/models/label/labelModels';
import { PageRequest } from '@/models/common/baseRequest';
import { AdmissionCensorType } from '@/enums/marginTrading/collateralEnum';
import { CommonStatus } from '@/enums/baseEnum';
import { encodeParams } from '@/utils/crypto/cryptoUtils';

const globSetting = useGlobSetting();
let { prefix, prefixExport } = globSetting;
prefix = prefix + '/margin-trading';

/**
 * 担保品准入调整 -- 查询担保品列表
 * @param params
 */
export function listSecCollateralInfos(
  params
): Promise<PaginatedResponseModel<CollateralInfoVO[]>> {
  return http.request<PaginatedResponseModel<CollateralInfoVO[]>>(
    {
      url: prefix + '/listSecCollateralInfos',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品准入调整 -- 查询证券的预警列表
 * @param params
 */
export function listCollateralWarnings(params): Promise<PaginatedResponseModel<LabelInfoVO[]>> {
  return http.request<PaginatedResponseModel<LabelInfoVO[]>>(
    {
      url: prefix + '/listCollateralWarnings',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品准入审核 -查询不同审核状态的审核列表
 * @param pageRequest
 * @param censorType
 */
export function listCollAdmissionCensorList(
  pageRequest: PageRequest,
  params: any
): Promise<PaginatedResponseModel<CollAdmissionCensorVO[]>> {
  return http.request<PaginatedResponseModel<CollAdmissionCensorVO[]>>(
    {
      url: prefix + '/listCollAdmissionCensorList',
      method: 'GET',
      params: { ...pageRequest, ...params },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品准入审核 - 单个提交审核或进行审批
 * @returns BasicResponseModel<string>
 * @param admissionRecord
 */
export function submitAdmissionCensor(
  admissionRecord: MrgTrdAdmissionDO
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + '/submitAdmissionCensor',
      method: 'POST',
      params: { ...admissionRecord },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品准入审核 - 批量提交审核或进行审批
 * @returns BasicResponseModel<string>
 * @param admissionRecordList
 */
export function multiCensorAdmissionRecords(
  admissionRecordList: MrgTrdAdmissionDO[]
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + '/multiCensorAdmissionRecords',
      method: 'POST',
      params: admissionRecordList,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品数据导出 -- 查看担保品导出列表
 * @param collateralRequest
 * @returns PaginatedResponseModel<CollateralExportModel[]>
 */
export function listExportSecCollateralInfos(
  collateralRequest: any
): Promise<PaginatedResponseModel<CollateralExportModel[]>> {
  return http.request<PaginatedResponseModel<CollateralExportModel[]>>(
    {
      url: prefix + '/listExportSecCollateralInfos?',
      method: 'GET',
      params: qs.stringify({ ...collateralRequest }, { arrayFormat: 'repeat' }),
    },
    {
      isTransformResponse: false,
    }
  );
}

// ==============================担保品折算率手动调整============================= //

/**
 * 担保品折算率手动调整 -- 获取折算率调整警告字典
 * @returns {Promise<BasicResponseModel<string[]>>}
 */
export function getHaircutAdjustWarningDict(): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: prefix + '/getHaircutAdjustWarningDict',
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率手动调整 -- 获取折算率调整警告列表
 */
export function getCollateralHaircutAdjustWarnings(
  pageRequest: PageRequest,
  level: string | null,
  warningType: string | null,
  date: string
): Promise<PaginatedResponseModel<CollHaircutAdjustWarningVO[]>> {
  return http.request<PaginatedResponseModel<CollHaircutAdjustWarningVO[]>>(
    {
      url: prefix + '/getCollateralHaircutAdjustWarnings',
      method: 'GET',
      params: { ...pageRequest, level, warningType, date },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率手动调整 -- 获取担保品手动调整详情
 * @returns {Promise<BasicResponseModel<CollateralInfoVO>>}
 */
export function getCollateralHaircutManualAdjust(params) {
  params = encodeParams(params);
  return http.request<BasicResponseModel<CollHaircutManualAdjustDO>>(
    {
      url: prefix + '/haircutManualAdjust',
      method: 'GET',
      params: { ...params },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 删除折算率手动调整记录
 * @param id
 */
export function deleteHaircutManualAdjust(id) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/haircutManualAdjust?id=' + id,
      method: 'DELETE',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存折算率手动调整记录
 * @param params
 */
export function saveHaircutManualAdjust(params: CollHaircutAdjustWarningVO) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/haircutManualAdjust',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率手动调整 -- 获取担保品手动调整列表
 * @returns {Promise<BasicResponseModel<CollAdmissionCensorVO>>}
 * @param pageRequest   分页请求
 * @param enableStatus  启用状态
 * @param secCode       证券代码
 * @param ifUser        是否是用户
 */
export function getCollHaircutManualAdjustList(
  pageRequest: PageRequest,
  enableStatus: CommonStatus | null,
  secCode: string | null,
  ifUser: boolean | null
) {
  return http.request<PaginatedResponseModel<CollHaircutManualAdjustVO[]>>(
    {
      url: prefix + '/getCollHaircutManualAdjustList',
      method: 'GET',
      params: { ...pageRequest, enableStatus, secCode, ifUser },
    },
    {
      isTransformResponse: false,
    }
  );
}
