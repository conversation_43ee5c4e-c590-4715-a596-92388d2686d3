/**
 * @file: collateralHaircutConfiguration.
 * @description: 担保品折算率配置相关
 * @date: 2024/10/12
 */
import { http } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import {
  CollateralHaircutItemDTO,
  CollateralHaircutModelDO,
} from '@/models/marginTrading/collateralModels';
import { LabelInfoVO } from '@/models/label/labelModels';
import { PageRequest } from '@/models/common/baseRequest';
import { AdmissionCensorType } from '@/enums/marginTrading/collateralEnum';
import qs from 'qs';
import { CommonStatus } from '@/enums/baseEnum';
import { encodeParams } from '@/utils/crypto/cryptoUtils';

let { prefix, prefixExport } = useGlobSetting();
const api = '/haircut-model';
prefix += api;
prefixExport += api;

/**
 * 担保品折算率调整 -- 查询用户担保品折算率模型
 * @returns CollateralHaircutModelDO[]
 */
export function collateralHaircutModels(
  pageRequest: PageRequest,
  ifSystemUser: boolean,
  ifOnlyMainModel?: boolean | null
): Promise<PaginatedResponseModel<CollateralHaircutModelDO[]>> {
  return http.request<PaginatedResponseModel<CollateralHaircutModelDO[]>>(
    {
      url: prefix + '/collateralHaircutModels',
      method: 'GET',
      params: { ...pageRequest, ifSystemUser, ifOnlyMainModel },
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 担保品折算率调整 -- 新建或保存担保品折算率模型
 * @returns CollateralHaircutModelDO
 */

export function saveCollateralHaircutModel(
  modelId: string,
  params: CollateralHaircutModelDO
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/collateralHaircutModel?modelId=' + modelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 删除担保品折算率模型
 * @param modelId
 */
export function deleteCollateralHaircutModel(modelId) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/collateralHaircutModel?modelId=' + modelId,
      method: 'DELETE',
    },
    {
      isTransformResponse: false,
    }
  );
}
