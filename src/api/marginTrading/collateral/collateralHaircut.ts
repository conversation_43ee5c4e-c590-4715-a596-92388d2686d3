/**
 * @file: collateralHaircut
 * @description: 担保品折算率相关
 * @date: 2024/9/7
 * @author: <PERSON>
 */
import { http } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import {
  CollAdmissionCensorVO,
  CollateralExportModel,
  CollateralHaircutCeilSettingDO,
  CollateralHaircutItemDO,
  CollateralHaircutItemDTO,
  CollateralInfoVO,
  CollHaircutAdjustWarningVO,
  CollHaircutCeilSettingVO,
  MrgTrdAdmissionDO,
  HaircutModelCompareVO,
} from '@/models/marginTrading/collateralModels';
import { LabelInfoVO } from '@/models/label/labelModels';
import { PageRequest } from '@/models/common/baseRequest';
import { AdmissionCensorType } from '@/enums/marginTrading/collateralEnum';
import qs from 'qs';
import { CommonStatus } from '@/enums/baseEnum';
import { encodeParams } from '@/utils/crypto/cryptoUtils';

let { prefix, prefixExport } = useGlobSetting();
const prefixManual = prefix + '/collateral-haircut';

const api = '/haircut-model';
prefix += api;
prefixExport += api;

/**
 * 担保品折算率调整 -- 获取当前模型的折算率所有计算字典
 * @returns BasicResponseModel<string>
 */
export function getHaircutModelDictListByModelId(
  modelId: string | undefined
): Promise<BasicResponseModel<CollateralHaircutItemDTO[]>> {
  return http.request<BasicResponseModel<CollateralHaircutItemDTO[]>>(
    {
      url: prefix + '/getHaircutModelDictListByModelId',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 获取当前模型的折算率所有计算事项
 * @returns BasicResponseModel<string>
 */
export function getHaircutModelItemListByModelId(
  modelId: string | undefined
): Promise<BasicResponseModel<CollateralHaircutItemDTO[]>> {
  return http.request<BasicResponseModel<CollateralHaircutItemDTO[]>>(
    {
      url: prefix + '/getHaircutModelItemListByModelId',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 获取当前折算率监控事项所有选项
 * @param {string} dictId - 字典ID
 * @returns {Promise<BasicResponseModel<CollateralHaircutItemDO[]>>} - 折算率监控事项所有选项
 */
export function getHaircutModelCurrentDictItems(
  dictId: string | null
): Promise<BasicResponseModel<CollateralHaircutItemDO[]>> {
  return http.request<BasicResponseModel<CollateralHaircutItemDO[]>>(
    {
      url: prefix + '/getHaircutModelCurrentDictItems',
      method: 'GET',
      params: { dictId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 新增或修改折算率监控事项阈值
 * @param {CollateralHaircutItemDO} item - 折算率监控事项
 * @returns {Promise<BasicResponseModel<string>>} - 保存或更新结果
 */
export function haircutModelItem(
  item: CollateralHaircutItemDO
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + '/haircutModelItem',
      method: 'POST',
      params: { ...item },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 删除当前用户某个计算事项阈值
 * @returns {Promise<BasicResponseModel<string>>} - 保存或更新结果
 * @param itemId
 */
export function deleteCollHaircutModelItem(
  itemId: string | null
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + `/haircutModelItem?`,
      method: 'delete',
      params: qs.stringify({ itemId }),
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 选择折算率监控事项
 * @returns {Promise<BasicResponseModel<string>>}
 * @param param
 */
export function saveHaircutModelCurrentDictItems(
  dictId: string | undefined,
  modelId: string | undefined,
  param: any
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + '/saveHaircutModelCurrentDictItems?dictId=' + dictId + '&modelId=' + modelId,
      method: 'POST',
      data: param,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 查看折算率上限设置选项
 */
export function listCollateralHaircutCeilOptions(
  modelId: string
): Promise<BasicResponseModel<CollHaircutCeilSettingVO>> {
  return http.request<BasicResponseModel<CollHaircutCeilSettingVO>>(
    {
      url: prefix + '/listCollateralHaircutCeilOptions',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 查看折算率上限设置列表
 */
export function listCollateralHaircutCeilSettings(
  modelId: string
): Promise<BasicResponseModel<CollateralHaircutCeilSettingDO[]>> {
  return http.request<BasicResponseModel<CollateralHaircutCeilSettingDO[]>>(
    {
      url: prefix + '/listCollateralHaircutCeilSettings',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 保存折算率上限设置
 * @param params
 */
export function saveCollateralHaircutCeilSetting(
  params: CollateralHaircutCeilSettingDO[]
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: prefix + '/saveCollateralHaircutCeilSetting',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 担保品折算率调整 -- 保存折算率监控事项调整结果
 * @param params
 */
export function saveCollHaircutChooseItems(
  params: CollateralHaircutItemDTO[],
  modelId: string | undefined
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/saveCollHaircutChooseItems?modelId=' + modelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 担保品折算率调整 -- 保存当前模型的折算率模型计算字典的所有选择结果
 * @param params
 */
export function saveHaircutModelDictListByModelId(
  params: CollateralHaircutItemDTO[],
  modelId: string | undefined
): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/saveHaircutModelDictListByModelId?modelId=' + modelId,
      method: 'POST',
      data: JSON.stringify(params),
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 折算率模型 -- 根据证券品种获取所有可选的计算选项
 * @param params
 */
export function getSecCategoryOptions(
  secType: string | undefined
): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getModelSecCategoryOptions',
      method: 'GET',
      params: { secType },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 查看当前模型的当前证券品种的配置
 */
export function getCollateralHaircutConfig(params: any) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/config',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 保存当前模型的当前证券品种的配置
 */
export function saveCollateralHaircutConfig(
  modelId: number | undefined,
  secType: string | undefined,
  params: any
) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/config?modelId=' + modelId + '&secType=' + secType,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的交易所折扣模板
 */
export function getExchangeDiscountTemplates(modelId: number | undefined) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/getExchangeDiscountTemplates',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 保存当前模型的交易所折扣模板
 */
export function saveExchangeDiscountTemplates(modelId: number | undefined, params: any) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/saveExchangeDiscountTemplates?modelId=' + modelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的默认折算率
 */
export function getModelDefaultHaircut(params: any) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/modelDefaultHaircut',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 保存当前模型的默认折算率
 */
export function saveModelDefaultHaircut(params: any) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/modelDefaultHaircut',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取折算率模型结果历史比较
 */
export function queryHaircutModelCompare(params: any) {
  return http.request<PaginatedResponseModel<HaircutModelCompareVO[]>>(
    {
      url: prefix + '/compare',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 导出折算率模型结果历史比较
 */
export function exportHaircutModelCompare(params: any) {
  return http.request<any>(
    {
      url: prefix + '/compare/export',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
