/*
集中度模型相关接口
 */

import { http } from '@/utils/http/axios';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import { useGlobSetting } from '@/hooks/setting';
import {
  ConcentrationModelConfigDO,
  LevelMaintenanceConcentrationVO,
  TableCellVO,
} from '@/models/marginTrading/model/ConcentrationModel';
import { PageRequest } from '@/models/common/baseRequest';

const globSetting = useGlobSetting();
let { prefix } = globSetting;
prefix += '/concentration-model';

/**
 * 查询用户集中度模型列表
 */
export function getConcentrationModel(
  pageRequest: PageRequest,
  ifUser: boolean,
  ifOnlyMainModel?: boolean | null
) {
  return http.request<PaginatedResponseModel<ConcentrationModelConfigDO[]>>(
    {
      url: prefix + '/concentrationModel',
      method: 'GET',
      params: { ...pageRequest, ifUser, ifOnlyMainModel },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 新建或保存集中度模型
 */
export function updateConcentrationModel(params: ConcentrationModelConfigDO) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/concentrationModel',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 删除集中度模型
 */
export function deleteConcentrationModel(modelId: string) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/concentrationModel?modelId=' + modelId,
      method: 'DELETE',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 设置当前模型的维保比例
 * @param params
 * @param modelId
 */
export function setModelMaintenanceMarginRatio(params: any, modelId: string | null) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/setModelMaintenanceMarginRatio?modelId=' + modelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 查看当前模型的维保比例列表
 * @param params
 * @param modelId
 */
export function getModelMaintenanceMarginRatioList(params: any, modelId: string | null) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getModelMaintenanceMarginRatioList?modelId=' + modelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 查看当前模型的当前分类集中度区间表格
 * @param params
 */
export function getModelLevelConcentrationChart(params: {
  modelId: string | null;
  ifRegi: number | null;
}) {
  return http.request<BasicResponseModel<TableCellVO[]>>(
    {
      url: prefix + '/getModelLevelConcentrationChart',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 查看当前模型的当前评级对应的维保比例集中度
 * @param params
 */
export function getModelLevelMaintenanceConcentration(
  modelId: string | null,
  level: string | null
) {
  return http.request<BasicResponseModel<LevelMaintenanceConcentrationVO[]>>(
    {
      url: prefix + '/getModelLevelMaintenanceConcentration',
      method: 'GET',
      params: { modelId, level },
    },
    {
      isTransformResponse: false,
    }
  );
}

export function setLevelMaintenanceConcentration(
  params: any,
  modelId: string | null,
  level: string | null
) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/setModelLevelMaintenanceConcentration?modelId=' + modelId + '&level=' + level,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
