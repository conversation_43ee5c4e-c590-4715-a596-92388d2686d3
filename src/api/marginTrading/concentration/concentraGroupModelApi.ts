/*
集中度分组模型相关模型
 */

import { http } from '@/utils/http/axios';
import {
  BasicResponseModel,
  ConcentraGroupModelRuleRemarkDO,
  CounterSecurityCategoryDictDO,
  PaginatedResponseModel,
} from '@/models/common/baseResponse';
import { useGlobSetting } from '@/hooks/setting';
import {
  ConcentraGroupModelResultCompareVO,
  LevelSrtModelConcentraGroupMappingVO,
  ModelSecConcentraGroupHistoryDO,
} from '@/models/marginTrading/model/ConcentraGroupModel';
import { TypeCountVO } from '@/models/common/utilModels';

const globSetting = useGlobSetting();
let { prefix } = globSetting;
prefix += '/concentra-group-model';

/**
 * 查看当前模型的当前证券品种的配置
 */
export function queryConcentraGroupModelList(params: any) {
  return http.request<BasicResponseModel<ConcentraGroupModelRuleRemarkDO[]>>(
    {
      url: prefix + '/config',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前证券分类默认集中度分组
 */
export function queryDefaultConcentraGroup(params: any) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/default-concentra-group',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存当前证券分类默认集中度分组
 */
export function saveDefaultConcentraGroup(params: any) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/default-concentra-group',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前集中度分组选项配置
 */
export function querySelectableGroupOption() {
  return http.request<BasicResponseModel<CounterSecurityCategoryDictDO[]>>(
    {
      url: prefix + '/selectableGroupOption',
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存当前集中度分组选项配置
 */
export function saveSelectableGroupOption(params: CounterSecurityCategoryDictDO[]) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/selectableGroupOption',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取债券评级所有可选项
 */
export function queryBondRatingOptions(params: any) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/bond-rating-options',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存当前模型的当前证券品种的配置
 */
export function saveConcentraGroupModelList(
  modelId: number | undefined,
  secType: string | undefined,
  params: any
) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/config?modelId=' + modelId + '&secType=' + secType,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前证券品种的评级映射
 */
export function queryLevelMapping(modelId: string | undefined, secType: string | undefined) {
  return http.request<PaginatedResponseModel>(
    {
      url: prefix + '/level-mapping?modelId=' + modelId + '&secType=' + secType,
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存当前证券品种的评级映射
 */
export function saveLevelMapping(
  modelId: string | undefined,
  secType: string | undefined,
  levelModelId: string | undefined,
  params: any
) {
  return http.request<PaginatedResponseModel>(
    {
      url:
        prefix +
        '/level-mapping?modelId=' +
        modelId +
        '&secType=' +
        secType +
        '&levelModelId=' +
        levelModelId,
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 点击计算当前模型
 * modelId：模型ID
 *
 */
export function calculateConcentraGroupModel(modelId: any) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/calculateModel?modelId=' + modelId,
      method: 'POST',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的模型结果可选日期
 * modelId：模型ID
 *
 */
export function queryResultDateOption(modelId: any) {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: prefix + '/result/dateOption',
      method: 'GET',
      params: { modelId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的模型结果
 *
 */
export function queryConcentraGroupModelResult(params: any) {
  return http.request<PaginatedResponseModel<ModelSecConcentraGroupHistoryDO[]>>(
    {
      url: prefix + '/result',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的股票的集中度分组映射结果
 *
 */
export function queryStockConcentraGroupMapResult(params: any) {
  return http.request<PaginatedResponseModel<LevelSrtModelConcentraGroupMappingVO[]>>(
    {
      url: prefix + '/result/stockConcentraGroupMap',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的集中度分组数量统计
 *
 */
export function queryConcentraGroupCountResult(params: any) {
  return http.request<PaginatedResponseModel<TypeCountVO[]>>(
    {
      url: prefix + '/result/concentraGroupCount',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取当前模型的模型结果比较
 *
 */
export function queryConcentraGroupModelResultCompare(params: any) {
  return http.request<PaginatedResponseModel<ConcentraGroupModelResultCompareVO[]>>(
    {
      url: prefix + '/result/compare',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 导出当前模型的模型结果比较
 *
 */
export function exportConcentraGroupModelResultCompare(params: any) {
  return http.request<any>(
    {
      url: prefix + '/result/compare/export',
      method: 'GET',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
