import { http } from '@/utils/http/axios';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import { useGlobSetting } from '@/hooks/setting';
import { FunctionDictDO, RoleDO } from '@/models/common/basic/roleModels';
import { PageRequest } from '@/models/common/baseRequest';

const globSetting = useGlobSetting();

/**
 * url路径前缀
 */
const basicPrefix = globSetting.basicPrefix + '/tRole';

// /**
//  * @description: 角色列表
//  */
// export function getRoleList() {
//   return http.request({
//     url: '/role/list',
//     method: 'GET',
//   });
// }

/**
 * 模糊查询角色列表
 * @param roleName 角色名称
 * @returns Promise<BasicResponseModel<string[]>>
 */
export function vagueQueryRoleList(roleName: string): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: basicPrefix + '/vagueQueryRoleList',
      method: 'GET',
      params: { roleName },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 角色列表
 * @param roleName 角色名称
 * @param userId 用户ID
 * @param enableStatus 启用状态 0:启用 1:禁用
 * @param pageRequest 分页参数
 * @returns Promise<PaginatedResponseModel<RoleDO>>
 */
export function tRoleList(
  roleName: string | null,
  userId: string | null,
  enableStatus: number | null,
  pageRequest: PageRequest | any
): Promise<PaginatedResponseModel<RoleDO>> {
  return http.request<PaginatedResponseModel<RoleDO>>(
    {
      url: basicPrefix + '/list',
      method: 'GET',
      params: { roleName, userId, enableStatus, ...pageRequest },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 根据角色id获取菜单id列表
 * @param roleId
 */
export function getMenuIdsByRoleId(roleId: string): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: basicPrefix + '/getMenuIdsByRoleId',
      method: 'GET',
      params: { roleId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 删除角色
 * @param roleId 角色id
 * @returns Promise<BasicResponseModel<string>>
 */
export function deleteRole(roleId: string): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: basicPrefix + '/deleteRole?roleId=' + roleId,
      method: 'DELETE',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 根据角色id查询置灰菜单id列表
 * @param roleId 角色id
 * @returns Promise<BasicResponseModel<string[]>>
 */
export function getGreyMenuIdsByRoleId(roleId: string): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: basicPrefix + '/getGreyMenuIdsByRoleId',
      method: 'GET',
      params: { roleId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取系统所有功能权限
 * @returns Promise<BasicResponseModel<FunctionDictDO[]>>
 */
export function getAllFunctions(): Promise<BasicResponseModel<FunctionDictDO[]>> {
  return http.request<BasicResponseModel<FunctionDictDO[]>>(
    {
      url: basicPrefix + '/getAllFunctions',
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 角色管理 - 根据角色id获取功能权限
 * @returns Promise<BasicResponseModel<FunctionDictDO[]>>
 */
export function getFunctionsByRoleId(
  roleId: string
): Promise<BasicResponseModel<FunctionDictDO[]>> {
  return http.request<BasicResponseModel<FunctionDictDO[]>>(
    {
      url: basicPrefix + '/getFunctionsByRoleId',
      method: 'GET',
      params: { roleId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 角色管理
 */
export function saveOrUpdateRole(params) {
  return http.request<BasicResponseModel>(
    {
      url: basicPrefix + '/saveOrUpdate',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
