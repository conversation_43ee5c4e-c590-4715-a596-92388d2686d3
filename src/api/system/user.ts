/**
 * 用户相关 接口管理
 */

import { http } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import { LevelCriteriaDetailDO, LevelCriteriaDO, UserDO } from '@/models/common/basic/userModels';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import { CommonEnum } from '@/enums/baseEnum';
import { PageRequest } from '@/models/common/baseRequest';
import { FunctionDictDO } from '@/models/common/basic/roleModels';

const globSetting = useGlobSetting();
let { basicPrefix } = globSetting;
basicPrefix += '/user';

/**
 * 获取用户评级标准
 *
 * 如果 `ifSystemUser` 参数为 `true`，则查询系统的评级标准。
 *
 * @param {boolean} ifSystemUser - 是否系统用户
 * @returns {Promise<BasicResponseModel<LevelCriteriaDetailDO[]>>} 用户评级标准列表的响应
 */
export function getUserLevelCriteria(
  ifSystemUser: boolean
): Promise<BasicResponseModel<LevelCriteriaDetailDO[]>> {
  return http.request<BasicResponseModel<LevelCriteriaDetailDO[]>>(
    {
      url: basicPrefix + '/getUserLevelCriteria',
      method: 'GET',
      params: { ifSystemUser },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取所有评级标准
 *
 * @param {CommonEnum} enableStatus 是否启用
 * @returns {Promise<BasicResponseModel<LevelCriteriaDO[]>>}
 */
export function getAllLevelCriteria(
  enableStatus: CommonEnum
): Promise<BasicResponseModel<LevelCriteriaDO[]>> {
  return http.request<BasicResponseModel<LevelCriteriaDO[]>>(
    {
      url: basicPrefix + '/getAllLevelCriteria',
      method: 'GET',
      params: { enableStatus },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取用户的评级标准ID
 *
 * @param {string} userId 用户ID
 * @returns {Promise<BasicResponseModel<number>>} 用户的评级标准ID
 */
export function getUserCriteriaId(userId: string): Promise<BasicResponseModel<number>> {
  return http.request<BasicResponseModel<number>>(
    {
      url: basicPrefix + '/getUserCriteriaId',
      method: 'GET',
      params: { userId },
    },
    {
      isTransformResponse: false,
    }
  );
}
/**
 * 用户列表
 *
 * @returns {Promise<PaginatedResponseModel<UserDO>>}
 * @param pageRequest 分页参数
 * @param userName 用户名
 */
export function userList(
  pageRequest: PageRequest,
  userName: string | null,
  userId: string | null
): Promise<PaginatedResponseModel<UserDO>> {
  return http.request<PaginatedResponseModel<UserDO>>(
    {
      url: basicPrefix + '/list',
      method: 'GET',
      params: { ...pageRequest, userName, userId },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 模糊查询用户列表
 * @param userName 用户名
 * @returns {Promise<BasicResponseModel<string[]>>}
 */
export function vagueQueryUsers(userName: string): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: basicPrefix + '/vagueQueryUsers',
      method: 'GET',
      params: { userName },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 保存或更新用户
 * @param params
 * @returns {Promise<BasicResponseModel<string>>}
 */
export function saveOrUpdate(params): Promise<BasicResponseModel<string>> {
  return http.request<BasicResponseModel<string>>(
    {
      url: basicPrefix + '/saveOrUpdate',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取用户所有功能权限
 * @returns Promise<BasicResponseModel<FunctionDictDO[]>>
 */
export function getUserAllFunctionss(
  userId: string | null
): Promise<BasicResponseModel<FunctionDictDO[]>> {
  return http.request<BasicResponseModel<FunctionDictDO[]>>(
    {
      url: basicPrefix + '/getUserAllFunctions?userId=' + userId,
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 删除用户
 * @param userId 用户ID
 * @returns Promise<BasicResponseModel<string>>
 */
export function deleteUser(userId: string): Promise<BasicResponseModel<string>> {
  // 验证参数
  if (!userId || userId.trim() === '') {
    throw new Error('用户ID不能为空');
  }

  console.log('发送删除用户请求，用户ID:', userId);

  // 直接在URL中拼接参数，确保作为查询参数发送
  const trimmedUserId = userId.trim();
  const urlWithParams = `${basicPrefix}/delete?userId=${encodeURIComponent(trimmedUserId)}`;

  return http.request<BasicResponseModel<string>>(
    {
      url: urlWithParams,
      method: 'DELETE',
      // 不使用params，直接在URL中拼接，避免被拦截器处理
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 修改当前用户密码（统一接口）
 * 支持首次登录修改密码和普通密码修改
 * 使用 ChangePasswordRequest 参数结构
 * @param oldPassword 原密码
 * @param newPassword 新密码
 * @returns Promise<BasicResponseModel>
 */
export function changePassword(
  oldPassword: string,
  newPassword: string
): Promise<BasicResponseModel> {
  return http.request<BasicResponseModel>(
    {
      url: basicPrefix + '/modifyPassword',
      method: 'POST',
      data: { oldPassword, newPassword },
    },
    {
      isTransformResponse: false,
    }
  );
}
