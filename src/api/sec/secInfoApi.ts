/**
 * @file: secInfo
 * @description: 证券基本信息相关接口
 * @date: 2024/8/24
 * @author: <PERSON>
 */

import { http } from '@/utils/http/axios';
import { BasicResponseModel, PaginatedResponseModel } from '@/models/common/baseResponse';
import { useGlobSetting } from '@/hooks/setting';
import qs from 'qs';
import { SecTypeSubTypeVO } from '@/models/sec/secInfoModels';
import { ExchangeProductInfoDO } from '@/models/sec/secProfileModels';
import { PageRequest } from '@/models/common/baseRequest';
import { SecType } from '@/enums/secEnum';

const globSetting = useGlobSetting();
let { prefix } = globSetting;
/**
 * url路径前缀
 */
prefix = prefix + '/tStockinfo';

/**
 * 获取证券基本信息
 * @param params
 */
export function getStockInfo(params: string[]) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getStockInfo?',
      method: 'GET',
      params: qs.stringify(params, { arrayFormat: 'repeat' }),
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获取股票板块类型
 */
export function getStockType(): Promise<BasicResponseModel<string[]>> {
  return http.request<BasicResponseModel<string[]>>(
    {
      url: prefix + '/getStockTypes',
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * 获得所有证券类型及其子分类
 */
export function getSecTypeAndSubTypes() {
  return http.request<BasicResponseModel<SecTypeSubTypeVO[] | any>>(
    {
      url: prefix + '/getSecTypeAndSubTypes',
      method: 'GET',
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description: 查询证券基本信息详情
 */
export function vagueQuerySecCodes(
  pageRequest: PageRequest,
  queryStr: string,
  secType: SecType | null
) {
  secType = secType != null ? Number(secType) : null;
  return http.request<PaginatedResponseModel<ExchangeProductInfoDO>>(
    {
      url: prefix + '/vagueQuerySecCodes',
      method: 'GET',
      params: { ...pageRequest, queryStr, secType },
    },
    {
      isTransformResponse: false,
    }
  );
}

/**
 * @description 证券搜索，只包含股票
 * @deprecated 已废弃
 */
export function listCodeAndName(params) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/listCodeAndName',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

export function getIfSuperAccount(params) {
  return http.request<BasicResponseModel>(
    {
      url: prefix + '/getIfSuperAccount',
      method: 'POST',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
