/**
 * @deprecated 自 v2.0.0 起废弃，将在 v3.0.0 中移除。请使用 {@link cryptoUtils} 替代。
 *
 * @example
 * ```typescript
 * // ❌ 旧用法（已废弃）
 * import { encrypt } from '@/utils/crypto/encode';
 *
 * // ✅ 新用法
 * import { encrypt } from '@/utils/crypto/cryptoUtils';
 * ```
 */

const base64EncodeChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

//加密
export function encrypt(str: string, key: string = base64EncodeChars): string {
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

//解密
export function decrypt(str: string, key: string = base64EncodeChars): string {
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

/**
 * 对象参数编码工具函数
 * 对对象中的所有字符串属性进行 URL 编码
 *
 * @param {Object} params - 需要编码的对象
 * @returns {Object} - 编码后的对象
 */
export function encodeParams(params: any): any {
  if (params === null || params === undefined) {
    return params;
  }

  if (typeof params === 'object' && !Array.isArray(params)) {
    const encodedParams: Record<string, any> = {};
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        encodedParams[key] = encodeParams(params[key]);
      }
    }
    return encodedParams;
  }

  if (typeof params === 'string') {
    return encodeURIComponent(params);
  }

  if (Array.isArray(params)) {
    return params.map((item) => encodeParams(item));
  }

  return params;
}

/**
 * 对URL查询参数中的方括号进行编码
 * 主要用于搜索功能，确保包含方括号的查询词能正确传递给API
 *
 * @param str - 需要编码的字符串
 * @returns 编码后的字符串，方括号被转换为 %5B 和 %5D
 *
 * @example
 * ```typescript
 * encodeUrlBrackets('策略[A]') // 返回: '策略%5BA%5D'
 * ```
 */
export function encodeUrlBrackets(str: string): string {
  return str.replace(/\[/g, '%5B').replace(/\]/g, '%5D');
}
