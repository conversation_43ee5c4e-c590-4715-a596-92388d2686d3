/**
 * @file cryptoUtils.ts
 * @description 加密解密和编码工具集合，提供XOR加密、URL编码等功能
 * <AUTHOR> Ye
 * @version 1.0.0
 */

/**
 * Base64 字符集，用作默认的 XOR 加密密钥
 * @constant
 */
const base64EncodeChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

/**
 * XOR 加密函数
 *
 * @description 使用 XOR 异或运算对字符串进行加密。该算法具有对称性，
 * 即使用相同的密钥可以进行加密和解密。主要用于敏感数据的简单加密处理，
 * 如 URL 参数中的用户ID、证券代码等信息的保护。
 *
 * **安全说明：** XOR 加密是一种简单的对称加密算法，适用于轻量级的数据混淆，
 * 但不适合高安全性要求的场景。对于敏感数据，建议使用更强的加密算法。
 *
 * @param {string} str - 需要加密的原始字符串
 * @param {string} [key=base64EncodeChars] - 加密密钥，默认使用 Base64 字符集
 *
 * @returns {string} 加密后的字符串
 *
 * @example
 * ```typescript
 * // 使用默认密钥加密
 * const encrypted = encrypt('Hello World');
 * console.log(encrypted); // 输出加密后的字符串
 *
 * // 使用自定义密钥加密
 * const customEncrypted = encrypt('sensitive data', 'mySecretKey');
 * ```
 *
 * @since 1.0.0
 */
export function encrypt(str: string | null, key: string = base64EncodeChars): string {
  if (str === null) {
    return '';
  }
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

/**
 * XOR 解密函数
 *
 * @description 使用 XOR 异或运算对加密字符串进行解密。由于 XOR 运算的对称性，
 * 解密过程与加密过程完全相同。必须使用与加密时相同的密钥才能正确解密。
 *
 * **重要提示：** 解密时必须使用与加密时完全相同的密钥，否则会得到错误的结果。
 *
 * @param {string} str - 需要解密的加密字符串
 * @param {string} [key=base64EncodeChars] - 解密密钥，必须与加密时使用的密钥相同
 *
 * @returns {string} 解密后的原始字符串
 *
 * @example
 * ```typescript
 * // 加密和解密示例
 * const original = 'Hello World';
 * const encrypted = encrypt(original);
 * const decrypted = decrypt(encrypted);
 * console.log(decrypted === original); // true
 *
 * // 使用自定义密钥
 * const customKey = 'mySecretKey';
 * const customEncrypted = encrypt('sensitive data', customKey);
 * const customDecrypted = decrypt(customEncrypted, customKey);
 * ```
 *
 * @since 1.0.0
 */
export function decrypt(str: string, key: string = base64EncodeChars): string {
  let result = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

/**
 * 递归对象参数编码工具函数
 *
 * @description 深度遍历对象，对所有字符串类型的属性值进行 URL 编码（encodeURIComponent）。
 * 支持嵌套对象和数组的递归处理，确保所有字符串数据都被正确编码，
 * 主要用于 HTTP 请求参数的预处理，防止特殊字符导致的传输问题。
 *
 * **处理规则：**
 * - 字符串：使用 encodeURIComponent 进行 URL 编码
 * - 对象：递归处理所有属性
 * - 数组：递归处理所有元素
 * - null/undefined：保持原值
 * - 其他类型：保持原值
 *
 * @param {any} params - 需要编码的参数对象，可以是任意类型
 *
 * @returns {any} 编码后的参数对象，保持原有的数据结构
 *
 * @example
 * ```typescript
 * // 简单对象编码
 * const params = {
 *   name: '张三',
 *   email: '<EMAIL>',
 *   query: 'hello world'
 * };
 * const encoded = encodeParams(params);
 * // 结果: { name: '%E5%BC%A0%E4%B8%89', email: 'user%40example.com', query: 'hello%20world' }
 *
 * // 嵌套对象编码
 * const complexParams = {
 *   user: {
 *     name: '李四',
 *     tags: ['标签1', '标签2']
 *   },
 *   count: 100
 * };
 * const encodedComplex = encodeParams(complexParams);
 * ```
 *
 * @since 1.0.0
 */
export function encodeParams(params: any): any {
  if (params === null || params === undefined) {
    return params;
  }

  if (typeof params === 'object' && !Array.isArray(params)) {
    const encodedParams: Record<string, any> = {};
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        encodedParams[key] = encodeParams(params[key]);
      }
    }
    return encodedParams;
  }

  if (typeof params === 'string') {
    return encodeURIComponent(params);
  }

  if (Array.isArray(params)) {
    return params.map((item) => encodeParams(item));
  }

  return params;
}

/**
 * URL 查询参数方括号编码函数
 *
 * @description 专门用于编码 URL 查询参数中的方括号字符。将方括号 `[` 和 `]`
 * 转换为对应的 URL 编码形式 `%5B` 和 `%5D`。主要用于搜索功能中包含方括号的查询词，
 * 确保这些特殊字符能够正确传递给后端 API，避免 URL 解析错误。
 *
 * **使用场景：**
 * - 搜索功能中的查询词包含方括号
 * - API 参数中需要传递包含方括号的字符串
 * - 防止方括号被误解为数组标记
 *
 * @param {string} str - 需要编码方括号的字符串
 *
 * @returns {string} 编码后的字符串，方括号被转换为 %5B 和 %5D
 *
 * @example
 * ```typescript
 * // 编码包含方括号的搜索词
 * const searchTerm = '策略[A]';
 * const encoded = encodeUrlBrackets(searchTerm);
 * console.log(encoded); // '策略%5BA%5D'
 *
 * // 编码复杂的查询字符串
 * const complexQuery = '数据[2024][Q1]报告';
 * const encodedQuery = encodeUrlBrackets(complexQuery);
 * console.log(encodedQuery); // '数据%5B2024%5D%5BQ1%5D报告'
 *
 * // 在 API 调用中使用
 * const apiUrl = `/api/search?q=${encodeUrlBrackets('策略[A]')}`;
 * ```
 *
 * @since 1.0.0
 */
export function encodeUrlBrackets(str: string): string {
  return str.replace(/\[/g, '%5B').replace(/\]/g, '%5D');
}
