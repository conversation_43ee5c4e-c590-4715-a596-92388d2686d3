/**
 * @file stockTableColumnFactory.ts
 * @description 股票表格列工厂函数，用于创建标准化的表格列配置
 * <AUTHOR> Ye
 */

import { h as createElement } from 'vue';
import { DataTableColumn, NButton, NIcon, NPopover } from 'naive-ui';
import { HelpCircleOutline } from '@vicons/ionicons5';
import { getComparisonColor, getFinancialColor, setLevelColor } from '@/utils/ui/color/LevelColor';
import { openStockArchives } from '@/utils/goToArchives';
import { StockTableInfoVO } from '@/models/common/utilModels';

// ==================== 类型定义 ====================

/**
 * 按钮列配置
 */
interface ButtonColumnConfig<T = any> {
  /** 列标题 */
  title: string;
  /** 数据字段名 */
  key: string;
  /** 列宽度，如 '110px' */
  width?: string;
  /** 固定位置：左侧或右侧 */
  fixed?: 'left' | 'right';
  /** 按钮点击事件处理函数 */
  onClick: (row: T) => void;
  /** 按钮类型：信息、主要、警告、错误 */
  buttonType?: 'info' | 'primary' | 'warning' | 'error';
}

/**
 * 财务列配置
 */
interface FinancialColumnConfig {
  /** 列标题 */
  title: string;
  /** 数据字段名 */
  key: string;
  /** 列宽度，如 '140px'，默认 '140px' */
  width?: string;
  /** 是否启用排序功能，默认 true */
  sorter?: boolean;
}

/**
 * 比较列配置（带中位数对比）
 */
interface ComparisonColumnConfig {
  /** 列标题 */
  title: string;
  /** 主要数据字段名 */
  key: string;
  /** 中位数数据字段名，用于对比显示 */
  medianKey: string;
  /** 列宽度，如 '150px'，默认 '150px' */
  width?: string;
  /** 是否启用排序功能，默认 true */
  sorter?: boolean;
}

/**
 * 分组列配置
 */
interface GroupColumnConfig {
  /** 分组列标题 */
  title: string;
  /** 子列配置数组 */
  children: DataTableColumn<StockTableInfoVO>[];
}

// ==================== 公共渲染函数 ====================

/**
 * 渲染财务数据（带颜色）
 */
export const renderFinancialValue = (value: any): any => {
  if (value === null || value === undefined) return '-';

  return createElement(
    'span',
    {
      style: { color: getFinancialColor(value) },
    },
    { default: () => value }
  );
};

/**
 * 渲染评级数据（带颜色）
 */
export const renderLevelValue = (value: string): any => {
  if (!value) return '-';

  return createElement(
    'span',
    {
      style: { color: setLevelColor(value) },
    },
    { default: () => value }
  );
};

/**
 * 渲染比较数据（值 + 中位数）
 */
export const renderComparisonValue = (value: any, medianValue: any): any => {
  if (!value) return '-';

  return [
    createElement(
      'span',
      {
        style: { color: getComparisonColor(value, medianValue) },
      },
      { default: () => value }
    ),
    `(${medianValue || '-'})`,
  ];
};

/**
 * 渲染按钮
 */
export const renderButton = (
  text: string | null,
  onClick: () => void,
  type: 'info' | 'primary' | 'warning' | 'error' = 'info'
): any => {
  return createElement(
    NButton,
    {
      onClick,
      type,
      strong: true,
      tertiary: true,
      size: 'small',
    },
    { default: () => text }
  );
};

/**
 * 渲染带帮助提示的标题
 */
export const renderTitleWithHelp = (title: string, helpText: string): any => {
  return [
    title,
    createElement(
      NPopover,
      {},
      {
        trigger: () =>
          createElement(NIcon, {}, { default: () => createElement(HelpCircleOutline) }),
        default: () => helpText,
      }
    ),
  ];
};

// ==================== 列工厂函数 ====================

/**
 * 创建基础列
 */
export const createBasicColumn = <T = any>(
  title: string,
  key: string,
  options: {
    width?: string;
    align?: 'left' | 'center' | 'right';
    sorter?: boolean;
    fixed?: 'left' | 'right';
    ellipsis?: boolean | { tooltip: boolean };
    render?: (row: T, index: number) => any;
  } = {}
): DataTableColumn<T> => ({
  title,
  key,
  align: options.align || 'center',
  width: options.width || '110px',
  sorter: options.sorter || false,
  fixed: options.fixed,
  ellipsis: options.ellipsis,
  ...(options.render && { render: options.render }),
});

/**
 * 创建财务数据列（带颜色渲染）
 */
export const createFinancialColumn = (
  config: FinancialColumnConfig
): DataTableColumn<StockTableInfoVO> => ({
  title: config.title,
  key: config.key,
  align: 'center',
  width: config.width || '140px',
  sorter: config.sorter !== false,
  render: (row) => renderFinancialValue(row[config.key]),
});

/**
 * 创建评级列（带颜色渲染）
 * @description
 * 评级列配置：
 * - title: 列标题
 * - key: 数据字段名
 * - width: 列宽度，默认 '120px'
 * - sorter: 是否启用排序功能，默认 true
 */
export const createLevelColumn = <T = any>(
  title: string,
  key: string,
  options: { width?: string; sorter?: boolean } = {}
): DataTableColumn<T> => ({
  title,
  key,
  align: 'center',
  width: options.width || '120px',
  sorter: options.sorter === undefined || options.sorter === null || options.sorter,
  render: (row) => renderLevelValue(row[key]),
});

/**
 * 创建比较数据列（值 + 中位数对比）
 */
export const createComparisonColumn = (
  config: ComparisonColumnConfig
): DataTableColumn<StockTableInfoVO> => ({
  title: () => createElement('span', {}, [config.title, createElement('br'), '(行业中位数)']),
  key: config.key,
  align: 'center',
  width: config.width || '150px',
  sorter: config.sorter !== false,
  render: (row) => renderComparisonValue(row[config.key], row[config.medianKey]),
});

/**
 * 创建按钮列
 * @param config 列配置
 * @returns 按钮列
 * @description
 * 按钮列配置：
 * - title: 列标题
 * - key: 数据字段名
 * - width: 列宽度，默认 '110px'
 * - fixed: 固定位置：左侧或右侧
 * - onClick: 按钮点击事件处理函数
 * - buttonType: 按钮类型：信息、主要、警告、错误，默认 'info'
 */
export const createButtonColumn = <T = any>(config: ButtonColumnConfig<T>): DataTableColumn<T> => ({
  title: config.title,
  key: config.key,
  align: 'center',
  width: config.width || '110px',
  fixed: config.fixed,
  render: (row) =>
    renderButton(row[config.key], () => config.onClick(row), config.buttonType || 'info'),
});

/**
 * 创建证券代码列（特殊按钮列）
 */
export const createSecurityCodeColumn = (): DataTableColumn<StockTableInfoVO> => ({
  title: '证券代码',
  key: 'secCode',
  align: 'center',
  width: '110px',
  fixed: 'left',
  render: (row) =>
    renderButton(row.secCode, () => openStockArchives(row.secCode, row.stockName, 1), 'info'),
});

/**
 * 创建证券名称列（特殊按钮列）
 */
export const createSecurityNameColumn = (): DataTableColumn<StockTableInfoVO> => ({
  title: '证券名称',
  key: 'stockName',
  align: 'center',
  width: '110px',
  fixed: 'left',
  render: (row) =>
    renderButton(row.stockName, () => openStockArchives(row.stockId, row.stockName, 1), 'info'),
});

/**
 * 创建分组列
 */
export const createGroupColumn = (
  config: GroupColumnConfig
): DataTableColumn<StockTableInfoVO> => ({
  title: config.title,
  key: '',
  align: 'center',
  children: config.children as any, // 类型断言解决 Naive UI 类型不匹配问题
});

// ==================== 预定义表格列配置 ====================

/**
 * 创建标准股票表格列配置
 */
export const createStockTableColumns = (
  type: 'standard' | 'strategy' | 'pool' | 'custom' = 'standard',
  options: {
    onShowDetail?: (row: StockTableInfoVO) => void;
  } = {}
): DataTableColumn<StockTableInfoVO>[] => {
  const { onShowDetail } = options;

  // 基础列配置
  const baseColumns = [
    createSecurityCodeColumn(),
    createSecurityNameColumn(),
    createBasicColumn('行业', 'industryName'),
    createBasicColumn('二级行业', 'industryNameTwo'),
  ];

  // 评级列配置
  const ratingColumns = [createLevelColumn('系统评级', 'level')];

  // 同业分类区间列
  const peerColumns = onShowDetail ? [createPeerRangeColumn(onShowDetail)] : [];

  // 市值分组列
  const marketValueColumns = [createMarketValueGroupColumn()];

  // 基础财务数据列
  const basicFinancialColumns = [
    createFinancialColumn({ title: '股价(元)', key: 'closes', width: '110px' }),
    createFinancialColumn({ title: '财务评分', key: 'financialScore', width: '120px' }),
    createFinancialColumn({ title: '综合评分', key: 'totalScore', width: '120px' }),
  ];

  // 比较数据列
  const comparisonColumns = [
    createComparisonColumn({ title: '市净率', key: 'pb', medianKey: 'midPb' }),
    createComparisonColumn({ title: '市盈率', key: 'pe', medianKey: 'midPe' }),
  ];

  // 详细财务指标列
  const detailedFinancialColumns = [
    createFinancialColumn({ title: '每股净资产(元/股)', key: 'netAssetValuePerShare' }),
    createFinancialColumn({ title: '扣非净利润(万元)', key: 'kfnetprofit' }),
    createGrossMarginGroupColumn(),
    createNetProfitGroupColumn(),
    createIncomeGroupColumn(),
    createFinancialColumn({
      title: '3年营收负债增速差',
      key: 'incomeDebtSubThreeYear',
      width: '150px',
    }),
    createFinancialColumn({
      title: '5年营收负债增速差',
      key: 'incomeDebtSubFiveYear',
      width: '150px',
    }),
    createFinancialColumn({ title: '5年亏损次数', key: 'losses' }),
  ];

  // 标签相关列
  const labelColumns = [
    createBasicColumn('标签数量', 'count', { sorter: true }),
    createFinancialColumn({ title: '高风险标签数量', key: 'highRiskLabelCount' }),
    createBasicColumn('标签内容', 'labels', { ellipsis: { tooltip: true }, width: '250px' }),
  ];

  // 根据类型返回不同的列配置
  switch (type) {
    case 'standard':
      return [
        ...baseColumns,
        ...ratingColumns,
        ...peerColumns,
        ...marketValueColumns,
        ...basicFinancialColumns,
        ...comparisonColumns,
        ...detailedFinancialColumns,
        ...labelColumns,
      ];

    case 'strategy':
      return [
        ...baseColumns,
        ...ratingColumns,
        ...peerColumns,
        ...marketValueColumns,
        ...basicFinancialColumns,
        ...comparisonColumns,
        ...detailedFinancialColumns,
        ...labelColumns,
      ];

    case 'pool':
      return [
        ...baseColumns,
        createLevelColumn('策略评级', 'afterLevel'),
        createLevelColumn('塔金评级', 'level'),
        ...peerColumns,
        createFinancialColumn({ title: '总市值(亿元)', key: 'totalAmount', width: '160px' }),
        ...basicFinancialColumns,
        ...comparisonColumns,
        ...detailedFinancialColumns,
        ...labelColumns,
      ];

    default:
      return baseColumns;
  }
};

// ==================== 专门的财务指标列工厂 ====================

/**
 * 创建市值分组列
 */
export const createMarketValueGroupColumn = (): DataTableColumn<StockTableInfoVO> =>
  createGroupColumn({
    title: '市值(亿元)',
    children: [
      createFinancialColumn({ title: '总市值', key: 'totalAmount', width: '120px' }),
      createFinancialColumn({ title: '流通市值', key: 'circulateTotalAmount', width: '120px' }),
    ],
  });

/**
 * 创建毛利率分组列
 */
export const createGrossMarginGroupColumn = (): DataTableColumn<StockTableInfoVO> =>
  createGroupColumn({
    title: '毛利率',
    children: [
      createFinancialColumn({
        title: '3年增速(%)',
        key: 'grossMarginTrendThreeYear',
        width: '140px',
      }),
      createFinancialColumn({
        title: '5年增速(%)',
        key: 'grossMarginTrendFiveYear',
        width: '140px',
      }),
      createFinancialColumn({ title: '3年增速-5年增速', key: 'grossMarginSub', width: '150px' }),
    ],
  });

/**
 * 创建扣非净利润分组列
 */
export const createNetProfitGroupColumn = (): DataTableColumn<StockTableInfoVO> =>
  createGroupColumn({
    title: '扣非净利润',
    children: [
      createFinancialColumn({
        title: '3年增速(%)',
        key: 'kfnetprofittrendthreeyear',
        width: '140px',
      }),
      createFinancialColumn({
        title: '5年增速(%)',
        key: 'kfnetprofittrendfiveyear',
        width: '140px',
      }),
      createFinancialColumn({ title: '3年增速-5年增速', key: 'kfnetprofitsub', width: '150px' }),
    ],
  });

/**
 * 创建营业收入分组列
 */
export const createIncomeGroupColumn = (): DataTableColumn<StockTableInfoVO> =>
  createGroupColumn({
    title: '营业收入',
    children: [
      createFinancialColumn({ title: '3年增速(%)', key: 'incomeTrendThreeYear', width: '140px' }),
      createFinancialColumn({ title: '5年增速(%)', key: 'incomeTrendFiveYear', width: '140px' }),
      createFinancialColumn({ title: '3年增速-5年增速', key: 'incomeSub', width: '150px' }),
    ],
  });

/**
 * 创建同业分类区间列（带帮助提示的按钮列）
 */
export const createPeerRangeColumn = (
  onDetailClick: (row: StockTableInfoVO) => void
): DataTableColumn<StockTableInfoVO> => ({
  title: () => renderTitleWithHelp('同业分类区间', '点击可查看同业详情'),
  key: 'levelRange',
  align: 'center',
  width: '150px',
  render: (row) => renderButton(row.levelRange, () => onDetailClick(row), 'info'),
});
