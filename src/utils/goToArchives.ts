// 跳转档案页面
import Router from '@/router';
import { encrypt } from '@/utils/crypto/cryptoUtils';
import { vagueQuerySecCodes } from '@/api/sec/secInfoApi';
import { SecType } from '@/enums/secEnum';

//跳转之前判断证券是基金，股票，转债还是其他
const goToArchives = async (stockId: string | null, stockName: string | null, pageType) => {
  const { code, data, msg } = await vagueQuerySecCodes(
    { current: 1, size: 10 },
    stockId ? stockId : '',
    null
  );
  if (code === 200) {
    if (data.records.length === 0) {
      alert('暂无该证券信息');
      return;
    }

    const { secType, secCategory, secName, secCode } = data.records[0];
    if (secType === SecType.STOCK || !secType) {
      openSecurityArchives(secCode.split('.')[0], secName, pageType);
    } else if (secType === SecType.FUND) {
      openFundFile(secCode.split('.')[0], secName, 1);
    } else if (secType === SecType.BOND) {
      openConvertibleBondFile(
        secCode.split('.')[0],
        secName,
        secCategory == '可转债' ? 'kzz' : pageType
      );
    }
  } else {
    alert(msg);
  }
};

export function openStockArchives(stockId: string | null, stockName: string | null, pageType) {
  //如果不是华龙，则跳转到证券判断页面
  // if (notHualong) {
  goToArchives(stockId, stockName, pageType).then((r) => {});
  // } else {
  //   //跳转到证券页面
  //   openSecurityArchives(stockId, stockName, pageType);
  // }
}

/**
 * 打开证券档案页面
 *
 * @description 在新窗口中打开指定证券的档案详情页面，用于查看证券的基本信息、
 * 财务数据、风险评级、交易历史等详细档案信息。页面会以加密方式传递证券ID和名称参数。
 *
 * @param {string} stockId - 证券ID，格式通常为"代码.交易所"（如"000001.SZ"），函数会自动提取代码部分
 * @param {string} stockName - 证券名称，用于页面显示和标识
 * @param {number} pageType - 页面类型标识，用于区分不同的档案展示模式或来源页面
 *
 * @returns {void} 无返回值，直接打开新窗口
 *
 * @example
 * ```typescript
 * // 打开平安银行的证券档案
 * openSecurityArchives('000001.SZ', '平安银行', 1);
 * ```
 *
 * @since 1.0.0
 */
export function openSecurityArchives(stockId: string, stockName: string | null, pageType) {
  const Logistics = Router.resolve({
    path: '/Archives',
    query: {
      Amount1: encrypt(stockId.split('.')[0]),
      Amount2: encrypt(stockName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}

/**
 * 打开事件追踪页面
 *
 * @description 在新窗口中打开指定证券的事件追踪页面，用于查看该证券相关的重要事件、
 * 公告信息、风险事件、监管动态等时间线信息。帮助用户了解证券的历史事件和风险变化轨迹。
 *
 * @param {string} stockId - 证券ID，格式通常为"代码.交易所"（如"000001.SZ"），函数会自动提取代码部分
 * @param {string} stockName - 证券名称，用于页面显示和事件关联
 * @param {number} pageType - 页面类型标识，用于区分不同的事件追踪模式或来源页面
 *
 * @returns {void} 无返回值，直接打开新窗口
 *
 * @example
 * ```typescript
 * // 打开平安银行的事件追踪页面
 * openEventTrace('000001.SZ', '平安银行', 1);
 * ```
 *
 * @since 1.0.0
 */
export function openEventTrace(stockId: string, stockName: string | null, pageType) {
  const Logistics = Router.resolve({
    path: '/EventTrace',
    query: {
      Amount1: encrypt(stockId.split('.')[0]),
      Amount2: encrypt(stockName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
/**
 * 打开客户档案页面
 *
 * @description 在新窗口中打开指定客户的档案详情页面，用于查看客户的基本信息、
 * 账户状态、交易记录、风险评估、资产配置等客户相关的完整档案信息。
 * 主要用于客户关系管理和风险监控。
 *
 * @param custId
 * @param custName
 * @param {number} pageType - 页面类型标识，用于区分不同的客户档案展示模式或来源页面
 *
 * @returns {void} 无返回值，直接打开新窗口
 *
 * @example
 * ```typescript
 * // 打开客户档案页面
 * openClientFile('C123456789', '张三', 1);
 * ```
 *
 * @since 1.0.0
 */
export function openClientFile(custId, custName, pageType) {
  const Logistics = Router.resolve({
    path: '/clientFile',
    query: {
      Amount1: encrypt(custId),
      Amount2: encrypt(custName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
/**
 * 打开基金档案页面
 *
 * @description 在新窗口中打开指定基金的档案详情页面，用于查看基金的基本信息、
 * 投资组合、历史业绩、风险指标、基金经理信息、费率结构等基金相关的详细档案。
 * 主要用于基金产品分析和投资决策支持。
 *
 * @param {string} fundId - 基金ID，基金的唯一标识代码（如基金代码）
 * @param {string} fundName - 基金名称，用于页面显示和基金识别
 * @param {number} pageType - 页面类型标识，用于区分不同的基金档案展示模式或来源页面
 *
 * @returns {void} 无返回值，直接打开新窗口
 *
 * @example
 * ```typescript
 * // 打开某基金的档案页面
 * openFundFile('000001', '华夏成长混合', 1);
 * ```
 *
 * @since 1.0.0
 */
export function openFundFile(id, name, pageType) {
  const Logistics = Router.resolve({
    path: '/fundFile',
    query: {
      Amount1: encrypt(id.split('.')[0]),
      Amount2: encrypt(name),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
/**
 * 打开可转债档案页面
 *
 * @description 在新窗口中打开指定可转债的档案详情页面，用于查看可转债的基本信息、
 * 转股条件、赎回条款、回售条款、债券评级、正股信息、转股价值分析等可转债特有的详细档案。
 * 主要用于可转债投资分析和风险评估。
 *
 * @param {string} bondId - 可转债ID，可转债的唯一标识代码
 * @param {string} bondName - 可转债名称，用于页面显示和债券识别
 * @param {number} pageType - 页面类型标识，用于区分不同的可转债档案展示模式或来源页面
 *
 * @returns {void} 无返回值，直接打开新窗口
 *
 * @example
 * ```typescript
 * // 打开某可转债的档案页面
 * openConvertibleBondFile('128001', '平银转债', 1);
 * ```
 *
 * @since 1.0.0
 */
export function openConvertibleBondFile(id, name, pageType) {
  const Logistics = Router.resolve({
    path: '/ConvertibleBondFile',
    query: {
      Amount1: encrypt(id.split('.')[0]),
      Amount2: encrypt(name),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
