// 跳转档案页面
import Router from '@/router';
// @ts-ignore
import { encrypt } from '@/utils/crypto/encode.ts';
import { vagueQuerySecCodes } from '@/api/sec/secInfoApi';
import { SecType } from '@/enums/secEnum';

//跳转之前判断证券是基金，股票，转债还是其他
const goToArchives = async (stockId: string | null, stockName: string | null, pageType) => {
  const { code, data, msg } = await vagueQuerySecCodes(
    { current: 1, size: 10 },
    stockId ? stockId : '',
    null
  );
  if (code === 200) {
    if (!data.records || data.records.length === 0) {
      alert('暂无该证券信息');
      return;
    }

    const { secType, secCategory, secName, secCode } = data.records[0];
    if (secType === SecType.STOCK || !secType) {
      openSecurityArchives(secCode.split('.')[0], secName, pageType);
    } else if (secType === SecType.FUND) {
      openFundFile(secCode.split('.')[0], secName, 1);
    } else if (secType === SecType.BOND) {
      openConvertibleBondFile(
        secCode.split('.')[0],
        secName,
        secCategory == '可转债' ? 'kzz' : pageType
      );
    }
  } else {
    alert(msg);
  }
};

export function openStockArchives(stockId: string | null, stockName: string | null, pageType) {
  //如果不是华龙，则跳转到证券判断页面
  // if (notHualong) {
  goToArchives(stockId, stockName, pageType).then((r) => {});
  // } else {
  //   //跳转到证券页面
  //   openSecurityArchives(stockId, stockName, pageType);
  // }
}

export function openSecurityArchives(stockId, stockName, pageType) {
  const Logistics = Router.resolve({
    path: '/Archives',
    query: {
      Amount1: encrypt(stockId.split('.')[0]),
      Amount2: encrypt(stockName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
export function openEventTrace(stockId, stockName, pageType) {
  const Logistics = Router.resolve({
    path: '/EventTrace',
    query: {
      Amount1: encrypt(stockId.split('.')[0]),
      Amount2: encrypt(stockName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
export function openClientFile(custId, custName, pageType) {
  const Logistics = Router.resolve({
    path: '/clientFile',
    query: {
      Amount1: encrypt(custId),
      Amount2: encrypt(custName),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
export function openFundFile(id, name, pageType) {
  const Logistics = Router.resolve({
    path: '/fundFile',
    query: {
      Amount1: encrypt(id.split('.')[0]),
      Amount2: encrypt(name),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
export function openConvertibleBondFile(id, name, pageType) {
  const Logistics = Router.resolve({
    path: '/ConvertibleBondFile',
    query: {
      Amount1: encrypt(id.split('.')[0]),
      Amount2: encrypt(name),
      Amount3: pageType,
    },
  });
  window.open(Logistics.href, '_blank', 'toolbar=no, width=1900, height=1000');
}
