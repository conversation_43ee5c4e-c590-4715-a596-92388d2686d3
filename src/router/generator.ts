import { constantRouterIcon } from './icons';
import { Router, RouteRecordRaw } from 'vue-router';
import { Layout, ParentLayout, WORKPLACE_MENU_CONFIG } from '@/router/constant';
import type { AppRouteRecordRaw, BackendRouteItem } from '@/router/types';
import { ErrorPageRoute } from '@/router/base';
import { ACCESS_TOKEN, CURRENT_ROUTES, CURRENT_USER_INFO } from '@/store/mutation-types';
import { storage } from '@/utils/common/storage';
import { useUserStore } from '@/store/modules/user';
import { normalizeComponentPath } from '@/views/hualong/config';
import { h } from 'vue';
import { NTag } from 'naive-ui';
import { getAuditTypeByRoute, isDongBei, parseIcon } from '@/utils';
import { ResultEnum } from '@/enums/base/httpEnum';
import { BasicResponseModel } from '@/models/common/baseResponse';
import { TypeCountVO } from '@/models/common/utilModels';
import { fetchRoutes } from '@/api/system/MenuApi';
import { upperFirst } from 'lodash-es';
import { ensureLeadingSlash } from '@/utils/common/format';

// 常量定义
const RELOAD_DELAY_MS = 1000; // 页面重载延迟时间（毫秒）
const MAX_BADGE_COUNT = 100; // 徽章显示的最大数量
const AUDIT_BADGE_STYLE = {
  color: '#bb1111',
  textColor: '#ffffff',
  borderColor: '#bb1111',
} as const;

const Iframe = () => import('@/views/iframe/index.vue');
const LayoutMap = new Map<string, () => Promise<typeof import('*.vue')>>();
LayoutMap.set('Layout', Layout);
LayoutMap.set('IFRAME', Iframe);

/**
 * 生成审核模块的待办任务徽章
 * @description 根据路由组件路径生成对应的审核任务数量徽章，仅在审核相关页面显示
 *
 * **功能特点：**
 * - 自动识别审核模块类型（担保品、标的证券、集中度、融资融券）
 * - 显示对应模块的待审核任务数量
 * - 支持"全部"类型的数量汇总
 * - 数量超过100时显示"99+"
 * - 非审核页面不显示徽章
 *
 * @param componentPath - 路由组件路径
 * @param waitCensorCountList - 待审核任务数量列表
 * @returns Vue渲染函数或空字符串
 *
 * @example
 * ```typescript
 * // 担保品审核页面
 * generateAuditTaskBadge('auditManage/collateralAudit', waitCensorCount)
 * // 返回: 红色徽章显示数量
 *
 * // 非审核页面
 * generateAuditTaskBadge('dashboard/workplace', waitCensorCount)
 * // 返回: 空字符串（不显示徽章）
 * ```
 * @category 路由工具
 * @see {@link getAuditTypeByRoute} 审核类型获取函数
 */
const generateAuditTaskBadge = (componentPath: string, waitCensorCountList: TypeCountVO[], waitCommitCounterCountList: TypeCountVO[] = []) => {
  // 获取标准化的组件路径
  const normalizedPath = normalizeComponentPath(componentPath);

  // 获取审核类型标识
  const auditType = getAuditTypeByRoute(normalizedPath);

  // 如果不是审核页面，不显示徽章
  if (!auditType) {
    return '';
  }

  // 计算待审核任务数量
  let pendingTaskCount: number;

  // 定义需要汇总的类型名称
  const summaryTypeNames = ['collateral', 'underlying', 'category'];

  if (auditType === 'all') {
    // "全部"类型：汇总指定类型的审核任务数量
    pendingTaskCount = waitCensorCountList
      .filter(auditItem => summaryTypeNames.includes(auditItem.typeName))
      .reduce((total, auditItem) => {
        return total + (auditItem.count || 0);
      }, 0);
  } else if (auditType === 'commit-counter-all') {
    // "待提交柜台全部"类型：汇总指定类型的待提交柜台数量
    pendingTaskCount = waitCommitCounterCountList
      .filter(auditItem => summaryTypeNames.includes(auditItem.typeName))
      .reduce((total, auditItem) => {
        return total + (auditItem.count || 0);
      }, 0);
  } else {
    // 特定类型：查找对应的审核任务数量
    const matchingAuditItem = waitCensorCountList.find(
      (auditItem) => auditItem.typeName === auditType
    );
    pendingTaskCount = matchingAuditItem?.count || 0;
  }

  // 格式化显示数量（超过100显示99+）
  const displayCount = pendingTaskCount > MAX_BADGE_COUNT ? '99+' : pendingTaskCount;

  // 生成徽章组件
  return h(
    NTag,
    {
      color: AUDIT_BADGE_STYLE,
      round: true,
      size: 'small',
    },
    {
      default: () => displayCount,
    }
  );
};

/**
 * 将后端路由数据转换为前端 Vue Router 可用的路由配置
 * @description 递归函数，用于处理嵌套的菜单结构，将后端路由数据转换为前端可用的路由配置
 *
 * **核心功能：**
 * - 🔄 数据格式转换：BackendRouteItem[] → AppRouteRecordRaw[]
 * - 🛠️ 路径标准化处理（确保以 / 开头）
 * - 📁 组件路径规范化和动态导入配置
 * - 🔤 路由名称首字母大写处理
 * - 🏷️ 审核模块徽章生成和权限控制
 * - 👁️ 可见性和隐藏状态处理
 * - 🔄 递归处理嵌套子路由结构
 *
 * **数据转换流程：**
 * 1. 遍历后端路由数据数组
 * 2. 为每个路由项生成标准化的前端配置
 * 3. 处理路由元信息、权限和缓存策略
 * 4. 生成审核模块的动态徽章组件
 * 5. 递归处理子路由并建立层级关系
 * 6. 返回符合 Vue Router 规范的路由配置
 *
 * **使用场景：**
 * - 动态路由生成：根据用户权限生成可访问路由
 * - 菜单渲染：为侧边栏菜单提供数据源
 * - 权限控制：结合用户权限过滤路由访问
 *
 * @param routerMap - 后端返回的路由数据数组
 * @returns 转换后的前端路由配置数组
 *
 * @example
 * ```typescript
 * const backendRoutes: BackendRouteItem[] = [
 *   {
 *     path: 'dashboard',
 *     name: 'dashboard',
 *     component: 'dashboard/index',
 *     meta: { title: '仪表板', icon: 'DashboardOutlined' },
 *     keepAlive: 0
 *   }
 * ];
 *
 * const frontendRoutes = transformBackendRoutesToFrontend(backendRoutes);
 * // 返回: AppRouteRecordRaw[]
 * ```
 *
 */
// 内部转换函数，不添加工作台菜单
const transformBackendRoutesToFrontendInternal = (
  routerMap: BackendRouteItem[]
): AppRouteRecordRaw[] => {
  const userStore = useUserStore();

  // 将后端返回的路由数据转换为前端可用的路由配置对象
  return routerMap.map((backendRouteItem: BackendRouteItem): AppRouteRecordRaw => {
    const currentRoute: AppRouteRecordRaw = {
      // 路由地址 动态拼接生成如 /dashboard/workplace
      path: ensureLeadingSlash(backendRouteItem.path),
      // 路由名称首字母大写
      name: upperFirst(backendRouteItem.name) ?? '',
      // 该路由对应页面的 组件
      component: `${normalizeComponentPath(backendRouteItem.component)}`,
      // 重定向配置：仅当后端返回重定向地址时才设置
      ...(backendRouteItem.redirect && { redirect: backendRouteItem.redirect }),
      // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
      meta: {
        ...backendRouteItem.meta,
        keepAlive: Boolean(backendRouteItem.keepAlive === 0),
        title: backendRouteItem.meta.title,
        // 生成审核模块的待办任务徽章
        extra: () =>
          generateAuditTaskBadge(backendRouteItem.component, userStore.getWaitCensorCount, userStore.getWaitCommitCounterCount),
        hidden: isDongBei(backendRouteItem.meta.title)
          ? true
          : userStore.getUserInfo.password != 'tarkin88888888'
          ? backendRouteItem.hidden ||
            (backendRouteItem.specialHidden == 1 && userStore.getIfSystemUser)
          : backendRouteItem.hidden,
        id: backendRouteItem.menuId,
        label: backendRouteItem.meta.title,
        disabled:
          backendRouteItem.children && backendRouteItem.children.length == 0
            ? backendRouteItem.enableStatus != 0
            : false,
        icon: backendRouteItem.meta.icon
          ? constantRouterIcon[backendRouteItem.meta.icon] || parseIcon(backendRouteItem.meta.icon)
          : null,
        // 后端接口暂时不返回权限配置，null 表示无权限限制
        permissions: backendRouteItem.meta.permissions || null,
      },
    };

    // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
    currentRoute.path = currentRoute.path.replace('//', '/');

    // 是否有子菜单，并递归处理
    if (backendRouteItem.children && backendRouteItem.children.length > 0) {
      // 如果未定义重定向路径，默认重定向到第一个子路由
      if (!backendRouteItem.redirect && backendRouteItem.children[0]?.path) {
        currentRoute.redirect = `${backendRouteItem.path}/${backendRouteItem.children[0].path}`;
      }
      // 递归处理子菜单
      currentRoute.children = transformBackendRoutesToFrontendInternal(backendRouteItem.children);
    }

    return currentRoute;
  });
};

// 公共转换函数，添加工作台菜单
export const transformBackendRoutesToFrontend = (
  routerMap: BackendRouteItem[]
): AppRouteRecordRaw[] => {
  // 将工作台菜单配置添加到 routerMap 数组的最前面
  const finalRouterMap = [WORKPLACE_MENU_CONFIG, ...routerMap];

  // 使用内部函数进行转换
  return transformBackendRoutesToFrontendInternal(finalRouterMap);
};

/**
 * 动态生成菜单
 * @description 根据用户权限和后端配置动态生成路由菜单
 *
 * **主要功能：**
 * - 🔐 获取用户权限和路由数据
 * - 📋 构建默认菜单列表
 * - 🔄 调用路由生成和组件解析
 * - 📊 刷新审核任务统计
 *
 *
 * @returns {Promise<RouteRecordRaw[]>} 生成的路由配置（实际为 AppRouteRecordRaw[]）
 */
export const generateDynamicRoutes = async (): Promise<RouteRecordRaw[]> => {
  const fetchRoutesResult = await fetchRoutes();
  //获取审核未读数量
  await useUserStore().refreshWaitCensorCount();
  //获取待提交柜台数量
  await useUserStore().refreshWaitCommitCounterCount();

  // 根据API响应状态处理路由生成，工作台菜单已在 transformBackendRoutesToFrontend 中添加
  const generatedRoutes = handleRouteGenerationByStatus(fetchRoutesResult);

  // 类型转换：将 AppRouteRecordRaw[] 转换为 RouteRecordRaw[] 以保持向后兼容
  return generatedRoutes as unknown as RouteRecordRaw[];
};

/**
 * 组件模块映射表缓存
 * @description 缓存 Vite glob 扫描的组件文件映射，避免重复扫描
 */
let componentModulesCache: Record<string, () => Promise<Recordable>>;

/**
 * 解析路由组件引用
 * @description 将路由配置中的字符串组件路径转换为实际的 Vue 组件引用，实现组件的动态导入和懒加载
 *
 * **核心功能：**
 * - 🔄 组件路径解析：字符串路径 → Vue 组件引用
 * - 📁 动态组件导入：基于文件路径的懒加载
 * - 🏗️ 布局组件映射：预定义布局组件的快速匹配
 * - 🖼️ 内嵌框架处理：外部页面的 IFRAME 组件配置
 * - 🔄 递归子路由处理：深度遍历嵌套路由结构
 *
 * **处理流程：**
 * 1. 扫描并缓存 views 目录下的所有组件文件
 * 2. 遍历路由配置数组，逐个处理路由项
 * 3. 根据组件类型进行不同的处理策略
 * 4. 递归处理子路由，确保所有层级都完成解析
 *
 * **组件类型处理：**
 * - 内嵌框架：设置为 IFRAME 组件
 * - 布局组件：从预定义映射表中获取
 * - 页面组件：通过动态导入函数加载
 * - 父级路由：使用 ParentLayout 组件
 *
 * @param routeConfigs - 需要解析组件的路由配置数组
 * @returns void - 直接修改传入的路由配置对象
 *
 * @example
 * ```typescript
 * const routes: AppRouteRecordRaw[] = [
 *   {
 *     path: '/dashboard',
 *     component: 'dashboard/index', // 字符串路径
 *     children: [...]
 *   }
 * ];
 *
 * resolveRouteComponents(routes);
 * // 转换后：component 变为 () => import('@/views/dashboard/index.vue')
 * ```
 */
export const resolveRouteComponents = (routeConfigs: AppRouteRecordRaw[] | undefined): void => {
  // console.log(`🚀 [路由解析] 开始解析路由组件，共 ${routeConfigs?.length || 0} 个路由配置`);

  // 使用 Vite 的 import.meta.glob 功能扫描 views 目录
  // 创建一个包含所有 .vue 和 .tsx 文件的模块映射表
  componentModulesCache = componentModulesCache || import.meta.glob('../views/**/*.{vue,tsx}');

  if (!routeConfigs) {
    console.warn(`⚠️ [路由解析] 路由配置为空，跳过组件解析`);
    return;
  }

  // 遍历路由配置，根据组件路径动态导入对应的组件
  routeConfigs.forEach((routeItem) => {
    // 处理内嵌框架类型的路由
    if (!routeItem.component && routeItem.meta?.frameSrc) {
      routeItem.component = 'IFRAME';
    }

    const { component: componentPath, name: routeName } = routeItem;

    // 根据组件路径类型进行不同的处理
    if (componentPath) {
      // 检查是否为预定义的布局组件
      const predefinedLayoutComponent = LayoutMap.get(componentPath as string);
      if (predefinedLayoutComponent) {
        routeItem.component = predefinedLayoutComponent;
      } else {
        // 动态导入页面组件
        routeItem.component = findComponentImportFunction(
          componentModulesCache,
          componentPath as string
        );
      }
    } else if (routeName) {
      // 使用父级布局组件
      routeItem.component = ParentLayout;
    }

    // 递归处理子路由
    const { children: childRoutes } = routeItem;
    if (childRoutes && childRoutes.length > 0) {
      resolveRouteComponents(childRoutes);
    }
  });
};

/**
 * 标准化 Vite 组件文件路径
 * @description 将 Vite glob 扫描的文件路径转换为标准的组件路径格式
 *
 * @param viteFilePath - Vite 扫描的原始文件路径（如：'../views/dashboard/index.vue'）
 * @returns 标准化的组件路径（如：'dashboard/index'）
 *
 * @example
 * ```typescript
 * normalizeViteFilePath('../views/dashboard/workplace.vue')
 * // 返回: 'dashboard/workplace'
 * ```
 */
const normalizeViteFilePath = (viteFilePath: string): string => {
  // 移除 Vite 的 '../views' 前缀
  let normalizedPath = viteFilePath.replace('../views', '');

  // 移除文件扩展名（.vue 或 .tsx）
  const fileExtensionIndex = normalizedPath.lastIndexOf('.');
  normalizedPath = normalizedPath.substring(0, fileExtensionIndex);

  // 移除开头的斜杠，返回纯净的相对路径
  return normalizedPath.startsWith('/') ? normalizedPath.substring(1) : normalizedPath;
};

/**
 * 检查组件路径是否匹配
 * @description 比较标准化后的文件路径与目标组件路径是否匹配
 *
 * @param viteFilePath - Vite 扫描的文件路径
 * @param targetPath - 目标组件路径
 * @returns 是否匹配
 */
const isComponentPathMatch = (viteFilePath: string, targetPath: string): boolean => {
  const normalizedPath = normalizeViteFilePath(viteFilePath);
  return normalizedPath === targetPath;
};

/**
 * 查找并获取组件导入函数
 * @description 根据组件路径字符串，从 Vite 扫描的组件模块映射表中查找对应的组件文件，并返回其动态导入函数
 *
 * **核心功能：**
 * - 🔍 路径匹配：将组件路径与 Vite glob 扫描结果进行精确匹配
 * - 🛠️ 路径标准化：处理 Vite 路径格式，转换为标准相对路径
 *
 * **路径转换示例：**
 * ```
 * 输入路径: 'dashboard/workplace'
 * Vite 扫描: '../views/dashboard/workplace.vue'
 * 标准化后: '/dashboard/workplace'
 * 匹配结果: () => import('@/views/dashboard/workplace.vue')
 * ```
 *
 * **错误处理：**
 * - 无匹配文件：返回 undefined
 * - 多个匹配文件：输出警告并返回 undefined
 *
 * @param componentModulesMap - Vite glob 扫描的组件模块映射表
 * @param targetComponentPath - 目标组件的相对路径（相对于 views 目录）
 * @returns 组件的动态导入函数，如果未找到或有冲突则返回 undefined
 *
 * @example
 * ```typescript
 * const modules = import.meta.glob('../views/**\/*.{vue,tsx}');
 * const importFn = findComponentImportFunction(modules, 'dashboard/index');
 * // 返回: () => import('@/views/dashboard/index.vue')
 * ```
 */
export const findComponentImportFunction = (
  componentModulesMap: Record<string, () => Promise<Recordable>>,
  targetComponentPath: string
) => {
  // 获取项目中所有组件模块的文件路径键名
  const allModuleFilePaths = Object.keys(componentModulesMap);

  // 使用拆分的函数进行路径匹配
  const matchedComponentFiles = allModuleFilePaths.filter((viteFilePath) =>
    isComponentPathMatch(viteFilePath, targetComponentPath)
  );

  // 情况1：找到唯一匹配的组件文件
  if (matchedComponentFiles?.length === 1) {
    const uniqueMatchedFile = matchedComponentFiles[0];

    return componentModulesMap[uniqueMatchedFile];
  }

  // 情况2：发现多个同名组件文件冲突
  if (matchedComponentFiles?.length > 1) {
    console.error(`❌ [冲突错误] 检测到路径 "${targetComponentPath}" 对应多个组件文件:`);
    matchedComponentFiles.forEach((file, index) => {
      console.error(`   ${index + 1}. ${file}`);
    });
    console.warn(
      `💡 [解决建议] 请不要在 views 目录的同一层级下创建同名的 .vue 和 .tsx 文件，这会导致动态导入失败`
    );
    return undefined;
  }

  // 情况3：未找到匹配的组件文件
  // console.warn(`⚠️ [未找到] 未找到路径 "${targetComponentPath}" 对应的组件文件`);

  return undefined;
};

/**
 * 清理用户状态和本地存储
 * @description 当API调用失败或用户认证失效时，清理所有用户相关的状态和存储数据
 */
const clearUserStateAndStorage = (): void => {
  const userStore = useUserStore();

  // 清理本地存储
  localStorage.clear();

  // 清理用户状态
  userStore.setToken('');
  userStore.setUserRoutes([]);
  userStore.setPermissions([]);
  userStore.setUserInfo(null);

  // 清理特定的存储项
  storage.remove(ACCESS_TOKEN);
  storage.remove(CURRENT_USER_INFO);
  storage.remove(CURRENT_ROUTES);
};

/**
 * 生成并导入路由配置
 * @description 将后端路由数据转换为前端路由配置，并解析组件引用
 *
 * **处理流程：**
 * 1. 调用 transformBackendRoutesToFrontend 转换路由数据格式
 * 2. 调用 resolveRouteComponents 解析组件引用
 * 3. 返回完整的路由配置
 *
 * @param routeData - 后端路由数据数组
 * @returns 生成的前端路由配置数组
 *
 * @since 1.0.0
 * @version 1.4.0
 */
const generateAndImportRoutes = (routeData: BackendRouteItem[]): AppRouteRecordRaw[] => {
  const routeConfigs = transformBackendRoutesToFrontend(routeData);
  resolveRouteComponents(routeConfigs);
  return routeConfigs;
};

/**
 * 添加动态路由到路由表
 * @description 将生成的动态路由配置添加到Vue Router实例中，并确保404错误页面路由在最后添加
 *
 * **核心功能：**
 * - 🔄 批量添加动态路由到路由表
 * - 🛡️ 自动添加404错误页面作为兜底机制
 * - 📊 提供路由添加过程的详细日志
 * - ⚡ 确保正确的路由优先级顺序
 *
 * **执行顺序：**
 * 1. 遍历并添加所有动态路由（具体路径）
 * 2. 检查是否已存在404错误页面路由
 * 3. 如果不存在，则添加404错误页面路由（通配符路径）
 * 4. 输出添加统计信息
 *
 * **设计原理：**
 * - 具体路由优先：确保用户有权限的页面能正确匹配
 * - 通配符兜底：未匹配的路径显示友好的404页面
 * - 防重复添加：避免同名路由的重复注册
 *
 * @param routeConfigs - 已处理的动态路由配置数组
 * @param router - Vue Router实例
 * @returns void
 *
 */
export const addDynamicRoutesToRouter = (routeConfigs: RouteRecordRaw[], router: Router): void => {
  // 步骤1: 添加所有动态路由（具体路径优先）
  routeConfigs.forEach((routeConfig, index) => {
    router.addRoute(routeConfig as unknown as RouteRecordRaw);
  });

  // // 输出当前路由表状态
  // console.log(
  //   '📊 [路由表] 当前所有路由:',
  //   router.getRoutes().map((r: any) => ({ path: r.path, name: r.name }))
  // );

  // 步骤2: 检查并添加404错误页面路由（通配符路径，兜底机制）
  const hasErrorPage = router.getRoutes().some((route: any) => route.name === ErrorPageRoute.name);

  if (!hasErrorPage) {
    router.addRoute(ErrorPageRoute as unknown as RouteRecordRaw);
  } else {
    console.log(`ℹ️ [404路由] 错误页面路由已存在，跳过添加`);
  }
};

/**
 * 根据API响应状态处理路由生成
 * @param fetchRoutesResult - 获取路由数据的API响应结果
 * @returns AppRouteRecordRaw[] 生成的路由配置
 * @description 根据不同的API响应状态码执行相应的路由生成策略：
 * - 成功且有数据：合并默认菜单和动态菜单
 * - 成功但无数据：仅使用默认菜单
 * - 失败：清理用户状态并重载页面，使用默认菜单
 */
const handleRouteGenerationByStatus = (
  fetchRoutesResult: BasicResponseModel
): AppRouteRecordRaw[] => {
  // 情况1：API调用成功且返回了路由数据
  if (fetchRoutesResult.code === ResultEnum.SUCCESS && fetchRoutesResult.data) {
    // console.log('✅ 路由数据获取成功，生成完整路由配置');
    return generateAndImportRoutes([...fetchRoutesResult.data]);
  }

  // 情况2：API调用成功但没有返回路由数据（兼容处理）
  if (fetchRoutesResult.code === ResultEnum.SUCCESS) {
    console.log('⚠️ 路由数据为空，使用默认菜单配置');
    return generateAndImportRoutes([]);
  }

  // 情况3：API调用失败或返回错误状态码
  console.error('❌ 路由数据获取失败，执行用户状态清理和页面重载');

  // 清理用户状态和存储
  clearUserStateAndStorage();

  // 延迟重载页面，给用户一些反应时间
  setTimeout(() => {
    location.reload();
  }, RELOAD_DELAY_MS);

  // 返回基础路由配置作为降级方案
  return generateAndImportRoutes([]);
};
