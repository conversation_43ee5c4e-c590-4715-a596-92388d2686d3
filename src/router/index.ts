/**
 * @fileoverview Vue Router 路由配置文件
 * @description 系统路由的核心配置文件，负责创建路由实例、定义路由规则、配置路由守卫
 * 包含动态路由加载、权限路由和公共路由的统一管理
 */

import { App } from 'vue';
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { RedirectRoute } from '@/router/base';
import { PageEnum } from '@/enums/base/pageEnum';
import { createRouterGuards } from './guards';
import { Layout } from '@/router/constant';

import type { IModuleType } from './types';

// ==================== 动态路由模块加载 ====================

/**
 * 动态导入所有路由模块
 * @description 使用 Vite 的 glob 功能自动扫描并导入 modules 目录下的所有路由配置文件
 */
const modules = import.meta.glob<IModuleType>('./modules/**/*.ts', { eager: true });

/**
 * 将导入的路由模块转换为路由记录数组
 * @description 遍历所有模块，提取默认导出的路由配置，支持单个路由或路由数组
 */
const routeModuleList: RouteRecordRaw[] = Object.keys(modules).reduce((list, key) => {
  const mod = modules[key].default ?? {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  return [...list, ...modList];
}, []);

/**
 * 路由排序函数
 * @param {RouteRecordRaw} a - 路由记录A
 * @param {RouteRecordRaw} b - 路由记录B
 * @returns {number} 排序结果
 * @description 根据路由元信息中的 sort 字段进行升序排序，用于控制菜单显示顺序
 */
function sortRoute(a: RouteRecordRaw, b: RouteRecordRaw): number {
  const sortA = Number(a.meta?.sort ?? 0);
  const sortB = Number(b.meta?.sort ?? 0);
  return sortA - sortB;
}

// 对路由模块列表进行排序
routeModuleList.sort(sortRoute);

// ==================== 基础路由配置 ====================

/**
 * 根路由配置
 * @description 应用的根路径路由，自动重定向到系统首页
 */
export const RootRoute: RouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

/**
 * 登录页面路由配置
 * @description 用户登录页面，无需权限验证
 */
export const LoginRoute: RouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('@/views/login/index.vue'),
  meta: {
    title: '登录',
  },
};

/**
 * 修改密码页面路由配置
 * @description 用户修改密码页面，无需权限验证
 */
export const ChangePasswordRoute: RouteRecordRaw = {
  path: '/change-password',
  name: 'ChangePassword',
  component: () => import('@/views/change-password/index.vue'),
  meta: {
    title: '修改密码',
  },
};

// ==================== 档案管理相关路由 ====================

/**
 * 主体档案窗口路由配置
 * @description 证券主体档案查看页面
 */
export const subjectArchiveWindow: RouteRecordRaw = {
  path: '/Archives',
  name: 'Archives',
  component: () => import('@/views/securityFileWindow/subject.vue'),
  meta: {
    title: '档案',
  },
};

/**
 * 事件跟踪路由配置
 * @description 证券事件跟踪页面
 */
export const EventTrace: RouteRecordRaw = {
  path: '/EventTrace',
  name: 'EventTrace',
  component: () => import('@/views/securityFileWindow/eventTrace.vue'),
  meta: {
    title: '事件跟踪',
  },
};

/**
 * 客户档案路由配置
 * @description 客户档案管理页面
 */
export const clientFile: RouteRecordRaw = {
  path: '/clientFile',
  name: 'clientFile',
  component: () => import('@/views/securityFileWindow/clientFile.vue'),
  meta: {
    title: '客户档案',
  },
};

/**
 * 基金档案路由配置
 * @description 基金档案管理页面
 */
export const fundFile: RouteRecordRaw = {
  path: '/fundFile',
  name: 'fundFile',
  component: () => import('@/views/securityFileWindow/fundFile.vue'),
  meta: {
    title: '基金档案',
  },
};

/**
 * 转债档案路由配置
 * @description 可转换债券档案管理页面
 */
export const ConvertibleBondFile: RouteRecordRaw = {
  path: '/ConvertibleBondFile',
  name: 'ConvertibleBondFile',
  component: () => import('@/views/securityFileWindow/ConvertibleBondFile.vue'),
  meta: {
    title: '转债档案',
  },
};
// ==================== 华龙证券专用路由 ====================

/**
 * 日报简报路由配置
 * @description 华龙证券日报简报页面
 */
export const DailyBriefingPc: RouteRecordRaw = {
  path: '/DailyBriefingPc',
  name: 'DailyBriefingPc',
  component: () => import('@/views/hualong/DailyBriefingPc/index.vue'),
  meta: {
    title: '简报',
  },
};

/**
 * 跌破预警路由配置
 * @description 华龙证券跌破预警监控页面
 */
export const dropWarningPc: RouteRecordRaw = {
  path: '/dropWarningPc',
  name: 'dropWarningPc',
  component: () => import('@/views/hualong/dropWarningPc/index.vue'),
  meta: {
    title: '跌破预警',
  },
};

/**
 * 合约到期预警路由配置
 * @description 华龙证券合约到期预警页面
 */
export const ContractExpirationWarningPc: RouteRecordRaw = {
  path: '/ContractExpirationWarningPc',
  name: 'ContractExpirationWarningPc',
  component: () => import('@/views/hualong/ContractExpirationWarningPc/index.vue'),
  meta: {
    title: '合约到期预警',
  },
};

/**
 * 风险账户管理路由配置
 * @description 华龙证券风险账户管理页面
 */
export const RzrqRiskAccountPc: RouteRecordRaw = {
  path: '/RzrqRiskAccountPc',
  name: 'RzrqRiskAccountPc',
  component: () => import('@/views/hualong/RzrqRiskAccountPc/index.vue'),
  meta: {
    title: '风险账户管理',
  },
};

// ==================== 路由分类配置 ====================

/**
 * 异步路由配置
 * @description 需要权限验证的动态路由，根据用户权限动态加载
 * @type {RouteRecordRaw[]}
 */
export const asyncRoutes: RouteRecordRaw[] = [...routeModuleList];

/**
 * 常量路由配置
 * @description 无需权限验证的公共路由，所有用户都可以访问
 */
export const constantRouter: RouteRecordRaw[] = [
  LoginRoute,
  ChangePasswordRoute,
  RootRoute,
  fundFile,
  ConvertibleBondFile,
  RedirectRoute,
  clientFile,
  subjectArchiveWindow,
  EventTrace,
  DailyBriefingPc,
  dropWarningPc,
  ContractExpirationWarningPc,
  RzrqRiskAccountPc,
];

// ==================== 路由实例创建 ====================

/**
 * 创建路由实例
 * @description 使用 HTML5 History 模式创建路由实例，配置基础路由和滚动行为
 */
const router = createRouter({
  history: createWebHistory(),
  routes: constantRouter,
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// ==================== 路由安装函数 ====================

/**
 * 安装路由到 Vue 应用
 * @param {App} app - Vue 应用实例
 * @description 将路由实例安装到 Vue 应用中，并创建路由守卫
 */
export function setupRouter(app: App): void {
  app.use(router);
  // 创建路由守卫
  createRouterGuards(router);
}

export default router;
