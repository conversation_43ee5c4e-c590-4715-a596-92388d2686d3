/**
 * @file guards.ts
 * @description 路由守卫配置文件，负责处理路由权限验证、token检查、动态路由加载等功能
 * <AUTHOR> @version 1.0.0
 */
// Vue Router 相关
import type { RouteRecordRaw } from 'vue-router';
import { isNavigationFailure, Router } from 'vue-router';

// 项目内部模块
import { useUser } from '@/store/modules/user';
import { useAsyncRoute } from '@/store/modules/asyncRoute';
import { ACCESS_TOKEN, CURRENT_USER_INFO } from '@/store/mutation-types';
import { storage } from '@/utils/common/storage';
import { PageEnum } from '@/enums/base/pageEnum';
import { ErrorPageRoute } from '@/router/base';
import { addDynamicRoutesToRouter } from '@/router/generator';
import { TokenManager } from '@/utils/auth/tokenManager';
import { isTokenTimeoutCheckEnabled } from '@/utils/auth/tokenConfig';
import { FirstLoginStatus } from '@/enums/base/userEnum';

/**
 * 路由重定向数据接口
 */
interface RedirectData {
  /** 重定向路径 */
  path: string;
  /** 是否替换当前历史记录 */
  replace: boolean;
  /** 查询参数 */
  query?: Recordable<string>;
}

/** 登录页面路径 */
const LOGIN_PATH = PageEnum.BASE_LOGIN;

/** 修改密码页面路径 */
const CHANGE_PASSWORD_PATH = PageEnum.CHANGE_PASSWORD;

/** 无需重定向的白名单路径列表 */
const whitePathList = [LOGIN_PATH, CHANGE_PASSWORD_PATH];

/** 无需token验证的特殊页面路径列表 */
const publicPagePaths = [
  '/DailyBriefingPc',
  '/dropWarningPc',
  '/ContractExpirationWarningPc',
  '/RzrqRiskAccountPc',
];

/**
 * 检查路径是否在白名单中
 * @param path 要检查的路径
 * @returns 是否在白名单中
 */
function isWhiteListPath(path: string): boolean {
  return whitePathList.includes(path as PageEnum);
}

/**
 * 检查路径是否为公开页面（无需token验证）
 * @param path 要检查的路径
 * @returns 是否为公开页面
 */
function isPublicPage(path: string): boolean {
  return publicPagePaths.includes(path);
}

/**
 * 创建重定向数据对象
 * @param targetPath 目标路径 如：/login
 * @param currentPath 当前路径 如：/myDesk/ratingStrategyModels
 * @returns 重定向数据对象
 */
function createRedirectData(targetPath: string, currentPath?: string): RedirectData {
  const redirectData: RedirectData = {
    path: targetPath,
    replace: true,
  };

  // 将当前页面路径保存为查询参数
  if (currentPath) {
    redirectData.query = {
      redirect: currentPath,
    };
  }

  return redirectData;
}

/**
 * 处理路由名称冲突问题
 * @description 检查并解决父子路由名称冲突，确保路由名称的唯一性
 * @param routes 路由配置数组
 * @returns 处理后的路由配置数组
 */
function processRouteNames(routes: RouteRecordRaw[]): RouteRecordRaw[] {
  return routes.map((route) => {
    const processedRoute = { ...route };

    /** 如果有子路由，检查是否存在名称冲突 */
    if (processedRoute.children && processedRoute.children.length > 0) {
      processedRoute.children = processedRoute.children.map(
        (child: RouteRecordRaw, index: number) => {
          const processedChild = { ...child };

          /**
           * 如果子路由名称与父路由名称相同，添加后缀避免冲突
           * @description 使用 String() 确保 symbol 类型的路由名称能正确转换为字符串
           */
          if (processedChild.name === processedRoute.name) {
            const parentName = String(processedRoute.name || '');
            const suffix = index > 0 ? index : '';
            processedChild.name = `${parentName}Child${suffix}`;
          }

          return processedChild;
        }
      );
    }

    return processedRoute;
  });
}

/**
 * 创建路由守卫
 * @param router Vue Router 实例
 */
export function createRouterGuards(router: Router): void {
  const userStore = useUser();
  const asyncRouteStore = useAsyncRoute();

  // 全局前置守卫，在每次路由跳转之前执行
  router.beforeEach(async (to, from, next) => {
    // 显示加载状态
    const Loading = window['$loading'] || null;
    void (Loading && Loading.start());

    // 设置页面标题
    to.meta.title = (to.query?.secName ? to.query?.secName + '-' : '') + to.meta.title;

    // 白名单路径可以直接进入
    if (isWhiteListPath(to.path)) {
      next();
      return;
    }

    // 检查是否存在token
    const token = storage.get(ACCESS_TOKEN);
    if (!token) {
      // 检查是否为公开页面
      if (isPublicPage(to.path)) {
        next();
        return;
      }

      // 检查是否设置了忽略权限验证
      if (to.meta.ignoreAuth) {
        next();
        return;
      }

      // 重定向到登录页，并保存原始访问路径
      const redirectData = createRedirectData(LOGIN_PATH, to.path);
      next(redirectData);
      return;
    }

    // 检查token是否在本地已过期（可选的前端预检查，仅在启用时）
    if (isTokenTimeoutCheckEnabled() && TokenManager.isTokenExpiredLocally(token)) {
      TokenManager.handleTokenExpired();
      return;
    }

    // 检查是否首次登录需要修改密码
    const currentUserInfo = storage.get(CURRENT_USER_INFO);
    if (currentUserInfo && currentUserInfo.firstLogin === FirstLoginStatus.YES) {
      try {
        // 首次登录用户只能访问修改密码页面
        if (to.path !== CHANGE_PASSWORD_PATH) {
          next({
            path: CHANGE_PASSWORD_PATH,
            query: { forced: 'true', from: 'route-guard' },
          });
          return;
        }
      } catch (error) {
        console.error(error);
        // token无效，清除用户信息
        await userStore.logout();
        next({ path: LOGIN_PATH, query: { redirect: to.fullPath } });
        return;
      }
    }

    // 如果动态路由已添加，直接通过
    if (asyncRouteStore.getIsDynamicRouteAdded) {
      next();
      return;
    }

    // 获取用户信息并生成动态路由
    const userInfo = await userStore.getInfo();
    const routes = await asyncRouteStore.generateRoutes(userInfo);
    // 处理路由名称冲突问题
    const processedRoutes = processRouteNames(routes);

    // 统一添加动态路由到路由表（包含404错误页面）
    addDynamicRoutesToRouter(processedRoutes, router);
    // 处理重定向逻辑
    const redirectPath = (from.query.redirect || to.path) as string;
    const redirect = decodeURIComponent(redirectPath);
    const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };

    // 标记动态路由已添加
    asyncRouteStore.setDynamicRouteAdded(true);
    next(nextData);
    void (Loading && Loading.finish());
  });

  router.afterEach((to, _, failure) => {
    // 设置页面标题
    document.title = (to?.meta?.title as string) || document.title;

    // 处理导航失败
    if (isNavigationFailure(failure)) {
      console.warn('路由跳转失败:', failure);
    }

    /**
     * 处理组件缓存逻辑
     * @description 根据路由元信息动态管理Vue组件的keep-alive缓存状态
     *
     * 缓存策略说明：
     * 1. 当组件设置了 keepAlive: true 时，将其添加到缓存列表
     * 2. 当组件设置了 keepAlive: false 或为重定向页面时，从缓存列表中移除
     * 3. 缓存列表用于控制 <keep-alive> 组件的 include 属性
     *
     * 应用场景：
     * - 表单页面：保持用户输入状态，避免数据丢失
     * - 列表页面：保持滚动位置和筛选条件
     * - 详情页面：减少重复的API请求，提升用户体验
     */
    const asyncRouteStore = useAsyncRoute();

    /** 获取当前已缓存的组件名称列表 */
    const keepAliveComponents = [...asyncRouteStore.getKeepAliveComponents];

    /**
     * 获取当前路由对应的组件名称
     * @description 通过路由匹配记录查找与当前路由名称相同的组件名
     * 这个名称必须与Vue组件的name属性保持一致才能正确缓存
     */
    const currentComponentName: any = to.matched.find((item) => item.name === to.name)?.name;

    /**
     * 条件1：添加组件到缓存列表
     * @description 满足以下所有条件时，将组件添加到keep-alive缓存：
     * - currentComponentName 存在（确保组件有有效名称）
     * - 组件尚未在缓存列表中（避免重复添加）
     * - 路由元信息中设置了 keepAlive: true（明确标记需要缓存）
     */
    if (
      currentComponentName &&
      !keepAliveComponents.includes(currentComponentName) &&
      to.meta?.keepAlive
    ) {
      /** 将组件名称添加到缓存列表，Vue的keep-alive将根据此列表缓存组件实例 */
      keepAliveComponents.push(currentComponentName);
      console.log(`[KeepAlive] 添加组件到缓存: ${currentComponentName}`);
    } else if (!to.meta?.keepAlive || to.name === 'Redirect') {
      /**
       * 条件2：从缓存列表中移除组件
       * @description 满足以下任一条件时，将组件从keep-alive缓存中移除：
       * - 路由元信息中设置了 keepAlive: false 或未设置（不需要缓存）
       * - 当前路由名称为 'Redirect'（重定向页面不应被缓存）
       */
      /**
       * 查找组件在缓存列表中的索引位置
       * @description 使用findIndex确保精确匹配组件名称
       */
      const index = keepAliveComponents.findIndex((name) => name === currentComponentName);

      /** 如果组件存在于缓存列表中，则将其移除 */
      if (index !== -1) {
        keepAliveComponents.splice(index, 1);
        console.log(`[KeepAlive] 从缓存中移除组件: ${currentComponentName}`);
      }
    }

    /**
     * 更新store中的缓存组件列表
     * @description 将处理后的缓存列表同步到Pinia store，
     * 供全局的keep-alive组件使用，实现组件缓存的统一管理
     */
    asyncRouteStore.setKeepAliveComponents(keepAliveComponents);

    // 结束加载状态
    const Loading = window['$loading'] || null;
    void (Loading && Loading.finish());
  });

  router.onError((error) => {
    console.error('Router error:', error);
  });
}
