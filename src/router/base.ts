/**
 * @file: base.ts
 * @description: 基础路由配置模块 - 包含错误页面和重定向路由的定义
 * @date: 2024-12-18
 * @author: Tarkin Risk Management Platform Team
 */

import { ErrorPage, RedirectName, Layout } from '@/router/constant';
import { RouteRecordRaw } from 'vue-router';

/**
 * 错误页面路由配置
 * @description 用于处理404错误页面的路由配置，采用通配符匹配所有未定义的路径
 * @type {RouteRecordRaw}
 * @example
 * // 当用户访问不存在的路径时，会自动跳转到此错误页面
 * // 如：/non-existent-page -> ErrorPage组件
 */
export const ErrorPageRoute: RouteRecordRaw = {
  /**
   * 通配符路径配置 - 404错误页面兜底路由
   * @description 使用Vue Router通配符语法匹配所有未定义的路由路径，作为系统的兜底错误处理机制
   *
   * **路径语法说明：**
   * - `/:path` - 动态路径参数，参数名为 `path`
   * - `(.*)` - 正则表达式，匹配任意字符（包括斜杠）
   * - `*` - 重复修饰符，表示可以匹配零个或多个路径段
   *
   * **匹配示例：**
   * ```
   * ✅ /unknown-page        → 匹配，显示404页面
   * ✅ /admin/not-found     → 匹配，显示404页面
   * ✅ /a/b/c/d/e          → 匹配，显示404页面
   * ✅ /                   → 匹配（如果没有其他根路由）
   * ```
   *
   * **⚠️ 重要约束：路由注册顺序**
   * 此通配符路由**必须在所有具体路由之后注册**，原因：
   * 1. Vue Router采用**注册顺序匹配**，不是智能匹配
   * 2. 如果通配符路由在前，会拦截所有后续路由
   *
   * **错误示例：**
   * ```typescript
   * // ❌ 错误：通配符在前
   * router.addRoute({ path: '/:path(.*)*' });  // 会拦截所有路由
   * router.addRoute({ path: '/dashboard' });   // 永远不会被匹配
   * ```
   *
   * **正确示例：**
   * ```typescript
   * // ✅ 正确：具体路由在前
   * router.addRoute({ path: '/dashboard' });   // 优先匹配
   * router.addRoute({ path: '/users' });       // 优先匹配
   * router.addRoute({ path: '/:path(.*)*' });  // 兜底匹配
   * ```
   *
   * **技术实现：**
   * - 在路由守卫中动态添加，确保在所有动态路由之后
   * - 配合Layout组件提供统一的页面结构
   * - 子路由指向实际的404错误页面组件
   *
   */
  path: '/:path(.*)*',
  /**
   * 错误页面路由名称
   * @description 404错误页面的唯一路由标识符，用于路由守卫中的查找和匹配
   * @constant {string} ErrorPage
   */
  name: 'ErrorPage',

  /**
   * 布局组件配置
   * @description 使用系统主布局组件作为容器，保持与其他页面一致的UI结构
   * @see {@link @/layout/index.vue} 主布局组件
   */
  component: Layout,

  /**
   * 路由元信息配置
   * @description 定义错误页面的元数据，控制页面行为和显示特性
   */
  meta: {
    /**
     * 页面标题
     * @description 错误页面的标题，用于浏览器标签页显示和SEO优化
     */
    title: 'ErrorPage',

    /**
     * 隐藏面包屑导航
     * @description 错误页面不显示面包屑导航，提供更简洁的错误提示界面
     * @default true
     */
    hideBreadcrumb: true,
  },
  /**
   * 子路由配置
   * @description 定义错误页面的子路由结构，实现Layout布局内的404页面渲染
   */
  children: [
    {
      /**
       * 子路由通配符路径
       * @description 与父路由保持一致的通配符匹配模式，确保在Layout容器内正确渲染404页面
       *
       * **设计原理：**
       * - 父路由提供Layout容器（导航栏、侧边栏等）
       * - 子路由提供具体的404页面内容
       * - 保持与父路由相同的通配符模式，确保路径参数正确传递
       *
       * **路径参数传递：**
       * ```
       * 访问: /unknown/page
       * 父路由: /:path(.*)*  → path = "unknown/page"
       * 子路由: /:path(.*)*  → path = "unknown/page" (继承)
       * ```
       */
      path: '/:path(.*)*',
      /**
       * 子路由名称
       * @description 错误页面子路由的唯一标识符，用于路由导航和组件缓存控制
       */
      name: 'ErrorPageSon',

      /**
       * 错误页面组件
       * @description 懒加载的404错误页面组件，提供用户友好的错误提示和导航选项
       * @see {@link @/views/exception/404.vue} 404页面组件实现
       */
      component: ErrorPage,

      /**
       * 子路由元信息配置
       * @description 定义错误页面的元数据，控制页面标题和导航行为
       */
      meta: {
        /**
         * 页面标题
         * @description 错误页面的标题，会显示在浏览器标签页和页面标题中
         */
        title: 'ErrorPage',

        /**
         * 隐藏面包屑导航
         * @description 错误页面不显示面包屑导航，因为用户访问的是无效路径
         * @default true
         */
        hideBreadcrumb: true,
      },
    },
  ],
};

/**
 * 重定向路由配置
 * @description 用于处理页面重定向的路由配置，支持动态路径参数传递
 * @type {RouteRecordRaw}
 * @example
 * // 重定向示例：/redirect/dashboard -> 跳转到dashboard页面
 * // 重定向示例：/redirect/user/profile -> 跳转到user/profile页面
 */
export const RedirectRoute: RouteRecordRaw = {
  /** 重定向基础路径 */
  path: '/redirect',
  /** 重定向路由名称，使用常量定义 */
  name: RedirectName,
  /** 使用布局组件作为容器 */
  component: Layout,
  /** 路由元信息配置 */
  meta: {
    /** 页面标题，使用常量定义 */
    title: RedirectName,
    /** 隐藏面包屑导航 */
    hideBreadcrumb: true,
  },
  /** 子路由配置 */
  children: [
    {
      /**
       * 动态路径参数配置
       * @description 使用(.*)通配符捕获所有路径参数，支持多级路径重定向
       */
      path: '/redirect/:path(.*)',
      /** 子路由名称，动态拼接常量 */
      name: `${RedirectName}Child`,
      /**
       * 懒加载重定向处理组件
       * @description 使用动态导入实现代码分割，提升应用性能
       */
      component: () => import('@/views/redirect/index.vue'),
      /** 子路由元信息 */
      meta: {
        /** 子页面标题 */
        title: RedirectName,
        /** 隐藏面包屑导航 */
        hideBreadcrumb: true,
      },
    },
  ],
};
