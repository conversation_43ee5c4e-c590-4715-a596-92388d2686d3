import { renderIcon } from '@/utils';

// Ant Design 图标 - 只导入确实存在的图标
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  TableOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  SafetyOutlined,
  BankOutlined,
  HomeOutlined,
  MenuOutlined,
  ContactsOutlined,
  ToolOutlined,
  ControlOutlined,
  Pie<PERSON>hartOutlined,
  FolderOutlined,
  BookOutlined,
  ProjectOutlined,
  BorderOuterOutlined,
  WalletOutlined,
  FormOutlined,
  ExceptionOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  FileOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ApiOutlined,
  BugOutlined,
  CodeOutlined,
  DesktopOutlined,
  MobileOutlined,
  TabletOutlined,
  PrinterOutlined,
  ScanOutlined,
  CameraOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  CustomerServiceOutlined,
  PhoneOutlined,
  MailOutlined,
  MessageOutlined,
  NotificationOutlined,
  BellOutlined,
  CalendarOutlined,
  Clock<PERSON>ircleOutlined,
  HistoryOutlined,
  EnvironmentOutlined,
  CompassOutlined,
  GlobalOutlined,
  TranslationOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  AuditOutlined,
  MonitorOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  DotChartOutlined,
  FundOutlined,
  StockOutlined,
  PropertySafetyOutlined,
  InsuranceOutlined,
  CreditCardOutlined,
  PayCircleOutlined,
  AccountBookOutlined,
  CalculatorOutlined,
  PercentageOutlined,
  RiseOutlined,
  FallOutlined,
  SwapOutlined,
  SyncOutlined,
  ReloadOutlined,
  UndoOutlined,
  RedoOutlined,
  CopyOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  MinusOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  MoreOutlined,
  EllipsisOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CompressOutlined,
  ExpandOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  FastBackwardOutlined,
  FastForwardOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  BackwardOutlined,
  ForwardOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignBottomOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  AppstoreOutlined,
  UnorderedListOutlined as ListOutlined,
  StarOutlined,
  StarFilled,
  HeartOutlined,
  HeartFilled,
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  SmileOutlined,
  FrownOutlined,
  MehOutlined,
  FireOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  ExperimentOutlined,
  RocketOutlined,
  TrophyOutlined,
  GiftOutlined,
  DownloadOutlined,
  UploadOutlined,
  InboxOutlined,
  SendOutlined,
} from '@vicons/antd';

// Ionicons5 图标 - 只导入确实存在的图标
import {
  OptionsSharp,
  DocumentTextSharp,
  StatsChartSharp,
  PeopleSharp,
  SettingsSharp,
  HomeSharp,
  BusinessSharp,
  CloudSharp,
  CodeSharp,
  CogSharp,
  ConstructSharp,
  CubeSharp,
  DesktopSharp,
  DocumentSharp,
  FolderSharp,
  GridSharp,
  HammerSharp,
  ImageSharp,
  InformationCircleSharp,
  KeySharp,
  LayersSharp,
  LibrarySharp,
  ListSharp,
  LocationSharp,
  LockClosedSharp,
  MailSharp,
  MapSharp,
  MegaphoneSharp,
  MenuSharp,
  NotificationsSharp,
  PaperPlaneSharp,
  PersonSharp,
  PhonePortraitSharp,
  PieChartSharp,
  PrintSharp,
  PulseSharp,
  ReaderSharp,
  RefreshSharp,
  RocketSharp,
  SaveSharp,
  SearchSharp,
  ServerSharp,
  ShieldSharp,
  SpeedometerSharp,
  StarSharp,
  StorefrontSharp,
  TabletPortraitSharp,
  TerminalSharp,
  TimeSharp,
  TodaySharp,
  TrendingUpSharp,
  TrophySharp,
  WalletSharp,
  WarningSharp,
  WifiSharp,
} from '@vicons/ionicons5';

//前端路由图标映射表
export const constantRouterIcon = {
  // ==================== 导航类图标 ====================
  DashboardOutlined: renderIcon(DashboardOutlined),
  HomeOutlined: renderIcon(HomeOutlined),
  MenuOutlined: renderIcon(MenuOutlined),
  AppstoreOutlined: renderIcon(AppstoreOutlined),
  HomeSharp: renderIcon(HomeSharp),
  MenuSharp: renderIcon(MenuSharp),

  // ==================== 用户管理类 ====================
  UserOutlined: renderIcon(UserOutlined),
  TeamOutlined: renderIcon(TeamOutlined),
  ContactsOutlined: renderIcon(ContactsOutlined),
  PeopleSharp: renderIcon(PeopleSharp),
  PersonSharp: renderIcon(PersonSharp),

  // ==================== 系统设置类 ====================
  SettingOutlined: renderIcon(SettingOutlined),
  ToolOutlined: renderIcon(ToolOutlined),
  ControlOutlined: renderIcon(ControlOutlined),
  OptionsSharp: renderIcon(OptionsSharp),
  SettingsSharp: renderIcon(SettingsSharp),
  CogSharp: renderIcon(CogSharp),

  // ==================== 数据展示类 ====================
  TableOutlined: renderIcon(TableOutlined),
  BarChartOutlined: renderIcon(BarChartOutlined),
  PieChartOutlined: renderIcon(PieChartOutlined),
  LineChartOutlined: renderIcon(LineChartOutlined),
  AreaChartOutlined: renderIcon(AreaChartOutlined),
  DotChartOutlined: renderIcon(DotChartOutlined),
  StatsChartSharp: renderIcon(StatsChartSharp),
  PieChartSharp: renderIcon(PieChartSharp),

  // ==================== 文档类 ====================
  FileTextOutlined: renderIcon(FileTextOutlined),
  FolderOutlined: renderIcon(FolderOutlined),
  BookOutlined: renderIcon(BookOutlined),
  FileOutlined: renderIcon(FileOutlined),
  DocumentTextSharp: renderIcon(DocumentTextSharp),
  DocumentSharp: renderIcon(DocumentSharp),
  FolderSharp: renderIcon(FolderSharp),
  LibrarySharp: renderIcon(LibrarySharp),
  ReaderSharp: renderIcon(ReaderSharp),

  // ==================== 金融业务类 ====================
  BankOutlined: renderIcon(BankOutlined),
  SafetyOutlined: renderIcon(SafetyOutlined),
  FundOutlined: renderIcon(FundOutlined),
  StockOutlined: renderIcon(StockOutlined),
  PropertySafetyOutlined: renderIcon(PropertySafetyOutlined),
  InsuranceOutlined: renderIcon(InsuranceOutlined),
  CreditCardOutlined: renderIcon(CreditCardOutlined),
  PayCircleOutlined: renderIcon(PayCircleOutlined),
  AccountBookOutlined: renderIcon(AccountBookOutlined),
  CalculatorOutlined: renderIcon(CalculatorOutlined),
  PercentageOutlined: renderIcon(PercentageOutlined),
  WalletOutlined: renderIcon(WalletOutlined),
  BusinessSharp: renderIcon(BusinessSharp),
  WalletSharp: renderIcon(WalletSharp),

  // ==================== 项目管理类 ====================
  ProjectOutlined: renderIcon(ProjectOutlined),
  BorderOuterOutlined: renderIcon(BorderOuterOutlined),
  FormOutlined: renderIcon(FormOutlined),
  ListOutlined: renderIcon(ListOutlined),
  GridSharp: renderIcon(GridSharp),
  LayersSharp: renderIcon(LayersSharp),
  CubeSharp: renderIcon(CubeSharp),
  ListSharp: renderIcon(ListSharp),

  // ==================== 状态类图标 ====================
  CheckCircleOutlined: renderIcon(CheckCircleOutlined),
  CloseCircleOutlined: renderIcon(CloseCircleOutlined),
  ExclamationCircleOutlined: renderIcon(ExclamationCircleOutlined),
  InfoCircleOutlined: renderIcon(InfoCircleOutlined),
  ExceptionOutlined: renderIcon(ExceptionOutlined),
  WarningSharp: renderIcon(WarningSharp),
  InformationCircleSharp: renderIcon(InformationCircleSharp),

  // ==================== 技术类图标 ====================
  DatabaseOutlined: renderIcon(DatabaseOutlined),
  CloudOutlined: renderIcon(CloudOutlined),
  ApiOutlined: renderIcon(ApiOutlined),
  BugOutlined: renderIcon(BugOutlined),
  CodeOutlined: renderIcon(CodeOutlined),
  MonitorOutlined: renderIcon(MonitorOutlined),
  CloudSharp: renderIcon(CloudSharp),
  CodeSharp: renderIcon(CodeSharp),
  ServerSharp: renderIcon(ServerSharp),
  TerminalSharp: renderIcon(TerminalSharp),

  // ==================== 设备类图标 ====================
  DesktopOutlined: renderIcon(DesktopOutlined),
  MobileOutlined: renderIcon(MobileOutlined),
  TabletOutlined: renderIcon(TabletOutlined),
  PrinterOutlined: renderIcon(PrinterOutlined),
  DesktopSharp: renderIcon(DesktopSharp),
  PhonePortraitSharp: renderIcon(PhonePortraitSharp),
  TabletPortraitSharp: renderIcon(TabletPortraitSharp),
  PrintSharp: renderIcon(PrintSharp),

  // ==================== 媒体类图标 ====================
  ScanOutlined: renderIcon(ScanOutlined),
  CameraOutlined: renderIcon(CameraOutlined),
  PictureOutlined: renderIcon(PictureOutlined),
  VideoCameraOutlined: renderIcon(VideoCameraOutlined),
  AudioOutlined: renderIcon(AudioOutlined),
  ImageSharp: renderIcon(ImageSharp),

  // ==================== 通讯类图标 ====================
  CustomerServiceOutlined: renderIcon(CustomerServiceOutlined),
  PhoneOutlined: renderIcon(PhoneOutlined),
  MailOutlined: renderIcon(MailOutlined),
  MessageOutlined: renderIcon(MessageOutlined),
  NotificationOutlined: renderIcon(NotificationOutlined),
  BellOutlined: renderIcon(BellOutlined),
  MailSharp: renderIcon(MailSharp),
  NotificationsSharp: renderIcon(NotificationsSharp),
  PaperPlaneSharp: renderIcon(PaperPlaneSharp),
  MegaphoneSharp: renderIcon(MegaphoneSharp),

  // ==================== 时间类图标 ====================
  CalendarOutlined: renderIcon(CalendarOutlined),
  ClockCircleOutlined: renderIcon(ClockCircleOutlined),
  HistoryOutlined: renderIcon(HistoryOutlined),
  TimeSharp: renderIcon(TimeSharp),
  TodaySharp: renderIcon(TodaySharp),

  // ==================== 位置类图标 ====================
  EnvironmentOutlined: renderIcon(EnvironmentOutlined),
  CompassOutlined: renderIcon(CompassOutlined),
  GlobalOutlined: renderIcon(GlobalOutlined),
  LocationSharp: renderIcon(LocationSharp),
  MapSharp: renderIcon(MapSharp),

  // ==================== 安全类图标 ====================
  EyeOutlined: renderIcon(EyeOutlined),
  EyeInvisibleOutlined: renderIcon(EyeInvisibleOutlined),
  LockOutlined: renderIcon(LockOutlined),
  UnlockOutlined: renderIcon(UnlockOutlined),
  KeyOutlined: renderIcon(KeyOutlined),
  AuditOutlined: renderIcon(AuditOutlined),
  LockClosedSharp: renderIcon(LockClosedSharp),
  KeySharp: renderIcon(KeySharp),
  ShieldSharp: renderIcon(ShieldSharp),

  // ==================== 趋势类图标 ====================
  RiseOutlined: renderIcon(RiseOutlined),
  FallOutlined: renderIcon(FallOutlined),
  TrendingUpSharp: renderIcon(TrendingUpSharp),
  PulseSharp: renderIcon(PulseSharp),
  SpeedometerSharp: renderIcon(SpeedometerSharp),

  // ==================== 操作类图标 ====================
  SwapOutlined: renderIcon(SwapOutlined),
  SyncOutlined: renderIcon(SyncOutlined),
  ReloadOutlined: renderIcon(ReloadOutlined),
  UndoOutlined: renderIcon(UndoOutlined),
  RedoOutlined: renderIcon(RedoOutlined),
  RefreshSharp: renderIcon(RefreshSharp),
  SaveSharp: renderIcon(SaveSharp),

  // ==================== 编辑类图标 ====================
  CopyOutlined: renderIcon(CopyOutlined),
  EditOutlined: renderIcon(EditOutlined),
  DeleteOutlined: renderIcon(DeleteOutlined),
  PlusOutlined: renderIcon(PlusOutlined),
  MinusOutlined: renderIcon(MinusOutlined),

  // ==================== 文件操作类 ====================
  DownloadOutlined: renderIcon(DownloadOutlined),
  UploadOutlined: renderIcon(UploadOutlined),
  InboxOutlined: renderIcon(InboxOutlined),
  SendOutlined: renderIcon(SendOutlined),

  // ==================== 搜索过滤类 ====================
  SearchOutlined: renderIcon(SearchOutlined),
  FilterOutlined: renderIcon(FilterOutlined),
  SortAscendingOutlined: renderIcon(SortAscendingOutlined),
  SortDescendingOutlined: renderIcon(SortDescendingOutlined),
  SearchSharp: renderIcon(SearchSharp),

  // ==================== 界面控制类 ====================
  MoreOutlined: renderIcon(MoreOutlined),
  EllipsisOutlined: renderIcon(EllipsisOutlined),
  FullscreenOutlined: renderIcon(FullscreenOutlined),
  FullscreenExitOutlined: renderIcon(FullscreenExitOutlined),
  CompressOutlined: renderIcon(CompressOutlined),
  ExpandOutlined: renderIcon(ExpandOutlined),
  MenuFoldOutlined: renderIcon(MenuFoldOutlined),
  MenuUnfoldOutlined: renderIcon(MenuUnfoldOutlined),

  // ==================== 方向类图标 ====================
  ArrowUpOutlined: renderIcon(ArrowUpOutlined),
  ArrowDownOutlined: renderIcon(ArrowDownOutlined),
  ArrowLeftOutlined: renderIcon(ArrowLeftOutlined),
  ArrowRightOutlined: renderIcon(ArrowRightOutlined),
  CaretUpOutlined: renderIcon(CaretUpOutlined),
  CaretDownOutlined: renderIcon(CaretDownOutlined),
  CaretLeftOutlined: renderIcon(CaretLeftOutlined),
  CaretRightOutlined: renderIcon(CaretRightOutlined),
  DoubleLeftOutlined: renderIcon(DoubleLeftOutlined),
  DoubleRightOutlined: renderIcon(DoubleRightOutlined),
  FastBackwardOutlined: renderIcon(FastBackwardOutlined),
  FastForwardOutlined: renderIcon(FastForwardOutlined),
  VerticalAlignTopOutlined: renderIcon(VerticalAlignTopOutlined),
  VerticalAlignMiddleOutlined: renderIcon(VerticalAlignMiddleOutlined),
  VerticalAlignBottomOutlined: renderIcon(VerticalAlignBottomOutlined),

  // ==================== 播放控制类 ====================
  PlayCircleOutlined: renderIcon(PlayCircleOutlined),
  PauseCircleOutlined: renderIcon(PauseCircleOutlined),
  StopOutlined: renderIcon(StopOutlined),
  BackwardOutlined: renderIcon(BackwardOutlined),
  ForwardOutlined: renderIcon(ForwardOutlined),
  StepBackwardOutlined: renderIcon(StepBackwardOutlined),
  StepForwardOutlined: renderIcon(StepForwardOutlined),

  // ==================== 评价类图标 ====================
  StarOutlined: renderIcon(StarOutlined),
  StarFilled: renderIcon(StarFilled),
  HeartOutlined: renderIcon(HeartOutlined),
  HeartFilled: renderIcon(HeartFilled),
  LikeOutlined: renderIcon(LikeOutlined),
  LikeFilled: renderIcon(LikeFilled),
  DislikeOutlined: renderIcon(DislikeOutlined),
  DislikeFilled: renderIcon(DislikeFilled),
  SmileOutlined: renderIcon(SmileOutlined),
  FrownOutlined: renderIcon(FrownOutlined),
  MehOutlined: renderIcon(MehOutlined),
  StarSharp: renderIcon(StarSharp),

  // ==================== 特殊效果类 ====================
  FireOutlined: renderIcon(FireOutlined),
  ThunderboltOutlined: renderIcon(ThunderboltOutlined),
  BulbOutlined: renderIcon(BulbOutlined),
  ExperimentOutlined: renderIcon(ExperimentOutlined),
  RocketOutlined: renderIcon(RocketOutlined),
  TrophyOutlined: renderIcon(TrophyOutlined),
  GiftOutlined: renderIcon(GiftOutlined),
  RocketSharp: renderIcon(RocketSharp),
  TrophySharp: renderIcon(TrophySharp),

  // ==================== 商业类图标 ====================
  ShoppingCartOutlined: renderIcon(ShoppingCartOutlined),
  StorefrontSharp: renderIcon(StorefrontSharp),

  // ==================== 网络类图标 ====================
  WifiSharp: renderIcon(WifiSharp),

  // ==================== 工具类图标 ====================
  HammerSharp: renderIcon(HammerSharp),
  ConstructSharp: renderIcon(ConstructSharp),

  // ==================== 语言类图标 ====================
  TranslationOutlined: renderIcon(TranslationOutlined),
};
