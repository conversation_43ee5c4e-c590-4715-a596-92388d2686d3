import type { RouteRecordRaw, RouteMeta } from 'vue-router';
import { defineComponent } from 'vue';

/**
 * 扩展的路由元信息接口
 * @description 扩展 Vue Router 的 RouteMeta 接口，添加项目特定的元信息字段
 * @since 1.0.0
 */
export interface ExtendedRouteMeta extends RouteMeta {
  /** 路由标题 */
  title?: string;
  /** 路由图标 */
  icon?: string;
  /** 权限列表 */
  permissions: string[] | null;
  /** 是否隐藏菜单项 */
  hidden?: boolean;
  /** 是否忽略权限验证 */
  ignoreAuth?: boolean;
  /** 是否缓存组件 */
  keepAlive?: boolean;
  /** 是否固定在标签页 */
  affix?: boolean;
  /** 内嵌框架地址 */
  frameSrc?: string;
  /** 外链地址 */
  externalLink?: string;
  /** 排序权重 */
  sort?: number;
  /** 是否禁用 */
  disabled?: boolean;
  /** 额外渲染函数 */
  extra?: () => any;
  /** 标签 */
  label?: string;
  /** 唯一标识 */
  id?: number;
}

export type Component<T = any> =
  | ReturnType<typeof defineComponent>
  | (() => Promise<typeof import('*.vue')>)
  | (() => Promise<T>);

export interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'meta' | 'children'> {
  name: string;
  meta: ExtendedRouteMeta;
  component?: Component | string;
  components?: Component;
  children?: AppRouteRecordRaw[];
  props?: Recordable;
  fullPath?: string;
}

export interface Meta {
  // 名称
  title: string;
  // 是否忽略权限
  ignoreAuth?: boolean;
  permissions?: string[];
  // 是否不缓存
  noKeepAlive?: boolean;
  // 是否固定在tab上
  affix?: boolean;
  // tab上的图标
  icon?: string;
  // 跳转地址
  frameSrc?: string;
  // 外链跳转地址
  externalLink?: string;
  //隐藏
  hidden?: boolean;
}

export interface Menu {
  title: string;
  label: string;
  key: string;
  meta: RouteMeta;
  name: string;
  component?: Component | string;
  components?: Component;
  children?: AppRouteRecordRaw[];
  props?: Recordable;
  fullPath?: string;
  icon?: any;
  path: string;
  permissions?: string[];
  redirect?: string;
  sort?: number;
}

export interface IModuleType {
  default: Array<RouteRecordRaw> | RouteRecordRaw;
}

/**
 * 后端返回的路由元信息接口
 * @description 定义后端API返回的路由元数据结构
 * @since 1.0.0
 */
export interface BackendRouteMeta {
  /** 路由标题，显示为菜单名称 */
  title: string;
  /** 路由图标 */
  icon?: string;
  /** 权限列表 */
  permissions?: string[];
  /** 是否隐藏 */
  hidden?: boolean;
  /** 是否忽略权限 */
  ignoreAuth?: boolean;
  /** 是否不缓存 */
  noKeepAlive?: boolean;
  /** 是否固定在tab上 */
  affix?: boolean;
  /** 跳转地址 */
  frameSrc?: string;
  /** 外链跳转地址 */
  externalLink?: string;
  /** 排序权重 */
  sort?: number;
}

/**
 * 后端返回的路由数据项接口
 * @description 定义后端API返回的单个路由配置结构，用于类型安全的路由数据处理
 *
 * **数据来源：**
 * - 后端 `/tMenu/routes` 接口返回
 * - 用户权限过滤后的菜单数据
 *
 * **主要用途：**
 * - `generateRoutes` 函数的输入参数类型
 * - 后端路由数据的类型验证
 * - 前后端数据结构的统一约定
 *
 * **转换流程：**
 * ```
 * BackendRouteItem[] → generateRoutes() → AppRouteRecordRaw[]
 * ```
 *
 * @interface BackendRouteItem
 * @since 1.0.0
 * <AUTHOR>
 * @version 1.0.0
 *
 * @example
 * ```typescript
 * const backendRoute: BackendRouteItem = {
 *   path: '/dashboard',
 *   name: 'Dashboard',
 *   component: 'dashboard/index',
 *   meta: {
 *     title: '仪表板',
 *     icon: 'DashboardOutlined',
 *     permissions: ['dashboard:view']
 *   },
 *   keepAlive: 0,
 *   hidden: false,
 *   menuId: 1001,
 *   enableStatus: 0,
 *   children: []
 * };
 * ```
 */
export interface BackendRouteItem {
  /** 路由路径 */
  path: string;
  /** 路由名称 */
  name: string;
  /** 组件路径（相对于 views 目录） */
  component: string;
  /** 路由元信息 */
  meta: BackendRouteMeta;
  /** 重定向路径 */
  redirect?: string;
  /** 是否缓存页面（0: 缓存, 1: 不缓存） */
  keepAlive?: number;
  /** 是否隐藏菜单项 */
  hidden?: boolean;
  /** 特殊隐藏标识（1: 对系统用户隐藏） */
  specialHidden?: number;
  /** 菜单ID */
  menuId?: number;
  /** 启用状态（0: 启用, 1: 禁用） */
  enableStatus?: number;
  /** 子路由列表 */
  children?: BackendRouteItem[];
}
