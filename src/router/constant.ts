import type { BackendRouteItem } from '@/router/types';

export const RedirectName = 'Redirect';

export const ErrorPage = () => import('@/views/exception/404.vue');

export const Layout = () => import('@/layout/index.vue');

export const ParentLayout = () => import('@/layout/parentLayout.vue');

/**
 * 工作台菜单配置常量
 * @description 工作台模块的默认路由配置，包含主页面和各类详情页面
 *
 * **包含页面：**
 * - 工作台主页：dashboard/workplace/workplace
 * - 股票详情页：dashboard/workplace/SecDetails
 * - 转债详情页：dashboard/workplace/BondDetails
 * - 基金详情页：dashboard/workplace/FundDetails
 *
 * **设计特点：**
 * - 详情页面支持动态参数（Amount1）
 * - 详情页面默认隐藏，不在菜单中显示
 * - 所有页面启用缓存机制（keepAlive: 0）
 *
 * @constant {BackendRouteItem}
 * @since 1.0.0
 * <AUTHOR> Risk Management Platform Team
 */
export const WORKPLACE_MENU_CONFIG: BackendRouteItem = {
  path: '/dashboard',
  name: '/Dashboard',
  component: 'Layout',
  redirect: '/dashboard/workplace/workplace',
  meta: {
    icon: 'DashboardOutlined',
    title: '首页',
  },
  children: [
    {
      path: '/dashboard/workplace/workplace',
      name: '/DashboardWorkplace',
      component: 'dashboard/workplace/workplace',
      meta: {
        title: '工作台',
        icon: 'HomeOutlined',
      },
      keepAlive: 0, // 0 表示缓存，1 表示不缓存
    },
    {
      path: 'SecDetails/:Amount1?',
      name: 'SecDetails',
      component: '/dashboard/workplace/SecDetails',
      meta: {
        title: '股票详情',
        hidden: true,
      },
      keepAlive: 0,
      hidden: true,
    },
    {
      path: 'BondDetails/:Amount1?',
      name: 'BondDetails',
      component: '/dashboard/workplace/BondDetails',
      meta: {
        title: '转债详情',
        hidden: true,
      },
      keepAlive: 0,
      hidden: true,
    },
    {
      path: 'FundDetails/:Amount1?',
      name: 'FundDetails',
      meta: {
        title: '基金详情',
        hidden: true,
      },
      keepAlive: 0,
      hidden: true,
      component: '/dashboard/workplace/FundDetails',
    },
  ],
} as const;
