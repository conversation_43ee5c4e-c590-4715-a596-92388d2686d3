import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { BugOutlined } from '@vicons/antd';
import { renderIcon } from '@/utils';

/**
 * 图标测试路由
 * 用于测试新的图标系统
 */
const routes: Array<RouteRecordRaw> = [
  {
    path: '/icon-test',
    name: 'IconTest',
    component: Layout,
    meta: {
      title: '图标测试',
      icon: renderIcon(BugOutlined),
      sort: 999, // 排在最后
    },
    children: [
      {
        path: 'index',
        name: 'IconTestIndex',
        meta: {
          title: '图标系统测试',
        },
        component: () => import('@/test/IconTest.vue'),
      },
    ],
  },
];

export default routes;
