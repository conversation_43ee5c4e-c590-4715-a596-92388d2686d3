<template>
  <NConfigProvider
    v-if="!isLock"
    :date-locale="dateZhCN"
    :locale="zhCN"
    :theme="getDarkTheme"
    :theme-overrides="getThemeOverrides"
  >
    <AppProvider>
      <RouterView />
    </AppProvider>
  </NConfigProvider>

  <transition v-if="isLock && $route.name !== 'login'" name="slide-up">
    <LockScreen />
  </transition>
</template>

<script lang="ts" setup>
  import { computed, onMounted, onUnmounted } from 'vue';
  import { zhCN, dateZhCN, darkTheme } from 'naive-ui';
  import { LockScreen } from '@/components/navie-ui-admin/Lockscreen';
  import { AppProvider } from '@/components/Application';
  import { useScreenLockStore } from '@/store/modules/screenLock.js';
  import { useRoute } from 'vue-router';
  import { useDesignSettingStore } from '@/store/modules/designSetting';
  import { lighten } from '@/utils';

  const route = useRoute();
  const useScreenLock = useScreenLockStore();
  const designStore = useDesignSettingStore();
  const isLock = computed(() => useScreenLock.isLocked);
  const lockTime = computed(() => useScreenLock.lockTime);

  /**
   * @type import('naive-ui').GlobalThemeOverrides
   */
  const getThemeOverrides = computed(() => {
    const appTheme = designStore.appTheme;
    const lightenStr = lighten(designStore.appTheme, 6);
    return {
      common: {
        primaryColor: appTheme,
        primaryColorHover: lightenStr,
        primaryColorPressed: lightenStr,
        primaryColorSuppl: appTheme,
      },
      LoadingBar: {
        colorLoading: appTheme,
      },
      /*Button: {
        // 🎨 Primary 按钮渐变样式
        colorPrimary: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        colorHoverPrimary: 'linear-gradient(135deg, #0056b3 0%, #004085 100%)',
        colorPressedPrimary: 'linear-gradient(135deg, #004085 0%, #002752 100%)',
        colorFocusPrimary: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',

        // 🎨 Success 按钮渐变样式
        colorSuccess: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
        colorHoverSuccess: 'linear-gradient(135deg, #1e7e34 0%, #155724 100%)',
        colorPressedSuccess: 'linear-gradient(135deg, #155724 0%, #0d3d1a 100%)',
        colorFocusSuccess: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',

        // 🎨 Warning 按钮渐变样式
        colorWarning: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
        colorHoverWarning: 'linear-gradient(135deg, #e0a800 0%, #c69500 100%)',
        colorPressedWarning: 'linear-gradient(135deg, #c69500 0%, #a67c00 100%)',
        colorFocusWarning: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',

        // 🎨 Info 按钮渐变样式
        colorInfo: 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)',
        colorHoverInfo: 'linear-gradient(135deg, #138496 0%, #0f6674 100%)',
        colorPressedInfo: 'linear-gradient(135deg, #0f6674 0%, #0a4d56 100%)',
        colorFocusInfo: 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)',

        // 🎨 Error 按钮渐变样式
        colorError: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
        colorHoverError: 'linear-gradient(135deg, #c82333 0%, #a71e2a 100%)',
        colorPressedError: 'linear-gradient(135deg, #a71e2a 0%, #861e25 100%)',
        colorFocusError: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',

        // 🎨 通用样式优化
        borderRadius: '8px',
        fontWeight: '500',
        textColor: '#ffffff',
        textColorPrimary: '#ffffff',
        textColorSuccess: '#ffffff',
        textColorWarning: '#ffffff',
        textColorInfo: '#ffffff',
        textColorError: '#ffffff',

        // 🎨 阴影效果
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        boxShadowHover: '0 4px 12px rgba(0, 0, 0, 0.15)',
        boxShadowPressed: '0 1px 2px rgba(0, 0, 0, 0.1)',
        boxShadowFocus: '0 0 0 2px rgba(24, 144, 255, 0.2)',
      },*/
    };
  });

  const getDarkTheme = computed(() => (designStore.darkTheme ? darkTheme : undefined));

  let timer: NodeJS.Timer;

  const timekeeping = () => {
    clearInterval(timer);
    if (route.name == 'login' || isLock.value) return;
    // 设置不锁屏
    useScreenLock.setLock(false);
    // 重置锁屏时间
    useScreenLock.setLockTime();
    timer = setInterval(() => {
      // 锁屏倒计时递减
      useScreenLock.setLockTime(lockTime.value - 1);
      if (lockTime.value <= 0) {
        // 设置锁屏
        useScreenLock.setLock(true);
        return clearInterval(timer);
      }
    }, 1000);
  };

  onMounted(() => {
    document.addEventListener('mousedown', timekeeping);
  });

  onUnmounted(() => {
    document.removeEventListener('mousedown', timekeeping);
  });
</script>

<style lang="less">
  @import 'styles/index.less';
</style>
