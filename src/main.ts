import './styles/tailwind.css';
import { createApp } from 'vue';
import { setupDirectives, setupNaive, setupNaiveDiscreteApi } from '@/plugins';
import App from './App.vue';
import router, { setupRouter } from './router';
import { setupStore } from '@/store';
import { MotionPlugin } from '@vueuse/motion';
import { checkConfigurationIssues, logEnvironmentInfo } from '@/utils/debug/envDebug';
import { storage } from '@/utils/common/storage';
import { ACCESS_TOKEN, CURRENT_USER_INFO } from '@/store/mutation-types';
import { FirstLoginStatus } from '@/enums/base/userEnum';

//
import '@/styles/table-style.css';
import '@/styles/common-style.css';
import '@/styles/button-styles.less';

/**
 * 检查并清理过期的首次登录状态
 * 防止用户重启浏览器后仍然停留在修改密码页面
 */
function checkAndCleanFirstLoginState() {
  const token = storage.get(ACCESS_TOKEN);
  const userInfo = storage.get(CURRENT_USER_INFO);

  // 如果没有token但有用户信息，说明session已过期，清理所有数据
  if (!token && userInfo) {
    console.log('🧹 检测到过期的用户信息，正在清理...');
    storage.remove(CURRENT_USER_INFO);
    storage.remove(ACCESS_TOKEN);
    return;
  }

  // 如果有首次登录状态但没有有效token，清理状态
  if (userInfo && userInfo.firstLogin === FirstLoginStatus.YES && !token) {
    console.log('🧹 检测到过期的首次登录状态，正在清理...');
    storage.remove(CURRENT_USER_INFO);
    storage.remove(ACCESS_TOKEN);
    return;
  }

  // 可以在这里添加更多的token有效性检查
  console.log('✅ 用户状态检查完成');
}

async function bootstrap() {
  const app = createApp(App);
  // 挂载状态管理
  setupStore(app);

  // 注册全局常用的 naive-ui 组件
  setupNaive(app);

  // 挂载 naive-ui 脱离上下文的 Api
  setupNaiveDiscreteApi();

  // 注册全局自定义组件
  //setupCustomComponents();

  // 注册全局自定义指令，如：v-permission权限指令
  setupDirectives(app);

  // 注册全局方法，如：app.config.globalProperties.$message = message
  //setupGlobalMethods(app);

  // 挂载路由
  setupRouter(app);

  // 路由准备就绪后挂载 APP 实例
  // https://router.vuejs.org/api/interfaces/router.html#isready
  await router.isReady();

  // 检查并清理过期的首次登录状态
  checkAndCleanFirstLoginState();

  // 在开发环境下显示详细的环境和配置信息
  if (import.meta.env.DEV) {
    console.log('开发环境，输出环境和配置信息...');
    logEnvironmentInfo();
    checkConfigurationIssues();
  }

  // https://www.naiveui.com/en-US/os-theme/docs/style-conflict#About-Tailwind's-Preflight-Style-Override
  const meta = document.createElement('meta');
  meta.name = 'naive-ui-style';
  document.head.appendChild(meta);
  // 屏蔽警告信息
  app.config.warnHandler = () => null;
  app.use(MotionPlugin);
  app.mount('#app', true);
}

void bootstrap();
