/**
 * @fileoverview 项目全局配置文件
 * @description 塔金风险管理平台的核心配置文件，定义了整个应用的UI布局、主题、权限模式等关键设置
 *
 * 🏗️ **架构作用**：
 * - 作为整个应用的配置中心，统一管理所有UI相关的默认设置
 * - 为状态管理系统提供初始化数据源
 * - 支持企业级的主题定制和布局配置
 *
 * 🔄 **数据流向**：
 * projectSetting.ts → useProjectSettingStore → useProjectSetting → Layout Components
 *
 * 📋 **使用场景**：
 * - 系统初始化时的默认配置加载
 * - 用户个性化设置的基准值
 * - 不同环境下的配置差异化管理
 * - 企业级定制化需求的配置基础
 *
 * <AUTHOR> Risk Management Platform Team
 * @version 1.3.0
 * @since 2024-01-01
 * @lastModified 2024-12-18
 *
 * @example
 * ```typescript
 * // 在状态管理中使用
 * import projectSetting from '@/settings/projectSetting';
 * const { navMode, navTheme } = projectSetting;
 *
 * // 在组件中通过hook使用
 * import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
 * const { navMode, headerSetting } = useProjectSetting();
 * ```
 */

import { PermissionModeEnum } from '@/enums/base/permissionEnum';

/**
 * 项目全局配置对象
 * @description 定义塔金风险管理平台的所有UI配置项，包括导航、主题、布局等核心设置
 *
 * 🎯 **配置分类**：
 * - 导航配置：控制菜单布局和主题
 * - 布局配置：头部、页脚、标签页等区域设置
 * - 交互配置：动画、响应式等用户体验设置
 * - 权限配置：路由权限验证模式
 *
 * ⚙️ **配置优先级**：
 * 1. 用户个人设置（localStorage）
 * 2. 企业定制配置（环境变量）
 * 3. 默认配置（此文件）
 */
const setting = {
  // ==================== 导航系统配置 ====================

  /**
   * 导航布局模式
   * @type {'vertical' | 'horizontal' | 'horizontal-mix'}
   * @default 'vertical'
   * @description 控制主导航菜单的布局方式
   *
   * 📋 **可选值**：
   * - `vertical`: 左侧垂直菜单模式（推荐用于管理后台）
   * - `horizontal`: 顶部水平菜单模式（适合内容展示型应用）
   * - `horizontal-mix`: 混合模式（顶部一级菜单 + 左侧二级菜单）
   *
   * 🎯 **业务场景**：
   * - 金融交易系统：推荐使用 `vertical`，便于快速切换功能模块
   * - 数据展示平台：可使用 `horizontal`，最大化内容展示区域
   * - 复杂业务系统：使用 `horizontal-mix`，平衡导航效率和空间利用
   */
  navMode: 'vertical',

  /**
   * 导航主题风格
   * @type {'dark' | 'light' | 'header-dark'}
   * @default 'light'
   * @description 控制导航区域的色彩主题
   *
   * 📋 **可选值**：
   * - `dark`: 深色侧边栏主题（经典管理后台风格）
   * - `light`: 浅色侧边栏主题（现代简洁风格）
   * - `header-dark`: 深色顶栏主题（适合品牌色突出）
   *
   * 🎨 **设计考量**：
   * - 深色主题：减少视觉疲劳，适合长时间操作
   * - 浅色主题：界面更加清爽，适合数据密集型应用
   * - 混合主题：平衡视觉层次和品牌展示
   */
  navTheme: 'light',

  /**
   * 移动端模式标识
   * @type {boolean}
   * @default false
   * @description 标识当前是否处于移动端适配模式
   *
   * 📱 **响应式设计**：
   * - 自动检测设备类型并调整布局
   * - 移动端下自动折叠侧边栏
   * - 优化触摸操作体验
   *
   * 🔧 **技术实现**：
   * - 结合 CSS 媒体查询实现响应式布局
   * - 通过 JavaScript 动态检测屏幕尺寸
   * - 支持横竖屏切换适配
   */
  isMobile: false,

  // ==================== 头部区域配置 ====================

  /**
   * 页面头部配置
   * @type {object}
   * @description 控制页面顶部区域的外观和行为设置
   *
   * 🎯 **功能特性**：
   * - 支持固定定位，保持头部始终可见
   * - 自定义背景色，适配企业品牌色
   * - 集成重载按钮，提升开发和用户体验
   */
  headerSetting: {
    /**
     * 头部背景色
     * @type {string}
     * @default '#fff'
     * @description 页面头部的背景颜色，支持十六进制、RGB、CSS变量等格式
     *
     * 🎨 **颜色规范**：
     * - 浅色主题：#fff（纯白）或 #fafafa（浅灰）
     * - 深色主题：#001529（深蓝）或 #141414（深灰）
     * - 品牌色：根据企业VI规范定制
     */
    bgColor: '#fff',

    /**
     * 固定头部定位
     * @type {boolean}
     * @default true
     * @description 是否将头部固定在页面顶部，滚动时保持可见
     *
     * ✅ **启用优势**：
     * - 重要操作按钮始终可见（用户信息、设置、退出等）
     * - 导航面包屑持续显示，用户不会迷失位置
     * - 符合现代Web应用的交互习惯
     *
     * ❌ **禁用场景**：
     * - 内容密集型应用，需要最大化显示区域
     * - 特殊的全屏展示需求
     */
    fixed: true,

    /**
     * 显示页面重载按钮
     * @type {boolean}
     * @default true
     * @description 是否在头部显示页面刷新/重载按钮
     *
     * 🔧 **功能用途**：
     * - 开发环境：快速刷新页面，查看代码修改效果
     * - 生产环境：用户遇到问题时的快速恢复手段
     * - 数据刷新：某些实时性要求高的页面需要手动刷新
     *
     * 💡 **最佳实践**：
     * - 开发环境建议启用，便于调试
     * - 生产环境可根据业务需求决定是否保留
     */
    isReload: true,
  },

  // ==================== 页脚区域配置 ====================

  /**
   * 显示页面页脚
   * @type {boolean}
   * @default true
   * @description 控制是否显示页面底部的页脚信息
   *
   * 📄 **页脚内容**：
   * - 版权信息：© 2024 塔金风险管理平台
   * - 技术支持：开发团队联系方式
   * - 法律声明：使用条款、隐私政策等
   * - 版本信息：当前系统版本号
   *
   * 🎯 **显示策略**：
   * - 管理后台：通常显示，提供必要的法律和技术信息
   * - 数据大屏：通常隐藏，最大化数据展示区域
   * - 移动端：可选择性显示，避免占用过多屏幕空间
   *
   * @deprecated v1.15 版本后废弃，占用操作空间，实际使用价值不高
   */
  showFooter: true,

  // ==================== 多标签页配置 ====================

  /**
   * 多标签页功能配置
   * @type {object}
   * @description 控制页面顶部多标签页的外观和行为
   *
   * 🚀 **核心功能**：
   * - 支持多页面同时打开，提升工作效率
   * - 标签页状态持久化，页面刷新后恢复
   * - 支持拖拽排序、右键菜单等高级交互
   * - 智能缓存管理，优化内存使用
   */
  multiTabsSetting: {
    /**
     * 标签页背景色
     * @type {string}
     * @default '#fff'
     * @description 多标签页区域的背景颜色
     *
     * 🎨 **设计原则**：
     * - 与头部背景色保持一致，形成视觉连贯性
     * - 避免过于鲜艳的颜色，减少视觉干扰
     * - 支持主题切换时的自动适配
     */
    bgColor: '#fff',

    /**
     * 显示多标签页
     * @type {boolean}
     * @default true
     * @description 是否启用多标签页功能
     *
     * ✅ **启用场景**：
     * - 复杂业务系统：用户需要在多个功能模块间频繁切换
     * - 数据对比分析：需要同时查看多个页面的数据
     * - 工作流程：按步骤完成的业务流程
     *
     * ❌ **禁用场景**：
     * - 简单应用：功能单一，不需要多页面切换
     * - 移动端：屏幕空间有限，标签页体验不佳
     * - 特殊业务：某些金融交易场景要求专注单一页面
     */
    show: true,

    /**
     * 固定多标签页位置
     * @type {boolean}
     * @default true
     * @description 是否将多标签页固定在页面顶部
     *
     * 🎯 **固定优势**：
     * - 标签页始终可见，方便快速切换
     * - 与固定头部形成统一的操作区域
     * - 符合用户对浏览器标签页的使用习惯
     */
    fixed: true,
  },

  // ==================== 侧边菜单配置 ====================

  /**
   * 侧边菜单配置
   * @type {object}
   * @description 控制左侧导航菜单的尺寸、行为和交互方式
   *
   * 📐 **尺寸设计**：
   * - 基于人机工程学原理设计菜单宽度
   * - 支持响应式适配不同屏幕尺寸
   * - 提供折叠模式节省屏幕空间
   */
  menuSetting: {
    /**
     * 菜单最小宽度
     * @type {number}
     * @default 64
     * @unit px
     * @description 菜单折叠时的最小宽度，通常只显示图标
     *
     * 📏 **设计标准**：
     * - 64px：标准图标尺寸 + 内边距，符合Material Design规范
     * - 保证图标清晰可见，支持快速识别
     * - 为hover展开效果预留空间
     */
    minMenuWidth: 64,

    /**
     * 菜单正常宽度
     * @type {number}
     * @default 240
     * @unit px
     * @description 菜单展开时的标准宽度，显示完整的菜单项文本
     *
     * 📏 **宽度考量**：
     * - 240px：适合中文菜单项显示，避免文字截断
     * - 平衡内容展示区域和导航区域的比例
     * - 符合主流管理后台的设计规范
     *
     * 🌍 **国际化支持**：
     * - 中文：240px 足够显示常见的功能模块名称
     * - 英文：可适当减少到 200px
     * - 其他语言：根据文字长度特点调整
     */
    menuWidth: 240,

    /**
     * 固定菜单位置
     * @type {boolean}
     * @default true
     * @description 是否将侧边菜单固定在页面左侧
     *
     * ✅ **固定优势**：
     * - 导航始终可见，提升操作效率
     * - 与内容区域形成清晰的功能分区
     * - 支持长页面滚动时的导航稳定性
     */
    fixed: true,

    /**
     * 混合菜单模式
     * @type {boolean}
     * @default false
     * @description 是否启用顶部+侧边的混合菜单布局
     *
     * 🔀 **混合模式特点**：
     * - 一级菜单显示在顶部，节省垂直空间
     * - 二级菜单显示在侧边，保持层级清晰
     * - 适合菜单层级较深的复杂系统
     *
     * 🎯 **适用场景**：
     * - 大型企业级应用：功能模块众多
     * - 多业务线系统：需要清晰的业务分区
     * - 宽屏显示环境：充分利用水平空间
     */
    mixMenu: false,

    /**
     * 移动端触发宽度
     * @type {number}
     * @default 800
     * @unit px
     * @description 屏幕宽度小于此值时自动切换到移动端布局
     *
     * 📱 **响应式断点**：
     * - 800px：平板电脑的典型宽度
     * - 小于此宽度时侧边菜单自动隐藏
     * - 通过汉堡菜单按钮控制菜单显示/隐藏
     *
     * 🎯 **设备适配**：
     * - 手机（< 480px）：完全隐藏侧边菜单
     * - 平板（480px - 800px）：可选择显示/隐藏
     * - 桌面（> 800px）：正常显示侧边菜单
     */
    mobileWidth: 800,

    /**
     * 菜单折叠状态
     * @type {boolean}
     * @default false
     * @description 菜单的初始折叠状态
     *
     * 🔄 **状态管理**：
     * - false：默认展开，显示完整菜单项
     * - true：默认折叠，只显示图标
     * - 用户操作后的状态会被持久化保存
     *
     * 💾 **持久化策略**：
     * - 用户的折叠偏好保存在 localStorage
     * - 页面刷新后恢复用户的选择
     * - 支持不同设备间的状态同步
     */
    collapsed: false,
  },

  // ==================== 面包屑导航配置 ====================

  /**
   * 面包屑导航配置
   * @type {object}
   * @description 控制页面顶部面包屑导航的显示和样式
   *
   * 🧭 **导航价值**：
   * - 显示用户当前位置，防止在复杂系统中迷失
   * - 提供快速返回上级页面的途径
   * - 增强页面层级结构的可理解性
   */
  crumbsSetting: {
    /**
     * 显示面包屑导航
     * @type {boolean}
     * @default true
     * @description 是否显示页面顶部的面包屑导航
     *
     * ✅ **显示优势**：
     * - 用户始终知道自己在系统中的位置
     * - 提供便捷的页面层级导航
     * - 符合用户对Web应用的使用习惯
     *
     * 🎯 **适用场景**：
     * - 多层级菜单结构：帮助用户理解页面关系
     * - 复杂业务流程：显示当前步骤在整个流程中的位置
     * - 数据钻取分析：显示从概览到详情的路径
     */
    show: true,

    /**
     * 显示面包屑图标
     * @type {boolean}
     * @default false
     * @description 是否在面包屑导航中显示页面图标
     *
     * 🎨 **视觉设计**：
     * - false：纯文字面包屑，界面更简洁
     * - true：图标+文字，视觉识别度更高
     *
     * 📱 **移动端考量**：
     * - 小屏幕设备建议关闭图标，节省空间
     * - 大屏幕设备可开启图标，增强视觉效果
     *
     * 🎯 **业务场景**：
     * - 金融系统：图标有助于快速识别不同业务模块
     * - 数据平台：纯文字更适合信息密集的界面
     */
    showIcon: false,
  },

  // ==================== 权限系统配置 ====================

  /**
   * 菜单权限验证模式
   * @type {PermissionModeEnum}
   * @default PermissionModeEnum.BACK
   * @description 控制系统路由和菜单的权限验证方式
   *
   * 🔐 **权限模式对比**：
   *
   * **FIXED（前端固定路由）**：
   * - 路由配置写死在前端代码中
   * - 通过用户权限标识控制菜单显示/隐藏
   * - 适合权限结构相对固定的小型系统
   * - 优势：响应速度快，离线可用
   * - 劣势：权限变更需要重新部署前端
   *
   * **BACK（后端动态获取）**：
   * - 路由配置由后端API动态返回
   * - 根据用户角色实时生成可访问的菜单
   * - 适合权限结构复杂多变的企业级系统
   * - 优势：权限控制灵活，支持实时调整
   * - 劣势：依赖网络，首次加载稍慢
   *
   * 🎯 **塔金平台选择 BACK 的原因**：
   * - 金融系统权限要求严格，需要精细化控制
   * - 支持多角色、多层级的复杂权限体系
   * - 便于合规审计和权限变更管理
   * - 适应业务快速发展的权限需求变化
   *
   * @see {@link PermissionModeEnum} 权限模式枚举定义
   */
  permissionMode: PermissionModeEnum.BACK,

  // ==================== 用户体验配置 ====================

  /**
   * 启用页面切换动画
   * @type {boolean}
   * @default true
   * @description 是否在页面路由切换时显示过渡动画效果
   *
   * ✨ **动画价值**：
   * - 提升用户体验，减少页面跳跃感
   * - 增加应用的现代感和专业度
   * - 为用户提供视觉反馈，确认操作生效
   *
   * ⚡ **性能考量**：
   * - 现代浏览器对CSS动画优化良好，性能影响微小
   * - 可根据设备性能动态调整动画复杂度
   * - 提供开关选项，满足不同用户偏好
   *
   * 🎯 **业务场景**：
   * - 展示型应用：建议启用，增强视觉体验
   * - 操作密集型应用：可选择性启用，避免影响效率
   * - 低性能设备：建议关闭，优先保证功能流畅性
   */
  isPageAnimate: true,

  /**
   * 页面切换动画类型
   * @type {string}
   * @default 'zoom-fade'
   * @description 指定页面路由切换时使用的动画效果类型
   *
   * 🎬 **可选动画类型**：
   * - `zoom-fade`: 缩放淡入淡出（推荐，现代感强）
   * - `slide-left`: 左滑切换（适合移动端风格）
   * - `slide-right`: 右滑切换（适合返回操作）
   * - `fade`: 纯淡入淡出（简洁，性能最佳）
   * - `slide-up`: 上滑切换（适合层级向上导航）
   * - `slide-down`: 下滑切换（适合层级向下导航）
   *
   * 🎨 **选择原则**：
   * - 考虑应用的整体设计风格
   * - 匹配用户的操作习惯和预期
   * - 平衡视觉效果和性能表现
   *
   * 💡 **最佳实践**：
   * - 管理后台：推荐 `zoom-fade` 或 `fade`
   * - 移动端应用：推荐 `slide-left/right`
   * - 数据展示：推荐 `fade`，减少视觉干扰
   */
  pageAnimateType: 'zoom-fade',
};

/**
 * 导出项目配置对象
 * @description 将配置对象作为默认导出，供其他模块使用
 *
 * 📦 **使用方式**：
 * ```typescript
 * // 完整导入
 * import projectSetting from '@/settings/projectSetting';
 *
 * // 解构导入
 * import projectSetting from '@/settings/projectSetting';
 * const { navMode, navTheme, headerSetting } = projectSetting;
 *
 * // 在状态管理中使用
 * import { defineStore } from 'pinia';
 * import projectSetting from '@/settings/projectSetting';
 *
 * export const useProjectSettingStore = defineStore('project-setting', () => {
 *   const navMode = ref(projectSetting.navMode);
 *   // ...
 * });
 * ```
 *
 * 🔄 **配置更新流程**：
 * 1. 修改此文件中的默认配置值
 * 2. 重启开发服务器或重新构建应用
 * 3. 清除用户的 localStorage 缓存（如需要）
 * 4. 新配置将作为默认值生效
 *
 * ⚠️ **注意事项**：
 * - 修改配置后建议进行全面测试
 * - 某些配置变更可能影响现有用户的使用习惯
 * - 生产环境配置变更需要谨慎评估影响范围
 */
export default setting;
