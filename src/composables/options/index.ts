/**
 * @file options.ts
 * @description 选项配置
 */

import { getStockType } from '@/api/sec/secInfoApi';
import { SelectOption } from 'naive-ui';
import { Ref } from 'vue';
import { getStockRegion } from '@/api/levelStrategy/levelStrategyApi';

/**
 * 获取股票类型选项，一般是作为选择框中的下拉选项，默认不包含注册制和非注册制
 */
export const fetchStockTypeOptions = (
  stockTypeOptions: Ref<SelectOption[]>,
  includeRegOrNonReg: boolean = false
) => {
  getStockType()
    .then(function (res) {
      if (res.code === 200) {
        const data = <any>[];
        res.data.forEach((row) => {
          data.push({ label: row, value: row });
        });
        if (includeRegOrNonReg) {
          data.push({ label: '注册制', value: '注册制' });
          data.push({ label: '非注册制', value: '非注册制' });
        }
        stockTypeOptions.value = data;
      }
    })
    .catch(function (error) {
      console.log('获取股票类型失败:', error);
    });
};

/**
 * 获取上市公司地区属性
 */
export const fetchStockRegionOptions = (stockRegionOptions: Ref<SelectOption[]>) => {
  getStockRegion()
    .then(function (res) {
      if (res.code === 200) {
        const data = <any>[];
        res.data.forEach((stockRegion) => {
          if (stockRegion) {
            data.push({ label: stockRegion, value: stockRegion });
          }
        });
        stockRegionOptions.value = data;
      }
    })
    .catch(function (error) {
      console.log('获取上市公司地区属性失败:', error);
    });
};
