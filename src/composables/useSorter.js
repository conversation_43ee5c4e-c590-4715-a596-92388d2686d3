// src/composables/useSorter.js
/**用于处理排序的公共组件*/

import { ref } from 'vue';

export function useSorter(pageData) {
  /**
   * 排序信息
   */
  const sorting = ref({
    ascOrDesc: '',
    orderBy: '',
  });

  /**
   * 排序变化处理函数
   * @param sorter 排序信息
   */
  const handleSorterChange = (sorter) => {
    if (!sorter.order) {
      sorting.value.ascOrDesc = '';
      sorting.value.orderBy = '';
    } else {
      sorting.value.ascOrDesc = sorter.order === 'ascend' ? 'asc' : 'desc';
      sorting.value.orderBy = sorter.columnKey;
    }
    pageData.current = 1;
  };

  return {
    sorting,
    handleSorterChange,
  };
}
