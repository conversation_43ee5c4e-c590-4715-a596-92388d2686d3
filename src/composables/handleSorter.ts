// src/composables/useSorter.ts
/**
 * 用于处理排序的公共组件
 *
 * @module useSorter
 */
import { PageRequest } from '@/models/common/baseRequest';

/**
 * 使用排序功能的组合式函数
 *
 * @returns {Object} 一个包含排序信息和排序变化处理函数的对象
 * @param pageRequest
 * @param column
 */
export function handleSorterChange(pageRequest: PageRequest, column) {
  if (column['columnKey']) {
    if (!column.order) {
      pageRequest.ascOrDesc = '';
      pageRequest.orderBy = '';
    } else {
      pageRequest.ascOrDesc = column.order === 'ascend' ? 'asc' : 'desc';
      pageRequest.orderBy = column.columnKey;
    }
    pageRequest.current = 1;
  }
}
