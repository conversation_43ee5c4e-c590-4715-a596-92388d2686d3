<template>
  <RouterView>
    <template #default="{ Component, route }">
      <template v-if="mode === 'production'">
        <transition :name="getTransitionName" mode="out-in" appear>
          <keep-alive v-if="keepAliveComponents.length" :include="keepAliveComponents">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
          <component v-else :is="Component" :key="route.fullPath" />
        </transition>
      </template>
      <template v-else>
        <keep-alive v-if="keepAliveComponents.length" :include="keepAliveComponents">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
        <component v-else :is="Component" :key="route.fullPath" />
      </template>
    </template>
  </RouterView>
</template>

<script setup lang="ts">
  /**
   * @file MainView 主视图组件
   * @description Vue 3 主内容区域组件，负责路由内容渲染、组件缓存管理和页面过渡动画
   * <AUTHOR> Risk Management Platform Team
   * @version 2.0.0 - 升级到 Vue 3 Composition API with <script setup>
   */
  import { computed, unref } from 'vue';
  import { useAsyncRouteStore } from '@/store/modules/asyncRoute';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';

  /**
   * 组件属性定义
   * @description 定义组件接收的 props 参数
   */
  interface Props {
    /** 是否不需要 key 属性 */
    notNeedKey?: boolean;
    /** 是否启用动画效果 */
    animate?: boolean;
  }

  /**
   * 定义组件 props，使用 TypeScript 接口
   * @description 为组件提供类型安全的属性定义
   */
  const props = withDefaults(defineProps<Props>(), {
    notNeedKey: false,
    animate: true,
  });

  // ==================== 状态管理和配置 ====================

  /**
   * 获取项目设置配置
   * @description 获取页面动画相关的配置信息
   */
  const { isPageAnimate, pageAnimateType } = useProjectSetting();

  /**
   * 获取异步路由状态管理器
   * @description 用于访问组件缓存列表等路由相关状态
   */
  const asyncRouteStore = useAsyncRouteStore();

  // ==================== 计算属性 ====================

  /**
   * 需要缓存的路由组件列表
   * @description 从 store 中获取需要进行 keep-alive 缓存的组件名称列表
   * @returns {string[]} 组件名称数组
   */
  const keepAliveComponents = computed(() => asyncRouteStore.getKeepAliveComponents);

  /**
   * 获取页面过渡动画名称
   * @description 根据项目配置决定是否启用页面切换动画
   * @returns {string} 动画名称或空字符串
   */
  const getTransitionName = computed(() => {
    return unref(isPageAnimate) ? unref(pageAnimateType) : '';
  });

  /**
   * 当前运行模式
   * @description 获取当前环境模式（development/production）
   * 用于区分开发环境和生产环境的不同行为
   */
  const mode = import.meta.env.MODE;

  // ==================== 组件名称定义 ====================

  /**
   * 定义组件名称
   * @description 为组件设置名称，便于调试和开发工具识别
   */
  defineOptions({
    name: 'MainView',
  });
</script>

<style lang="less" scoped></style>
