<template>
  <!-- 顶部搜索证券 -->
  <div ref="searchContainer">
    <n-space>
      <n-tooltip placement="bottom">
        <template #trigger>
          <n-icon>
            <Search v-if="!showFund" style="font-size: 18px" @click="showFund = !showFund" />
          </n-icon>
        </template>
        <span>搜索证券</span>
      </n-tooltip>
      <transition name="fade">
        <SecuritySearchFund
          v-if="showFund"
          ref="securitySearchRef"
          width="250px"
          @get-value="getStockValue"
        />
      </transition>
    </n-space>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted, ref } from 'vue';
  import { NIcon, useMessage } from 'naive-ui';
  import { Search } from '@vicons/ionicons5';
  import SecuritySearchFund from '@/components/SecuritySearch/SecuritySearchFund.vue';
  import { useRouter } from 'vue-router';
  import { SecType } from '@/enums/secEnum';
  import {
    openConvertibleBondFile,
    openFundFile,
    openSecurityArchives,
  } from '@/utils/goToArchives';
  import { encrypt } from '@/utils/crypto/encode';
  import { vagueQuerySecCodes } from '@/api/sec/secInfoApi';

  // 定义组件名称
  defineOptions({
    name: 'StockSearch',
  });

  const router = useRouter();
  const message = useMessage();

  const showFund = ref(false);

  const getStockValue = async (id, name, type) => {
    if (!id) {
      return;
    }

    const { code, data, msg } = await vagueQuerySecCodes(
      {
        current: 1,
        size: 10,
      },
      id,
      null
    );
    if (code === 200) {
      if (!data.records || data.records.length === 0) {
        alert('暂无该证券信息');
        return;
      }

      const { secType, secCategory, secName, secCode } = data.records[0];
      //如果是股票
      if (secType == SecType.STOCK || !secType) {
        router.push({
          name: 'SecDetails',
          query: {
            secName,
          },
          params: { Amount1: secCode.split('.')[0], Amount2: '' },
        });
        //如果是基金
      } else if (secType == SecType.FUND) {
        router.push({
          name: 'FundDetails',
          query: {
            secName,
          },
          params: {
            Amount1: secCode.split('.')[0],
            Amount2: '',
          },
        });

        //如果是债券
      } else if (secType == SecType.BOND) {
        router.push({
          query: {
            secName,
          },
          name: 'BondDetails',
          params: {
            Amount1: secCode.split('.')[0] + '-' + (secCategory == '可转债' ? 'kzz' : secName),
            Amount2: '',
          },
        });
      }
    } else {
      message.error(msg);
    }
  };
</script>

<style>
  /* 定义过渡效果 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }
  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }
</style>
