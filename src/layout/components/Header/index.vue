<template>
  <div class="layout-header">
    <!--顶部菜单-->
    <div
      v-if="unref(navMode) === 'horizontal' || (unref(navMode) === 'horizontal-mix' && mixMenu)"
      class="layout-header-left"
    >
      <div v-if="unref(navMode) === 'horizontal'" class="logo">
        <img :src="websiteConfig.logo" alt="" />
        <!--        <h2 v-show="!collapsed" class="title">{{ websiteConfig.title }}</h2>-->
      </div>
      <AsideMenu
        v-model:location="getMenuLocation"
        :collapsed="collapsed"
        :inverted="getInverted"
        mode="horizontal"
        @update:collapsed="(value) => emit('update:collapsed', value)"
      />
    </div>
    <!--左侧菜单-->
    <div v-else class="layout-header-left">
      <!-- 菜单收起 -->
      <div
        class="ml-1 layout-header-trigger layout-header-trigger-min"
        @click="() => emit('update:collapsed', !collapsed)"
      >
        <n-icon v-if="collapsed" size="18">
          <MenuUnfoldOutlined />
        </n-icon>
        <n-icon v-else size="18">
          <MenuFoldOutlined />
        </n-icon>
      </div>
      <!-- 刷新 -->
      <div
        v-if="unref(headerSetting).isReload"
        class="mr-1 layout-header-trigger layout-header-trigger-min"
        @click="reloadPage"
      >
        <n-icon size="18">
          <ReloadOutlined />
        </n-icon>
      </div>
      <!-- 面包屑 -->
      <n-breadcrumb v-if="unref(crumbsSetting).show">
        <template
          v-for="routeItem in breadcrumbList"
          :key="routeItem.name === 'Redirect' ? void 0 : routeItem.name"
        >
          <n-breadcrumb-item v-if="routeItem.meta.title">
            <n-dropdown
              v-if="routeItem.children.length"
              :options="routeItem.children"
              @select="dropdownSelect"
            >
              <span class="link-text">
                <component
                  :is="routeItem.meta.icon"
                  v-if="unref(crumbsSetting).showIcon && routeItem.meta.icon"
                />
                {{ routeItem.meta.title }}
              </span>
            </n-dropdown>
            <span v-else class="link-text">
              <component
                :is="routeItem.meta.icon"
                v-if="unref(crumbsSetting).showIcon && routeItem.meta.icon"
              />
              {{ routeItem.meta.title }}
            </span>
          </n-breadcrumb-item>
        </template>
      </n-breadcrumb>
    </div>
    <div class="layout-header-right">
      <div
        v-for="item in iconList"
        :key="item.icon"
        class="layout-header-trigger layout-header-trigger-min"
      >
        <n-tooltip placement="bottom">
          <template #trigger>
            <n-icon size="18">
              <component :is="item.icon" v-on="item.eventObject || {}" />
            </n-icon>
          </template>
          <span>{{ item.tips }}</span>
        </n-tooltip>
      </div>
      <!--搜索-->
      <div>
        <SearchSecurities />
      </div>
      <!--公告-->
      <div class="layout-header-trigger layout-header-trigger-min">
        <n-tooltip placement="bottom">
          <template #trigger>
            <n-icon>
              <n-badge :value="noticeCount">
                <BulbOutline style="font-size: 18px" @click="openNotice" /> </n-badge
            ></n-icon>
          </template>
          <span>公告</span>
        </n-tooltip>
      </div>
      <!--切换全屏-->
      <div class="layout-header-trigger layout-header-trigger-min">
        <n-tooltip placement="bottom">
          <template #trigger>
            <n-icon size="18">
              <component :is="state.fullscreenIcon" @click="toggleFullScreen" />
            </n-icon>
          </template>
          <span>全屏</span>
        </n-tooltip>
      </div>
      <!-- 个人中心 -->
      <div class="layout-header-trigger layout-header-trigger-min">
        <n-dropdown :options="avatarOptions" trigger="hover" @select="avatarSelect">
          <div class="avatar">
            <n-avatar :size="38" :src="schoolboy" circle />
            <div> &nbsp;{{ userStore.getUserInfo.userName }} </div>
          </div>
        </n-dropdown>
      </div>

      <!--设置-->
      <!--      <div class="layout-header-trigger layout-header-trigger-min" @click="openSetting">-->
      <!--        <n-tooltip placement="bottom-end">-->
      <!--          <template #trigger>-->
      <!--            <n-icon size="18" style="font-weight: bold">-->
      <!--              <SettingOutlined />-->
      <!--            </n-icon>-->
      <!--          </template>-->
      <!--          <span>项目配置</span>-->
      <!--        </n-tooltip>-->
      <!--      </div>-->
      <!-- 密码修改弹窗 -->
      <PasswordChangeModal
        v-model:show="pwdModal"
        @cancel="handlePasswordChangeCancel"
        @success="handlePasswordChangeSuccess"
      />
    </div>
  </div>
  <!--公告弹窗-->
  <n-modal
    v-model:show="noticeModal"
    :bordered="false"
    class="custom-card"
    preset="card"
    style="width: 50%"
  >
    <template #header>
      <n-h2 class="mb-0">公告列表</n-h2>
    </template>
    <template #header-extra>
      <n-button
        v-if="noticeCount > 0"
        quaternary
        type="success"
        @click="update_NoticeReadStatus(null)"
      >
        一键已读全部
      </n-button>
    </template>
    <Notice
      :activeValue="activeValue"
      :noticeList="noticeList"
      :total="total"
      @set-notice-list="setNoticeList"
      @update-notice-read-status="update_NoticeReadStatus"
    />
  </n-modal>

  <!--项目配置-->
  <ProjectSetting ref="drawerSetting" />
</template>

<script lang="ts" setup>
  import { reactive, toRefs, ref, computed, onMounted, unref } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import components from './components';
  import { NDialogProvider, useDialog, useMessage } from 'naive-ui';
  import { CURRENT_ROUTES, TABS_ROUTES } from '@/store/mutation-types';
  import { useUserStore } from '@/store/modules/user';
  import { useScreenLockStore } from '@/store/modules/screenLock';
  import ProjectSetting from './ProjectSetting.vue';
  import Notice from './Notice.vue';
  import { AsideMenu } from '@/layout/components/Menu';
  import SearchSecurities from '@/layout/components/Header/SearchSecurities.vue';
  import schoolboy from '@/assets/images/schoolboy.png';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { websiteConfig } from '@/config/website.config';
  import { PasswordChangeModal } from '@/components/Password';
  import { BulbOutline, Search } from '@vicons/ionicons5';
  import {
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    ReloadOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
  } from '@vicons/antd';
  import { PageRequest } from '@/models/common/baseRequest';
  import { getNoticeList, getUnReadNoticeCount, updateNoticeReadStatus } from '@/api/system/notice';
  import { storage } from '@/utils/common/storage';
  import { SystemNoticeVO } from '@/models/common/basic/noticeModels';

  // 类型定义
  interface NoticeItem {
    noticeId: string;
    title: string;
    content: string;
    readStatus: number;
    createTime: string;
  }

  interface IconItem {
    icon: string;
    tips: string;
    eventObject?: Record<string, (...args: any[]) => any>;
  }

  interface AvatarOption {
    label: string;
    key: number;
  }

  // 定义组件名称
  defineOptions({
    name: 'PageHeader',
  });

  // 定义Props
  interface Props {
    collapsed?: boolean;
    inverted?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    collapsed: false,
    inverted: false,
  });

  // 定义Emits
  interface Emits {
    'update:collapsed': [value: boolean];
  }

  const emit = defineEmits<Emits>();
  // 组合式函数和状态管理
  const userStore = useUserStore();
  const useLockscreen = useScreenLockStore();
  const message = useMessage();
  const dialog = useDialog();
  const router = useRouter();
  const route = useRoute();

  const { navMode, navTheme, headerSetting, menuSetting, crumbsSetting } = useProjectSetting();

  const { name, userId } = userStore?.info || {};

  // 响应式状态
  const drawerSetting = ref();
  const pwdModal = ref(false);
  const noticeCount = ref(0);
  const total = ref(0);
  const noticeModal = ref(false);
  const activeValue = ref<string | null>(null);
  const noticeList = ref<SystemNoticeVO[]>([]);

  const state = reactive({
    username: name ?? '',
    fullscreenIcon: FullscreenOutlined,
  });

  const pageData = reactive({
    size: 10,
    current: 1,
  });

  // 公告相关方法
  const setNoticeList = (size: number, current: number) => {
    pageData.size = size;
    pageData.current = current;
    get_NoticeList();
  };

  // 打开公告
  const openNotice = () => {
    noticeModal.value = true;
  };

  const get_UnReadNoticeCount = async () => {
    try {
      const res = await getUnReadNoticeCount({});
      if (res.code === 200) {
        noticeCount.value = res.data;
      }
    } catch {
      console.error('获取未读公告数量失败');
    }
  };

  // 查询公告
  const get_NoticeList = async () => {
    try {
      const { code, data } = await getNoticeList({
        size: pageData.size,
        current: pageData.current,
      });
      if (code === 200 && data && data.records.length > 0) {
        noticeList.value = data.records;
        total.value = data.total;
        activeValue.value = data.records[0].noticeId;
      } else {
        noticeList.value = [];
        activeValue.value = null;
      }
    } catch {
      console.error('获取公告列表失败');
    }
  };

  // 修改已读状态
  const update_NoticeReadStatus = async (noticeId: string | null) => {
    try {
      const res = await updateNoticeReadStatus({ noticeId, readStatus: 0 });
      if (res.code === 200) {
        await get_NoticeList();
        await get_UnReadNoticeCount();
      } else {
        message.error(res.msg);
      }
    } catch {
      message.error('操作失败');
    }
  };

  // 计算属性
  const getInverted = computed(() => {
    return ['light', 'header-dark'].includes(unref(navTheme)) ? props.inverted : !props.inverted;
  });

  const mixMenu = computed(() => {
    return unref(menuSetting).mixMenu;
  });

  const getChangeStyle = computed(() => {
    const { collapsed } = props;
    const { minMenuWidth, menuWidth } = unref(menuSetting);
    return {
      left: collapsed ? `${minMenuWidth}px` : `${menuWidth}px`,
      width: `calc(100% - ${collapsed ? `${minMenuWidth}px` : `${menuWidth}px`})`,
    };
  });

  const getMenuLocation = computed(() => {
    return 'header';
  });

  // 密码修改相关方法
  const handlePasswordChangeSuccess = () => {
    pwdModal.value = false;
    message.success('密码修改成功，即将跳转到登录页面');
  };

  const handlePasswordChangeCancel = () => {
    pwdModal.value = false;
  };

  // 面包屑相关
  const generator = (routerMap: any[]): any[] => {
    return routerMap.map((item) => {
      const currentMenu = {
        ...item,
        label: item.meta.title,
        key: item.name,
        disabled: item.path === '/',
      };
      // 是否有子菜单，并递归处理
      if (item.children && item.children.length > 0) {
        // Recursion
        currentMenu.children = generator(item.children);
      }
      return currentMenu;
    });
  };

  const breadcrumbList = computed(() => {
    return generator(route.matched);
  });

  const dropdownSelect = (key: string) => {
    router.push({ name: key });
  };
  // 页面操作方法
  const reloadPage = () => {
    router.push({
      path: '/redirect' + unref(route).fullPath,
    });
  };

  // 修改密码
  const changePwd = () => {
    pwdModal.value = true;
  };

  // 退出登录
  const doLogout = () => {
    dialog.info({
      title: '提示',
      content: '您确定要退出登录吗',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await userStore.logout();
          message.success('成功退出登录');
          // 移除标签页
          localStorage.removeItem(TABS_ROUTES);
          localStorage.removeItem(CURRENT_ROUTES);
          await router.replace({
            name: 'Login',
            query: {
              // redirect: route.fullPath,
            },
          });
          setTimeout(() => {
            storage.remove(TABS_ROUTES);
            location.reload();
          }, 1000);
        } catch {
          message.error('退出登录失败');
        }
      },
      onNegativeClick: () => {},
    });
  };

  // 全屏相关方法
  const toggleFullscreenIcon = () => {
    state.fullscreenIcon =
      document.fullscreenElement !== null ? FullscreenExitOutlined : FullscreenOutlined;
  };

  // 全屏切换
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // 配置数据
  const iconList: IconItem[] = [
    // {
    //   icon: 'SearchOutlined',
    //   tips: '搜索',
    // },
    // {
    //   icon: 'GithubOutlined',
    //   tips: 'github',
    //   eventObject: {
    //     click: () => window.open('https://github.com/jekip/naive-ui-admin'),
    //   },
    // },
    // {
    //   icon: 'LockOutlined',
    //   tips: '锁屏',
    //   eventObject: {
    //     click: () => useLockscreen.setLock(true),
    //   },
    // },
  ];

  const avatarOptions: AvatarOption[] = [
    {
      label: '修改密码',
      key: 1,
    },
    {
      label: '退出登录',
      key: 2,
    },
  ];

  // 头像下拉菜单
  const avatarSelect = (key: number) => {
    switch (key) {
      case 1:
        changePwd();
        break;
      case 2:
        doLogout();
        break;
    }
  };

  // 设置相关
  const openSetting = () => {
    const { openDrawer } = drawerSetting.value;
    openDrawer();
  };

  // 生命周期
  onMounted(async () => {
    // 监听全屏切换事件
    document.addEventListener('fullscreenchange', toggleFullscreenIcon);

    // 初始化数据
    await get_UnReadNoticeCount();
    await get_NoticeList();
  });
</script>

<style lang="less" scoped>
  .layout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    height: 64px;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    transition: all 0.2s ease-in-out;
    width: 100%;
    z-index: 11;

    &-left {
      display: flex;
      align-items: center;

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 64px;
        line-height: 64px;
        overflow: hidden;
        white-space: nowrap;
        padding-left: 10px;

        img {
          width: auto;
          margin-right: 10px;
        }

        .title {
          margin-bottom: 0;
        }
      }

      ::v-deep(.ant-breadcrumb span:last-child .link-text) {
        color: #515a6e;
      }

      .n-breadcrumb {
        display: inline-block;
      }

      &-menu {
        color: var(--text-color);
      }
    }

    &-right {
      display: flex;
      align-items: center;
      margin-right: 20px;

      .avatar {
        display: flex;
        align-items: center;
        height: 64px;
      }

      > * {
        cursor: pointer;
      }
    }

    &-trigger {
      display: inline-block;
      width: 64px;
      height: 64px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;

      .n-icon {
        display: flex;
        align-items: center;
        height: 64px;
        line-height: 64px;
      }

      &:hover {
        background: hsla(0, 0%, 100%, 0.08);
      }

      .anticon {
        font-size: 16px;
        color: #515a6e;
      }
    }

    &-trigger-min {
      width: auto;
      padding: 0 12px;
    }
  }

  .layout-header-light {
    background: #fff;
    color: #515a6e;

    .n-icon {
      color: #515a6e;
    }

    .layout-header-left {
      ::v-deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
        color: #515a6e;
      }
    }

    .layout-header-trigger {
      &:hover {
        background: #f8f8f9;
      }
    }
  }

  .layout-header-fix {
    position: fixed;
    top: 0;
    right: 0;
    left: 200px;
    z-index: 11;
  }

  //::v-deep(.menu-router-link) {
  //  color: #515a6e;
  //
  //  &:hover {
  //    color: #1890ff;
  //  }
  //}
</style>
