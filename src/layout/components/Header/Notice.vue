<template>
  <div class="Notice">
    <n-empty description="暂无公告" v-if="noticeList.length === 0">
      <template #extra> </template>
    </n-empty>
    <n-card v-else size="small">
      <template #action>
        <n-space>
          <n-pagination
            class="mt-5"
            v-model:page="pageData.current"
            v-model:page-size="pageData.size"
            @update:page="updatePage"
            @update:page-size="updatePageSize"
            :item-count="total"
            :page-sizes="[10, 20]"
            show-size-picker
          >
            <template #suffix> 共 {{ total }} 条 </template>
          </n-pagination>
        </n-space>
      </template>
      <n-tabs
        v-model:value="activeName"
        class="min-h-[500px]"
        type="line"
        animated
        placement="left"
      >
        <n-tab-pane :name="item.noticeId" v-for="(item, index) in noticeList">
          <template #tab>
            <n-badge :dot="item.readStatus == 1">
              <div
                ><b>{{ item.noticeTitle }}</b></div
              >
            </n-badge>

            <div class="opacity-75"> {{ item.publishTime }}</div>
          </template>
          <div class="text-center">
            <n-h2 class="mb-0">{{ item.noticeTitle }}</n-h2>
            <n-text depth="3">
              {{ item.publishTime }}
            </n-text>
          </div>
          <n-card size="small" embedded :bordered="false">
            <div v-html="item.noticeContent"></div>
          </n-card>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, reactive } from 'vue';
  const activeName = ref('1111');
  const emit = defineEmits(['updateNoticeReadStatus', 'setNoticeList']);
  const props = defineProps({
    noticeList: {
      type: Array,
    },
    activeValue: {
      type: String,
    },
    total: {
      type: Number,
    },
  });

  const pageData = reactive({
    size: 10,
    current: 1,
  });
  watch(
    () => props.activeValue,
    (newVal) => {
      if (newVal) {
        activeName.value = newVal;
        emit('updateNoticeReadStatus', newVal);
      }
    },
    { immediate: true }
  );
  watch(
    () => activeName.value,
    (newVal) => {
      if (newVal) {
        emit('updateNoticeReadStatus', newVal);
      }
    }
  );
  const updatePage = (page) => {
    pageData.current = page;
    emit('setNoticeList', pageData.size, page);
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    emit('setNoticeList', pageSize, pageData.current);
  };
</script>

<style>
  .Notice .n-tabs .n-tabs-tab .n-tabs-tab__label {
    display: block !important;
  }
</style>
