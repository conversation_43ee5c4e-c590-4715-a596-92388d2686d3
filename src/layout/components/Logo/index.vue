<template>
  <div class="logo">
    <img :src="websiteConfig.logo" alt="" :class="{ 'mr-2': !collapsed }" />
    <h2 v-show="!collapsed" class="title">{{ websiteConfig.title }}</h2>
  </div>
</template>

<script setup lang="ts">
  import { websiteConfig } from '@/config/website.config';

  // 定义 props
  interface Props {
    collapsed?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    collapsed: false,
  });
</script>

<style lang="less" scoped>
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64px;
    line-height: 64px;
    overflow: hidden;
    white-space: nowrap;

    img {
      width: auto;
      height: 72px;
    }

    .title {
      margin: 0;
    }
  }
</style>
