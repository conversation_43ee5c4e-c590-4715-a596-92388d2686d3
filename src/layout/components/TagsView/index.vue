<!--
  多标签页视图组件 - 管理页面标签的核心组件
  功能特性：
  1. 多标签页管理：支持标签页的增加、删除、切换
  2. 拖拽排序：支持标签页拖拽重新排序
  3. 滚动导航：标签页过多时支持左右滚动
  4. 右键菜单：提供刷新、关闭等操作
  5. 主题适配：支持明暗主题切换
  6. 缓存机制：页面刷新后保持标签页状态
  7. 固定标签：支持固定重要标签页不被关闭
-->
<template>
  <!-- 标签页容器 - 根据配置动态调整样式和位置 -->
  <div
    class="box-border tabs-view"
    :class="{
      'tabs-view-fix': multiTabsSetting.fixed,
      'tabs-view-fixed-header': isMultiHeaderFixed,
      'tabs-view-default-background': getDarkTheme === false,
      'tabs-view-dark-background': getDarkTheme === true,
    }"
    :style="getChangeStyle"
  >
    <!-- 标签页主体区域 -->
    <div class="tabs-view-main">
      <!-- 标签页卡片容器 - 包含滚动控制和标签页列表 -->
      <div ref="navWrap" class="tabs-card" :class="{ 'tabs-card-scrollable': scrollable }">
        <!-- 左滚动按钮 - 当标签页过多需要滚动时显示 -->
        <span
          class="tabs-card-prev"
          :class="{ 'tabs-card-prev-hide': !scrollable }"
          @click="scrollPrev"
        >
          <n-icon size="16" color="#515a6e">
            <LeftOutlined />
          </n-icon>
        </span>
        <!-- 右滚动按钮 - 当标签页过多需要滚动时显示 -->
        <span
          class="tabs-card-next"
          :class="{ 'tabs-card-next-hide': !scrollable }"
          @click="scrollNext"
        >
          <n-icon size="16" color="#515a6e">
            <RightOutlined />
          </n-icon>
        </span>
        <!-- 标签页滚动区域 - 包含所有标签页 -->
        <div ref="navScroll" class="tabs-card-scroll">
          <!-- 可拖拽的标签页列表 - 支持拖拽排序 -->
          <Draggable :list="tabsList" animation="300" item-key="fullPath" class="flex">
            <template #item="{ element }">
              <div
                :id="`tag${element.fullPath.split('/').join('\/')}`"
                class="tabs-card-scroll-item"
                :class="{
                  'active-item': activeKey === element.fullPath,
                  'pinned-item': isTabPinned(element)
                }"
                @click.stop="goPage(element)"
                @contextmenu="handleContextMenu($event, element)"
              >
                <n-icon
                  size="12"
                  class="pin-icon"
                  v-if="element.pinned"
                  :style="{ color: activeKey === element.fullPath ? '#ffffff' : '#1890ff' }"
                >
                  <PushpinFilled />
                </n-icon>
                <span>{{ element.meta.title }}</span>
                <n-icon
                  size="14"
                  @click.stop="closeTabItem(element)"
                  v-if="!isTabPinned(element)"
                  class="close-icon"
                >
                  <CloseOutlined />
                </n-icon>
              </div>
            </template>
          </Draggable>
        </div>
      </div>
      <!-- 标签页操作下拉菜单按钮 -->
      <div class="tabs-close">
        <n-dropdown
          trigger="hover"
          @select="closeHandleSelect"
          placement="bottom-end"
          :options="TabsMenuOptions"
        >
          <div class="tabs-close-btn">
            <n-icon size="16" color="#515a6e">
              <DownOutlined />
            </n-icon>
          </div>
        </n-dropdown>
      </div>
      <!-- 右键菜单下拉框 - 在标签页上右键时显示 -->
      <n-dropdown
        :show="showDropdown"
        :x="dropdownX"
        :y="dropdownY"
        @clickoutside="onClickOutside"
        placement="bottom-start"
        @select="closeHandleSelect"
        :options="TabsMenuOptions"
      />
    </div>
  </div>
</template>

<script lang="ts">
  // Vue 3 核心功能导入
  import {
    defineComponent,
    reactive,
    computed,
    ref,
    toRefs,
    provide,
    watch,
    onMounted,
    nextTick,
  } from 'vue';
  // 路由相关导入
  import { useRoute, useRouter } from 'vue-router';
  // 本地存储工具
  import { storage } from '@/utils/common/storage';
  import { TABS_ROUTES } from '@/store/mutation-types';
  // 状态管理相关
  import { useTabsViewStore } from '@/store/modules/tabsView';
  import { useAsyncRouteStore } from '@/store/modules/asyncRoute';
  import { RouteItem } from '@/store/modules/tabsView';
  // 项目配置相关
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { useMessage } from 'naive-ui';
  // 拖拽组件
  import Draggable from 'vuedraggable';
  // 页面枚举
  import { PageEnum } from '@/enums/base/pageEnum';
  // 图标组件
  import {
    DownOutlined,
    ReloadOutlined,
    CloseOutlined,
    ColumnWidthOutlined,
    MinusOutlined,
    LeftOutlined,
    RightOutlined,
    PushpinOutlined,
    PushpinFilled,
  } from '@vicons/antd';
  // 工具函数
  import { renderIcon } from '@/utils';
  // 元素尺寸监听器
  import elementResizeDetectorMaker from 'element-resize-detector';
  // 设计系统相关
  import { useDesignSetting } from '@/hooks/setting/useDesignSetting';
  import { useProjectSettingStore } from '@/store/modules/projectSetting';
  import { useThemeVars } from 'naive-ui';
  // 页面跳转工具
  import { useGo } from '@/hooks/web/usePage';

  export default defineComponent({
    name: 'TabsView',
    components: {
      DownOutlined,
      CloseOutlined,
      LeftOutlined,
      RightOutlined,
      PushpinOutlined,
      PushpinFilled,
      Draggable,
    },
    props: {
      // 侧边栏是否折叠 - 影响标签页的位置和宽度
      collapsed: {
        type: Boolean,
      },
    },
    setup(props) {
      // 主题设计相关hooks
      const { getDarkTheme, getAppTheme } = useDesignSetting();
      // 项目配置相关hooks
      const { navMode, headerSetting, menuSetting, multiTabsSetting, isMobile } =
        useProjectSetting();
      const settingStore = useProjectSettingStore();

      // 消息提示
      const message = useMessage();
      // 路由相关
      const route = useRoute();
      const router = useRouter();
      // 状态管理
      const tabsViewStore = useTabsViewStore();
      const asyncRouteStore = useAsyncRouteStore();
      // DOM引用
      const navScroll: any = ref(null); // 标签页滚动容器
      const navWrap: any = ref(null); // 标签页包装容器
      const isCurrent = ref(false); // 是否为当前页面
      const go = useGo(); // 页面跳转工具

      // 主题变量
      const themeVars = useThemeVars();

      // 获取卡片背景色 - 用于标签页背景
      const getCardColor = computed(() => {
        return themeVars.value.cardColor;
      });

      // 获取基础文字颜色 - 用于标签页文字
      const getBaseColor = computed(() => {
        return themeVars.value.textColor1;
      });

      // 组件响应式状态
      const state = reactive({
        activeKey: route.fullPath, // 当前激活的标签页路径
        scrollable: false, // 是否需要滚动（标签页过多时）
        dropdownX: 0, // 右键菜单X坐标
        dropdownY: 0, // 右键菜单Y坐标
        showDropdown: false, // 是否显示右键菜单
        isMultiHeaderFixed: false, // 多标签页头部是否固定
        multiTabsSetting: multiTabsSetting, // 多标签页配置
        contextMenuTarget: null as RouteItem | null, // 当前右键菜单的目标标签页
      });

      /**
       * 获取简化的路由对象 - 提取路由的关键信息用于标签页存储
       * @param route 完整的路由对象
       * @returns 简化的路由信息
       */
      const getSimpleRoute = (route): RouteItem => {
        const { fullPath, hash, meta, name, params, path, query } = route;
        return { fullPath, hash, meta, name, params, path, query };
      };

      // 判断是否为混合菜单且无子菜单模式
      const isMixMenuNoneSub = computed(() => {
        const mixMenu = settingStore.menuSetting.mixMenu;
        const currentRoute = useRoute();
        if (navMode.value != 'horizontal-mix') return true;
        return !(navMode.value === 'horizontal-mix' && mixMenu && currentRoute.meta.isRoot);
      });

      /**
       * 动态计算标签页容器的样式 - 根据菜单状态调整位置和宽度
       * 主要处理：
       * 1. 菜单折叠/展开时的位置调整
       * 2. 移动端适配
       * 3. 固定模式下的宽度计算
       */
      const getChangeStyle = computed(() => {
        const { collapsed } = props;
        const { minMenuWidth, menuWidth }: any = menuSetting.value;
        const { fixed }: any = multiTabsSetting.value;
        // 计算左侧偏移量
        let lenNum =
          navMode.value === 'horizontal' || !isMixMenuNoneSub.value
            ? '0px'
            : collapsed
            ? `${minMenuWidth}px`
            : `${menuWidth}px`;

        // 移动端全宽显示
        if (isMobile.value) {
          return {
            left: '0px',
            width: '100%',
          };
        }
        // 桌面端根据菜单状态调整
        return {
          left: lenNum,
          width: `calc(100% - ${!fixed ? '0px' : lenNum})`,
        };
      });

      /**
       * 标签页右侧下拉菜单配置
       * 提供刷新、关闭、固定等操作选项，根据当前状态动态禁用某些选项
       */
      const TabsMenuOptions = computed(() => {
        const isDisabled = tabsList.value.length <= 1; // 只有一个标签页时禁用关闭操作
        const currentTab = state.contextMenuTarget; // 当前右键点击的标签页
        const isPinned = currentTab ? isTabPinned(currentTab) : false; // 是否已固定
        const isSystemAffix = currentTab?.meta?.affix; // 是否系统固定（不可取消）

        const menuOptions = [
          {
            label: '刷新当前',
            key: '1',
            icon: renderIcon(ReloadOutlined),
          },
        ];

        // 添加固定/取消固定选项
        if (currentTab && !isSystemAffix) {
          menuOptions.push({
            label: isPinned ? '取消固定' : '固定标签页',
            key: '5',
            icon: renderIcon(isPinned ? PushpinOutlined : PushpinFilled),
          });
        }

        // 添加关闭相关选项
        menuOptions.push(
          {
            label: `关闭当前`,
            key: '2',
            disabled: isCurrent.value || isDisabled || isPinned, // 首页、只有一个标签或已固定时禁用
            icon: renderIcon(CloseOutlined),
          },
          {
            label: '关闭其他',
            key: '3',
            disabled: isDisabled,
            icon: renderIcon(ColumnWidthOutlined),
          },
          {
            label: '关闭全部',
            key: '4',
            disabled: isDisabled,
            icon: renderIcon(MinusOutlined),
          }
        );

        return menuOptions;
      });

      // 从本地存储恢复标签页缓存
      let cacheRoutes: RouteItem[] = [];
      const simpleRoute = getSimpleRoute(route);
      try {
        // 尝试从localStorage读取缓存的标签页数据
        const routesStr = storage.get(TABS_ROUTES) as string | null | undefined;
        cacheRoutes = routesStr ? JSON.parse(routesStr) : [simpleRoute];
      } catch (e) {
        // 解析失败时使用当前路由作为默认标签页
        cacheRoutes = [simpleRoute];
      }

      /**
       * 同步路由信息到缓存
       * 确保缓存中的路由信息与最新的路由配置保持一致
       * 主要更新meta信息和name属性
       */
      const routes = router.getRoutes();
      cacheRoutes.forEach((cacheRoute) => {
        const route = routes.find((route) => route.path === cacheRoute.path);
        if (route) {
          cacheRoute.meta = route.meta || cacheRoute.meta;
          cacheRoute.name = (route.name || cacheRoute.name) as string;
        }
      });

      // 初始化标签页存储 - 将缓存的标签页数据加载到store中
      tabsViewStore.initTabs(cacheRoutes);

      /**
       * 监听页面滚动 - 控制标签页固定状态
       * 当页面滚动到一定位置时，如果配置了固定标签页，则显示固定效果
       */
      function onScroll(e) {
        let scrollTop =
          e.target.scrollTop ||
          document.documentElement.scrollTop ||
          window.pageYOffset ||
          document.body.scrollTop; // 获取滚动条偏移量
        // 当头部不固定但标签页固定，且滚动超过64px时，启用固定模式
        state.isMultiHeaderFixed = !!(
          !headerSetting.value.fixed &&
          multiTabsSetting.value.fixed &&
          scrollTop >= 64
        );
      }

      // 注册全局滚动监听
      window.addEventListener('scroll', onScroll, true);

      /**
       * 移除Keep-Alive缓存组件名称
       * 当关闭标签页时，需要从Keep-Alive缓存中移除对应的组件
       * 避免内存泄漏和缓存冲突
       */
      const delKeepAliveCompName = () => {
        if (route.meta.keepAlive) {
          // 查找当前路由对应的组件名称
          const name = router.currentRoute.value.matched.find((item) => item.name == route.name)
            ?.components?.default.name;
          if (name) {
            // 从Keep-Alive组件列表中移除
            const updatedComponents = asyncRouteStore.getKeepAliveComponents.filter(
              (item) => item != name
            );
            asyncRouteStore.setKeepAliveComponents(updatedComponents);
          }
        }
      };

      // 获取标签页列表 - 从store中获取所有标签页
      const tabsList: any = computed(() => tabsViewStore.tabsList);

      // 路由白名单 - 这些页面不会被添加到标签页中
      const whiteList: string[] = [
        PageEnum.BASE_LOGIN_NAME, // 登录页
        PageEnum.REDIRECT_NAME, // 重定向页
        PageEnum.ERROR_PAGE_NAME, // 错误页
      ];

      /**
       * 监听路由变化 - 自动添加新的标签页
       * 当用户导航到新页面时，自动将页面添加到标签页列表中
       */
      watch(
        () => route.fullPath,
        (to) => {
          // 跳过白名单中的页面
          if (whiteList.includes(route.name as string)) return;
          state.activeKey = to; // 更新当前激活的标签页
          tabsViewStore.addTab(getSimpleRoute(route)); // 添加新标签页
          updateNavScroll(true); // 更新滚动状态，自动滚动到新标签页
        },
        { immediate: true } // 立即执行一次
      );

      /**
       * 页面卸载前保存标签页数据
       * 确保用户刷新页面后能恢复之前的标签页状态
       */
      window.addEventListener('beforeunload', () => {
        storage.set(TABS_ROUTES, JSON.stringify(tabsList.value));
      });

      /**
       * 关闭指定标签页
       * @param route 要关闭的路由对象
       */
      const removeTab = (route) => {
        // 防止关闭最后一个标签页
        if (tabsList.value.length === 1) {
          return message.warning('这已经是最后一页，不能再关闭了！');
        }
        delKeepAliveCompName(); // 清理Keep-Alive缓存
        tabsViewStore.closeCurrentTab(route); // 从store中移除标签页

        // 如果关闭的是当前激活的标签页，需要切换到其他标签页
        if (state.activeKey === route.fullPath) {
          const currentRoute = tabsList.value[Math.max(0, tabsList.value.length - 1)];
          state.activeKey = currentRoute.fullPath;
          router.push(currentRoute); // 跳转到新的激活标签页
        }
        updateNavScroll(); // 更新滚动状态
      };

      /**
       * 刷新当前页面
       * 通过重定向页面实现刷新效果，避免整个应用重新加载
       */
      const reloadPage = () => {
        delKeepAliveCompName(); // 清理Keep-Alive缓存
        router.push({
          path: '/redirect' + route.fullPath, // 跳转到重定向页面
        });
      };

      // 向子组件提供刷新页面方法 - 子组件可以通过inject获取
      provide('reloadPage', reloadPage);

      /**
       * 关闭左侧所有标签页
       * @param route 基准路由，关闭此路由左侧的所有标签页
       */
      const closeLeft = (route) => {
        tabsViewStore.closeLeftTabs(route);
        state.activeKey = route.fullPath;
        router.replace(route.fullPath);
        updateNavScroll();
      };

      /**
       * 关闭右侧所有标签页
       * @param route 基准路由，关闭此路由右侧的所有标签页
       */
      const closeRight = (route) => {
        tabsViewStore.closeRightTabs(route);
        state.activeKey = route.fullPath;
        router.replace(route.fullPath);
        updateNavScroll();
      };

      /**
       * 关闭其他所有标签页
       * @param route 保留的路由，关闭除此路由外的所有标签页
       */
      const closeOther = (route) => {
        tabsViewStore.closeOtherTabs(route);
        state.activeKey = route.fullPath;
        router.replace(route.fullPath);
        updateNavScroll();
      };

      /**
       * 关闭所有标签页
       * 关闭所有标签页后跳转到首页
       */
      const closeAll = () => {
        tabsViewStore.closeAllTabs();
        router.replace(PageEnum.BASE_HOME); // 跳转到首页
        updateNavScroll();
      };

      /**
       * 处理标签页操作菜单选择
       * 根据选择的菜单项执行相应的操作
       * @param key 菜单项的key值
       */
      const closeHandleSelect = (key) => {
        const targetRoute = state.contextMenuTarget || route; // 获取目标路由

        switch (key) {
          case '1': // 刷新当前页面
            reloadPage();
            break;
          case '2': // 关闭当前标签页
            removeTab(targetRoute);
            break;
          case '3': // 关闭其他标签页
            closeOther(targetRoute);
            break;
          case '4': // 关闭所有标签页
            closeAll();
            break;
          case '5': // 固定/取消固定标签页
            if (targetRoute) {
              togglePinTab(targetRoute);
            }
            break;
        }
        updateNavScroll(); // 更新滚动状态
        state.showDropdown = false; // 隐藏下拉菜单
        state.contextMenuTarget = null; // 清除右键菜单目标
      };

      /**
       * 平滑滚动到指定位置
       * 使用requestAnimationFrame实现平滑滚动效果
       * @param value 目标滚动位置
       * @param amplitude 每次滚动的步长
       */
      function scrollTo(value: number, amplitude: number) {
        const currentScroll = navScroll.value.scrollLeft;
        // 计算下一次滚动位置
        const scrollWidth =
          (amplitude > 0 && currentScroll + amplitude >= value) ||
          (amplitude < 0 && currentScroll + amplitude <= value)
            ? value
            : currentScroll + amplitude;
        // 执行滚动
        navScroll.value && navScroll.value.scrollTo(scrollWidth, 0);
        // 如果还没到达目标位置，继续滚动
        if (scrollWidth === value) return;
        return window.requestAnimationFrame(() => scrollTo(value, amplitude));
      }

      /**
       * 向左滚动标签页
       * 当标签页过多时，点击左箭头向左滚动
       */
      function scrollPrev() {
        const containerWidth = navScroll.value.offsetWidth; // 容器宽度
        const currentScroll = navScroll.value.scrollLeft; // 当前滚动位置

        if (!currentScroll) return; // 已经在最左侧
        // 计算滚动目标位置
        const scrollLeft = currentScroll > containerWidth ? currentScroll - containerWidth : 0;
        scrollTo(scrollLeft, (scrollLeft - currentScroll) / 20); // 平滑滚动
      }

      /**
       * 滚动到指定位置 - 用于页面内容滚动
       * @param number 滚动位置
       */
      const scrollToTwo = (number) => {
        try {
          let div = document.getElementsByClassName('n-layout-scroll-container')[1];
          div.scrollTop = number;
        } catch (e) {
          // 忽略错误，可能是DOM元素不存在
        }
      };

      /**
       * 向右滚动标签页
       * 当标签页过多时，点击右箭头向右滚动
       */
      function scrollNext() {
        const containerWidth = navScroll.value.offsetWidth; // 容器宽度
        const navWidth = navScroll.value.scrollWidth; // 内容总宽度
        const currentScroll = navScroll.value.scrollLeft; // 当前滚动位置

        if (navWidth - currentScroll <= containerWidth) return; // 已经在最右侧
        // 计算滚动目标位置
        const scrollLeft =
          navWidth - currentScroll > containerWidth * 2
            ? currentScroll + containerWidth
            : navWidth - containerWidth;
        scrollTo(scrollLeft, (scrollLeft - currentScroll) / 20); // 平滑滚动
      }

      /**
       * 更新标签页滚动状态
       * 检查是否需要显示滚动按钮，并可选择自动滚动到当前激活的标签页
       * @param autoScroll 是否自动滚动到当前激活的标签页
       */
      async function updateNavScroll(autoScroll?: boolean) {
        await nextTick(); // 等待DOM更新完成
        if (!navScroll.value) return;

        const containerWidth = navScroll.value.offsetWidth; // 容器可见宽度
        const navWidth = navScroll.value.scrollWidth; // 内容总宽度

        // 判断是否需要滚动
        if (containerWidth < navWidth) {
          state.scrollable = true; // 显示滚动按钮

          // 自动滚动到当前激活的标签页
          if (autoScroll) {
            let tagList = navScroll.value.querySelectorAll('.tabs-card-scroll-item') || [];
            [...tagList].forEach((tag: HTMLElement) => {
              // fix SyntaxError
              if (tag.id === `tag${state.activeKey.split('/').join('\/')}`) {
                tag.scrollIntoView && tag.scrollIntoView();
              }
            });
          }
        } else {
          state.scrollable = false; // 隐藏滚动按钮
        }
      }

      /**
       * 处理容器尺寸变化
       * 当窗口大小改变时重新计算滚动状态
       */
      function handleResize() {
        updateNavScroll(true);
      }

      /**
       * 处理标签页右键菜单
       * @param e 鼠标事件对象
       * @param item 被右键点击的标签页项
       */
      function handleContextMenu(e, item) {
        e.preventDefault(); // 阻止浏览器默认右键菜单
        // 判断是否为首页（首页不能关闭）
        isCurrent.value = PageEnum.BASE_HOME_REDIRECT === item.path;
        state.contextMenuTarget = item; // 保存当前右键点击的标签页
        state.showDropdown = false; // 先隐藏菜单

        // 在下一个tick显示菜单，确保位置正确
        nextTick().then(() => {
          state.showDropdown = true;
          state.dropdownX = e.clientX; // 设置菜单X坐标
          state.dropdownY = e.clientY; // 设置菜单Y坐标
        });
      }

      /**
       * 点击菜单外部时隐藏右键菜单
       */
      function onClickOutside() {
        state.showDropdown = false;
      }

      /**
       * 标签页点击跳转
       * @param e 标签页路由对象
       */
      function goPage(e) {
        const { fullPath } = e;
        if (fullPath === route.fullPath) return; // 如果是当前页面则不跳转
        state.activeKey = fullPath; // 更新激活状态
        go(e, true); // 执行页面跳转
      }

      /**
       * 关闭指定标签页
       * @param e 标签页路由对象
       */
      function closeTabItem(e) {
        const { fullPath } = e;
        const routeInfo = tabsList.value.find((item) => item.fullPath == fullPath);
        removeTab(routeInfo); // 调用关闭标签页方法
      }

      /**
       * 固定/取消固定标签页
       * @param e 标签页路由对象
       */
      function togglePinTab(e) {
        const { fullPath } = e;
        const routeInfo = tabsList.value.find((item) => item.fullPath == fullPath);
        if (routeInfo) {
          if (routeInfo.pinned) {
            tabsViewStore.unpinTab(routeInfo);
            message.success('已取消固定标签页');
          } else {
            tabsViewStore.pinTab(routeInfo);
            message.success('已固定标签页');
          }
        }
      }

      /**
       * 判断标签页是否被固定
       * @param route 标签页路由对象
       * @returns 是否被固定
       */
      function isTabPinned(route) {
        return route.pinned || route?.meta?.affix;
      }

      // 组件挂载后初始化尺寸监听
      onMounted(() => {
        onElementResize();
      });

      /**
       * 初始化元素尺寸监听器
       * 监听标签页容器尺寸变化，自动调整滚动状态
       */
      function onElementResize() {
        let observer;
        observer = elementResizeDetectorMaker(); // 创建尺寸监听器
        observer.listenTo(navWrap.value, handleResize); // 监听容器尺寸变化
      }

      return {
        ...toRefs(state),
        navWrap,
        navScroll,
        route,
        tabsList,
        scrollTo,
        scrollToTwo,
        goPage,
        closeTabItem,
        togglePinTab,
        isTabPinned,
        closeLeft,
        closeRight,
        closeOther,
        closeAll,
        reloadPage,
        getChangeStyle,
        TabsMenuOptions,
        closeHandleSelect,
        scrollNext,
        scrollPrev,
        handleContextMenu,
        onClickOutside,
        getDarkTheme,
        getAppTheme,
        getCardColor,
        getBaseColor,
      };
    },
  });
</script>

<!-- 标签页视图样式 -->
<style lang="less" scoped>
  /* 标签页容器主体样式 */
  .tabs-view {
    width: 100%;
    padding: 6px 0;
    display: flex;
    transition: all 0.2s ease-in-out; /* 平滑过渡效果 */

    /* 标签页主要内容区域 */
    &-main {
      height: 32px;
      display: flex;
      max-width: 100%;
      min-width: 100%;

      /* 标签页卡片容器 */
      .tabs-card {
        -webkit-box-flex: 1;
        flex-grow: 1;
        flex-shrink: 1;
        overflow: hidden;
        position: relative;

        /* 左右滚动按钮样式 */
        .tabs-card-prev,
        .tabs-card-next {
          width: 32px;
          text-align: center;
          position: absolute;
          line-height: 32px;
          cursor: pointer;

          .n-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            width: 32px;
          }
        }

        /* 左滚动按钮位置 */
        .tabs-card-prev {
          left: 0;
        }

        /* 右滚动按钮位置 */
        .tabs-card-next {
          right: 0;
        }

        /* 隐藏滚动按钮 */
        .tabs-card-next-hide,
        .tabs-card-prev-hide {
          display: none;
        }

        /* 标签页滚动区域 */
        &-scroll {
          white-space: nowrap; /* 防止换行 */
          overflow: hidden; /* 隐藏溢出内容 */

          /* 单个标签页项样式 */
          &-item {
            background: v-bind(getCardColor); /* 动态背景色 */
            color: v-bind(getBaseColor); /* 动态文字色 */
            height: 32px;
            padding: 6px 16px 4px;
            border-radius: 3px;
            margin-right: 6px;
            cursor: pointer;
            display: inline-block;
            position: relative;
            flex: 0 0 auto; /* 不伸缩 */

            /* 标签页标题文字 */
            span {
              float: left;
              vertical-align: middle;
            }

            /* Pin图标样式 */
            .pin-icon {
              float: left;
              margin-right: 4px;
              margin-top: 1px;
              vertical-align: middle;
            }

            /* 关闭图标样式 */
            .close-icon {
              height: 22px;
              width: 21px;
              margin-right: -6px;
              position: relative;
              vertical-align: middle;
              text-align: center;
              color: #808695;

              /* 关闭按钮悬停效果 */
              &:hover {
                color: #515a6e !important;
              }

              svg {
                height: 21px;
                display: inline-block;
              }
            }

            /* 鼠标悬停效果 */
            &:hover {
              color: #515a6e;
            }

            /* 通用图标样式（兼容旧版本） */
            .n-icon {
              height: 22px;
              width: 21px;
              margin-right: -6px;
              position: relative;
              vertical-align: middle;
              text-align: center;
              color: #808695;

              /* 图标悬停效果 */
              &:hover {
                color: #515a6e !important;
              }

              svg {
                height: 21px;
                display: inline-block;
              }
            }
          }

          /* 激活状态的标签页 */
          .active-item {
            color: v-bind(getAppTheme); /* 使用主题色 */
          }

          /* 固定状态的标签页 */
          .pinned-item {
            border: 1px solid #1890ff;
            background: rgba(24, 144, 255, 0.05);

            &.active-item {
              background: v-bind(getAppTheme);
              color: #ffffff;
            }
          }
        }
      }

      /* 可滚动状态下的标签页卡片 */
      .tabs-card-scrollable {
        padding: 0 32px; /* 为滚动按钮留出空间 */
        overflow: hidden;
      }
    }

    /* 标签页操作按钮区域 */
    .tabs-close {
      min-width: 32px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: var(--color);
      border-radius: 2px;
      cursor: pointer;

      /* 操作按钮内容 */
      &-btn {
        color: var(--color);
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  /* 浅色主题背景 */
  .tabs-view-default-background {
    background: #f5f7f9;
  }

  /* 深色主题背景 */
  .tabs-view-dark-background {
    background: #101014;
  }

  /* 固定定位模式 */
  .tabs-view-fix {
    position: fixed;
    z-index: 5;
    padding: 6px 10px 6px 10px;
    left: 200px; /* 默认左侧偏移 */
  }

  /* 固定头部模式 */
  .tabs-view-fixed-header {
    top: 0;
  }
</style>
