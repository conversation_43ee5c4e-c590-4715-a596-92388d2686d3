<template>
  <!--  策略名称通用-->
  <div>
    <n-auto-complete
      v-model:value="selectedValues"
      :loading="loading"
      :options="options"
      :placeholder="placeholder || '请输入策略名称'"
      :render-label="renderLabel"
      :style="{ width: width || '234px' }"
      :to="false"
      class="SearchPolicyName"
      clearable
      show-empty
      @select="handleUpdateValue"
      @update:value="handleSearch"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, h } from 'vue';
  import { NIcon, SelectOption, SelectGroupOption, NText, NPopover, NEllipsis } from 'naive-ui';
  import { vagueQueryLevelSrtNames } from '@/api/levelStrategy/levelStrategyPoolApi';
  interface Props {
    placeholder?: string;
    width?: string;
    levelSrtType: number | null;
  }
  const props = defineProps<Props>();

  const renderLabel = (option) => {
    return h(
      NPopover,
      {
        placement: 'top',
      },
      {
        trigger: () =>
          h(
            NEllipsis,
            {
              class: 'w-[500px] block',
            },
            { default: () => option.label }
          ),
        default: option.label,
      }
    );
  };
  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  //搜索
  const handleSearch = (val) => {
    get_PoolPolicyRelateNames(val);
  };
  const fallbackOption = (value) => {
    return { label: '' + value, value };
  };
  const get_PoolPolicyRelateNames = async (levelSrtName: string | null) => {
    emit('getValue', selectedValues.value);
    loading.value = true;
    const { data, code } = await vagueQueryLevelSrtNames(levelSrtName, props.levelSrtType);
    loading.value = false;
    options.value = [];

    if (code == 200) {
      [...new Set(data)].forEach((item: any) => {
        options.value.push({ label: item, value: item });
      });
    }
  };

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
</script>
<style>
  .SearchPolicyName .n-base-select-menu {
    width: 500px !important;
  }
</style>
