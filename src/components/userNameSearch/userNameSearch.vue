<template>
  <!--  用户名称-->
  <div>
    <n-select
      :style="{ width: width || '234px', height: '52px' }"
      v-model:value="selectedValues"
      label-field="label"
      :consistent-menu-width="false"
      value-field="value"
      :placeholder="placeholder || '请输入用户名称'"
      :options="options"
      :loading="loading"
      clearable
      tag
      :filterable="true"
      size="large"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { SelectOption, SelectGroupOption } from 'naive-ui';
  import { vagueQueryUsers } from '@/api/system/user';
  interface Props {
    placeholder?: string;
    width?: string;
  }
  defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  //搜索
  const handleSearch = (val: string) => {
    get_vagueQueryUsers(val);
  };

  const get_vagueQueryUsers = async (userName: string) => {
    loading.value = true;
    const { data, code } = await vagueQueryUsers(userName);
    loading.value = false;
    options.value = [];
    if (code == 200) {
      [...new Set(data)].forEach((item: any) => {
        options.value.push({ label: item, value: item });
      });
    }
  };

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
</script>
<style scoped>
  :deep(.n-base-selection) {
    height: 52px !important;
    border-radius: 12px !important;
    border: 2px solid #e2e8f0 !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
  }

  :deep(.n-base-selection:focus-within) {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  }

  :deep(.n-base-selection .n-base-selection-label) {
    height: 48px !important;
    line-height: 48px !important;
    font-size: 18px !important;
    font-weight: 500 !important;
  }

  :deep(.n-base-selection .n-base-selection-input) {
    height: 48px !important;
    line-height: 48px !important;
    font-size: 18px !important;
    font-weight: 500 !important;
  }

  :deep(.n-base-selection .n-base-selection-placeholder) {
    height: 48px !important;
    line-height: 48px !important;
    font-size: 18px !important;
    font-weight: 500 !important;
  }
</style>
