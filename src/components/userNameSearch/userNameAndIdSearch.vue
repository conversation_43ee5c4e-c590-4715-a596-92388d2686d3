<template>
  <!--用户名称和ID-->
  <n-select
    v-model:value="selectedValues"
    :options="options"
    :style="{ width: width || '234px' }"
    clearable
    filterable
    label-field="userName"
    placeholder="请选择"
    value-field="userId"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  import { getUserList } from '@/api/backTest/backTestingReportApi';
  interface Props {
    width?: string;
  }
  defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  const get_UserList = async () => {
    const { data, code } = await getUserList();
    if (code == 200) {
      options.value = data;
    }
  };
  get_UserList();

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
</script>

<style scoped></style>
