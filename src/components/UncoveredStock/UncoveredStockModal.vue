<!--未囊括股票模态框-->
<template>
  <n-space>
    <n-select
      v-model:value="matrixDate"
      :options="dateOption"
      clearable
      placeholder="请选择日期"
      style="width: 240px"
      @update:value="setmatrixDate()"
    />
    <n-select
      v-model:value="stockType"
      :options="stockOptions"
      clearable
      placeholder="请选择类型"
      style="width: 240px"
      @update:value="setmatrixDate()"
    />
  </n-space>
  <br />
  <n-grid :x-gap="12" :y-gap="10" cols="6" item-responsive>
    <n-grid-item span="3">
      <n-card bordered title="评级分析">
        <strategyDetailBar
          :showLoading="showLoading"
          :xData="xlevel"
          :yData="ylevel"
          height="300px"
          type="level"
        />
      </n-card>
    </n-grid-item>

    <n-grid-item span="3">
      <n-card bordered title="板块分析">
        <echartsPie
          :showLoading="showLoading"
          :xData="xstockType"
          :yData="ystockType"
          height="300px"
        />
      </n-card>
    </n-grid-item>
    <n-grid-item span="6">
      <n-card bordered title="行业分析">
        <strategyDetailBar
          :showLoading="showLoading"
          :xData="xindustry"
          :yData="yindustry"
          height="300px"
          type="industry"
        />
      </n-card>
    </n-grid-item>
    <n-grid-item span="6">
      <n-card bordered>
        <template #header>
          <n-h3 class="mb-0" prefix="bar"> 股票信息</n-h3>
        </template>

        <stockTable :flag="flag" :param="{ matrixDate, matrixId, stockType }" />
      </n-card>
    </n-grid-item>
  </n-grid>
</template>

<script lang="ts" setup>
  import { nextTick, ref, watch } from 'vue';
  import echartsPie from '@/components/pieEcharts/echartsPie.vue';
  import stockTable from '@/components/UncoveredStock/stockTable.vue';
  import strategyDetailBar from '@/views/myDesk/components/PolicyPool/strategyDetailBar.vue';
  import {
    listMatrixUnCoveredStrategyStocksInfo,
    queryMatrixUnCoveredSecAnalysis,
  } from '@/api/matrix/matrixApi';

  const props = defineProps({
    dateOption: [],
    stockTypeOptions: [],
    matrixId: '',
    defaultMatrixDate: '',
    defaultStockType: '',
    flag: '',
  });

  const showLoading = ref(false);
  const loadingTable = ref(false);
  const xlevel = ref([]);
  const ylevel = ref([]);
  const xstockType = ref([]);
  const ystockType = ref([]);
  const xindustry = ref([]);
  const yindustry = ref([]);
  const dataList = ref([]);
  const matrixDate = ref(null);
  const stockType = ref(null);

  const get_MatrixUnCoveredStocks = async () => {
    showLoading.value = true;

    let obj;
    if (props.flag) {
      obj = await listMatrixUnCoveredStrategyStocksInfo({
        matrixDate: matrixDate.value,
        stockType: stockType.value,
        matrixId: props.matrixId,
      });
    } else {
      obj = await queryMatrixUnCoveredSecAnalysis({
        matrixDate: matrixDate.value,
        stockType: stockType.value,
        matrixId: props.matrixId,
      });
    }
    showLoading.value = false;
    const { code, data } = obj;
    if (code === 200) {
      let { level, stockType, industry } = data;
      if (level[0] != null) {
        xlevel.value = level.map(({ typeName }) => typeName);
        ylevel.value = level.map(({ count }) => count);
      }
      if (stockType[0] != null) {
        xstockType.value = stockType.map(({ typeName }) => typeName);
        ystockType.value = stockType.map(({ count }) => count);
      }
      if (industry[0] != null) {
        xindustry.value = industry.map(({ typeName }) => typeName);
        yindustry.value = industry.map(({ count }) => count);
      }
    }
  };

  // const get_MatrixUnCoveredStocksInfo=async () => {
  // loadingTable.value=true;
  //   const {code, data} = await getMatrixUnCoveredStocksInfo();
  //   loadingTable.value=false;
  //
  //   if(code==200){
  //
  //     dataList.value=data;
  //   }
  // }
  const setmatrixDate = () => {
    nextTick(() => {
      get_MatrixUnCoveredStocks();
    });
  };
  watch(
    () => props.dateOption,
    (newVal) => {
      if (newVal) {
        matrixDate.value = newVal[0].value;
      }
    },
    { deep: true, immediate: true }
  );

  watch(
    () => [props.defaultMatrixDate, props.defaultStockType],
    (newArr) => {
      try {
        if (!newArr[0]) {
          matrixDate.value = props.dateOption[0].value;
        } else {
          matrixDate.value = newArr[0];
        }
        stockType.value = newArr[1];
        get_MatrixUnCoveredStocks();
      } catch (e) {
        //console.log(e);
      }
    },
    { deep: true, immediate: true }
  );

  watch(
    () => props.matrixId,
    () => {
      get_MatrixUnCoveredStocks();
    },
    { deep: true }
  );
</script>

<style scoped></style>
