<!--未覆盖股票/表格-->
<template>
  <div>
    <div style="float: right; padding-bottom: 5px">
      <SecurityInfoSearch @get-value="getValue" />
    </div>

    <n-data-table
      :columns="createColumns"
      :data="stockData"
      :loading="loading"
      :scroll-x="5200"
      @update:sorter="handleSorterChange"
    />
    <br />
    <n-space justify="center">
      <n-pagination
        v-show="total > 0"
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条 </template>
      </n-pagination>
    </n-space>

    <!--  同业详情弹框  -->
    <n-modal v-model:show="showDetailModal" class="w-3/4">
      <SecPeerDataTable :isHide="true" :secCode="stockIdTwo" :secName="stockName" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { h, toRefs, ref, onMounted, reactive, watch, computed } from 'vue';
  import { NButton, NDataTable, NIcon, NPopover } from 'naive-ui';
  import {
    getComparisonColor,
    setLevelColor,
    getFinancialColor,
  } from '@/utils/ui/color/LevelColor';
  import { openStockArchives } from '@/utils/goToArchives';
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue';
  import {
    getMatrixUnCoveredSecInfo,
    listMatrixUnCoveredStrategyStocks,
  } from '@/api/matrix/matrixApi';

  // 定义 props
  interface Props {
    param?: any;
    flag?: boolean;
  }
  const props = defineProps<Props>();
  const { param, flag } = toRefs(props);

  // 响应式数据
  const stockData = ref([]);
  const ascOrDesc = ref('');
  const orderBy = ref('');
  const stockId = ref('');
  const stockIdTwo = ref('');
  const stockName = ref('');
  const stockType = ref('');
  const matrixDate = ref('');
  const loading = ref(true);
  const showDetailModal = ref(false);
  const total = ref(0);
  const pageData = reactive({
    size: 10,
    current: 1,
  });
  // 方法定义
  const getDetailModal = (row) => {
    showDetailModal.value = true;
    stockIdTwo.value = row.stockId;
    stockName.value = row.stockName;
  };

  //证券搜索
  const getValue = (val) => {
    stockId.value = val;
    pageData.current = 1;
    get_MatrixUnCoveredStocksInfo();
  };

  // const hasUserLevel= computed(() => stockData.value.some(item => 'userLevel' in item));
  const createColumns = computed(() => {
    const cols = [
      {
        title: '证券代码',
        align: 'center',
        fixed: 'left',
        key: 'secCode',
        width: '100px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.stockName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '证券名称',
        fixed: 'left',
        key: 'stockName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                //console.log(1);
                openStockArchives(row.stockId, row.stockName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.stockName }
          );
        },
      },
      { align: 'center', width: '140px', title: '行业', key: 'industryName' },
      { align: 'center', width: '140px', title: '二级行业', key: 'industryNameTwo' },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '策略评级',
        key: 'afterLevel',
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.afterLevel) },
            },
            { default: () => row.afterLevel }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '塔金评级',
        key: 'level',
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.level) },
            },
            { default: () => row.level }
          );
        },
      },
      {
        align: 'center',
        width: '150px',
        title: () => {
          return [
            '同业分类区间',
            h(
              NPopover,
              {},
              {
                trigger: () => h(NIcon, {}, { default: () => h(HelpCircleOutline) }),
                default: '点击可查看同业详情',
              }
            ),
          ];
        },
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                getDetailModal(row);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.levelRange }
          );
        },
        key: 'levelRange',
      },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: '总市值(亿元)',
        key: 'totalAmount',
      },
      { sorter: true, align: 'center', width: '100px', title: '股价(元)', key: 'closes' },
      {
        sorter: true,
        align: 'center',
        width: '100px',
        title: '财务评分',
        key: 'financialScore',
      },
      { sorter: true, align: 'center', width: '100px', title: '综合评分', key: 'totalScore' },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: () => {
          return h('span', {}, ['市净率', h('br'), '(行业中位数)']);
        },
        key: 'pb',
        render(row) {
          return row.pb
            ? [
                h(
                  'span',
                  {
                    style: { color: getComparisonColor(row.pb, row.midPb) },
                  },
                  { default: () => row.pb }
                ),
                `(${row.midPb || '-'})`,
              ]
            : '-';
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: () => {
          return h('span', {}, ['市盈率', h('br'), '(行业中位数)']);
        },
        key: 'pe',
        render(row) {
          return row.pe
            ? [
                h(
                  'span',
                  {
                    style: { color: getComparisonColor(row.pe, row.midPe) },
                  },
                  { default: () => row.pe }
                ),
                `(${row.midPe || '-'})`,
              ]
            : '-';
        },
      },

      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '每股净资产(元/股)',
        key: 'netAssetValuePerShare',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.netAssetValuePerShare) },
            },
            { default: () => row.netAssetValuePerShare }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '扣非净利润(万元)',
        key: 'kfnetprofit',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.kfnetprofit) },
            },
            { default: () => row.kfnetprofit }
          );
        },
      },
      {
        title: '毛利率',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'grossMarginTrendThreeYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginTrendThreeYear) },
                },
                { default: () => row.grossMarginTrendThreeYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'grossMarginTrendFiveYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginTrendFiveYear) },
                },
                { default: () => row.grossMarginTrendFiveYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'grossMarginSub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginSub) },
                },
                { default: () => row.grossMarginSub }
              );
            },
          },
        ],
      },
      {
        title: '扣非净利润',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'kfnetprofittrendthreeyear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofittrendthreeyear) },
                },
                { default: () => row.kfnetprofittrendthreeyear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'kfnetprofittrendfiveyear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofittrendfiveyear) },
                },
                { default: () => row.kfnetprofittrendfiveyear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'kfnetprofitsub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofitsub) },
                },
                { default: () => row.kfnetprofitsub }
              );
            },
          },
        ],
      },
      {
        title: '营业收入',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'incomeTrendThreeYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeTrendThreeYear) },
                },
                { default: () => row.incomeTrendThreeYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'incomeTrendFiveYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeTrendFiveYear) },
                },
                { default: () => row.incomeTrendFiveYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'incomeSub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeSub) },
                },
                { default: () => row.incomeSub }
              );
            },
          },
        ],
      },
      {
        sorter: true,
        align: 'center',
        width: '170px',
        title: '3年营收负债增速差',
        key: 'incomeDebtSubThreeYear',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.incomeDebtSubThreeYear) },
            },
            { default: () => row.incomeDebtSubThreeYear }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '170px',
        title: '5年营收负债增速差',
        key: 'incomeDebtSubFiveYear',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.incomeDebtSubFiveYear) },
            },
            { default: () => row.incomeDebtSubFiveYear }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '5年亏损次数',
        key: 'losses',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.losses) },
            },
            { default: () => row.losses }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '5年亏损次数',
        key: 'losses',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.losses) },
            },
            { default: () => row.losses }
          );
        },
      },
      { sorter: true, align: 'center', width: '100px', title: '标签数量', key: 'count' },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: '高风险标签数量',
        key: 'highRiskLabelCount',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.highRiskLabelCount) },
            },
            { default: () => row.highRiskLabelCount }
          );
        },
      },
      {
        align: 'center',
        title: '标签内容',
        key: 'labels',
        ellipsis: {
          tooltip: true,
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  // 排序
  const handleSorterChange = (sorter) => {
    if (!sorter.order) {
      ascOrDesc.value = '';
      orderBy.value = '';
    } else {
      ascOrDesc.value = sorter.order == 'ascend' ? 'asc' : 'desc';
      orderBy.value = sorter.columnKey;
    }
    pageData.current = 1;
    get_MatrixUnCoveredStocksInfo();
  };

  // 获取数据
  const get_MatrixUnCoveredStocksInfo = async () => {
    if (flag.value) {
      loading.value = true;
      let { code, data } = await listMatrixUnCoveredStrategyStocks({
        ...param.value,
        ...pageData,
        ascOrDesc: ascOrDesc.value,
        orderBy: orderBy.value,
        stockId: stockId.value,
      });
      loading.value = false;
      if (code === 200) {
        stockData.value = data.records;
        total.value = data.total;
      }
    } else {
      loading.value = true;
      let { code, data } = await getMatrixUnCoveredSecInfo({
        ...param.value,
        ...pageData,
        ascOrDesc: ascOrDesc.value,
        orderBy: orderBy.value,
        secCode: stockId.value,
      });
      loading.value = false;
      if (code === 200) {
        stockData.value = data.records;
        total.value = data.total;
      }
    }
  };

  onMounted(() => {
    // get_MatrixUnCoveredStocksInfo();
  });
  const updatePage = (page) => {
    pageData.current = page;
    get_MatrixUnCoveredStocksInfo();
  };

  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    get_MatrixUnCoveredStocksInfo();
  };

  watch(
    () => props.param,
    (newVal) => {
      if (
        newVal &&
        (newVal.matrixDate != matrixDate.value || newVal.stockType != stockType.value)
      ) {
        matrixDate.value = newVal.matrixDate;
        stockType.value = newVal.stockType;
        pageData.current = 1;
        get_MatrixUnCoveredStocksInfo();
      }
    },
    { deep: true }
  );
</script>

<style scoped></style>
