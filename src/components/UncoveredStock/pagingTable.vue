<template>
  <!--  前端分页-->
  <div>
    <div class="absolute top-[30px] right-[30px]">
      <n-space>
        <StockSearch @get-value="getValue" />
        <!--        <BrokerageSearchOne @getValue="getSource"></BrokerageSearchOne>-->
      </n-space>
    </div>
    <n-data-table
      :columns="createColumns"
      :data="
        dataList
          .slice((pageData.current - 1) * pageData.size, pageData.current * pageData.size)
          .filter((item) => (stockId ? item.stockId == stockId : item))
          .filter((item) => (source ? item.source == source : item))
      "
      :scroll-x="5300"
      @update:sorter="handleSorterChange"
    />
    <br />
    <n-space justify="center">
      <n-pagination
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="
          dataList
            .filter((item) => (stockId ? item.stockId == stockId : item))
            .filter((item) => (source ? item.source == source : item)).length
        "
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix>
          共
          {{
            dataList
              .filter((item) => (stockId ? item.stockId == stockId : item))
              .filter((item) => (source ? item.source == source : item)).length
          }}
          条
        </template>
      </n-pagination>
    </n-space>
    <!--  同业详情弹框  -->
    <n-modal v-model:show="showDetailModal" class="w-3/4">
      <SecPeerDataTable :isHide="true" :secCode="stockIdTwo" :secName="stockName" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { h, toRefs, ref, computed, reactive } from 'vue';
  import { DataTableColumns, NButton, NDataTable, NIcon, NPopover } from 'naive-ui';
  import {
    getComparisonColor,
    setLevelColor,
    getFinancialColor,
  } from '@/utils/ui/color/LevelColor';
  import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue';
  import { openStockArchives } from '@/utils/goToArchives';
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { StockTableInfoVO } from '@/models/common/utilModels';

  const props = defineProps({
    dataList: [],
    type: null,
  });
  const { type } = toRefs(props);
  const stockId = ref('');
  const source = ref('');
  const stockName = ref('');
  const stockIdTwo = ref('');
  const showDetailModal = ref(false);

  const pageData = reactive({
    size: 10,
    current: 1,
  });

  //证券搜索
  const getValue = (val) => {
    stockId.value = val;
    pageData.current = 1;
  }; //证券搜索
  const getSource = (val) => {
    source.value = val;
    pageData.current = 1;
  };
  const getDetailModal = (row) => {
    showDetailModal.value = true;
    stockIdTwo.value = row.stockId;
    stockName.value = row.stockName;
  };
  const createColumns = computed(() => {
    const cols: DataTableColumns<StockTableInfoVO> = [
      {
        title: '证券代码',
        align: 'center',
        fixed: 'left',
        key: 'stockId',
        width: '100px',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.stockId, row.stockName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.stockId }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '证券名称',
        fixed: 'left',
        key: 'stockName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                //console.log(1);
                openStockArchives(row.stockId, row.stockName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.stockName }
          );
        },
      },
      { align: 'center', width: '140px', title: '行业', key: 'industryName' },
      { align: 'center', width: '140px', title: '二级行业', key: 'industryNameTwo' },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '策略评级',
        key: 'afterLevel',
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.afterLevel) },
            },
            { default: () => row.afterLevel }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '塔金评级',
        key: 'level',
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.level) },
            },
            { default: () => row.level }
          );
        },
      },
      {
        align: 'center',
        width: '140px',
        title: () => {
          return [
            '同业分类区间',
            h(
              NPopover,
              {},
              {
                trigger: () => h(NIcon, {}, { default: () => h(HelpCircleOutline) }),
                default: '点击可查看同业详情',
              }
            ),
          ];
        },
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                getDetailModal(row);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.levelRange }
          );
        },
        key: 'levelRange',
      },
      { sorter: true, align: 'center', width: '160px', title: '总市值(亿元)', key: 'totalAmount' },
      { sorter: true, align: 'center', width: '100px', title: '股价(元)', key: 'closes' },
      { sorter: true, align: 'center', width: '100px', title: '财务评分', key: 'financialScore' },
      { sorter: true, align: 'center', width: '100px', title: '综合评分', key: 'totalScore' },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: () => {
          return h('span', {}, ['市净率', h('br'), '(行业中位数)']);
        },
        key: 'pb',
        render(row) {
          return row.pb
            ? [
                h(
                  'span',
                  {
                    style: { color: getComparisonColor(row.pb, row.midPb) },
                  },
                  { default: () => row.pb }
                ),
                `(${row.midPb || '-'})`,
              ]
            : '-';
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: () => {
          return h('span', {}, ['市盈率', h('br'), '(行业中位数)']);
        },
        key: 'pe',
        render(row) {
          return row.pe
            ? [
                h(
                  'span',
                  {
                    style: { color: getComparisonColor(row.pe, row.midPe) },
                  },
                  { default: () => row.pe }
                ),
                `(${row.midPe || '-'})`,
              ]
            : '-';
        },
      },

      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '每股净资产(元/股)',
        key: 'netAssetValuePerShare',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.netAssetValuePerShare) },
            },
            { default: () => row.netAssetValuePerShare }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: '扣非净利润(万元)',
        key: 'kfnetprofit',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.kfnetprofit) },
            },
            { default: () => row.kfnetprofit }
          );
        },
      },
      {
        title: '毛利率',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'grossMarginTrendThreeYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginTrendThreeYear) },
                },
                { default: () => row.grossMarginTrendThreeYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'grossMarginTrendFiveYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginTrendFiveYear) },
                },
                { default: () => row.grossMarginTrendFiveYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'grossMarginSub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.grossMarginSub) },
                },
                { default: () => row.grossMarginSub }
              );
            },
          },
        ],
      },
      {
        title: '扣非净利润',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'kfnetprofittrendthreeyear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofittrendthreeyear) },
                },
                { default: () => row.kfnetprofittrendthreeyear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'kfnetprofittrendfiveyear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofittrendfiveyear) },
                },
                { default: () => row.kfnetprofittrendfiveyear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'kfnetprofitsub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.kfnetprofitsub) },
                },
                { default: () => row.kfnetprofitsub }
              );
            },
          },
        ],
      },
      {
        title: '营业收入',
        key: '',
        align: 'center',
        children: [
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '3年增速(%)',
            key: 'incomeTrendThreeYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeTrendThreeYear) },
                },
                { default: () => row.incomeTrendThreeYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '140px',
            title: '5年增速(%)',
            key: 'incomeTrendFiveYear',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeTrendFiveYear) },
                },
                { default: () => row.incomeTrendFiveYear }
              );
            },
          },
          {
            sorter: true,
            align: 'center',
            width: '160px',
            title: '3年增速-5年增速',
            key: 'incomeSub',
            render(row) {
              return h(
                'span',
                {
                  style: { color: getFinancialColor(row.incomeSub) },
                },
                { default: () => row.incomeSub }
              );
            },
          },
        ],
      },
      {
        sorter: true,
        align: 'center',
        width: '170px',
        title: '3年营收负债增速差',
        key: 'incomeDebtSubThreeYear',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.incomeDebtSubThreeYear) },
            },
            { default: () => row.incomeDebtSubThreeYear }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '170px',
        title: '5年营收负债增速差',
        key: 'incomeDebtSubFiveYear',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.incomeDebtSubFiveYear) },
            },
            { default: () => row.incomeDebtSubFiveYear }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '5年亏损次数',
        key: 'losses',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.losses) },
            },
            { default: () => row.losses }
          );
        },
      },
      {
        sorter: true,
        align: 'center',
        width: '140px',
        title: '5年亏损次数',
        key: 'losses',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.losses) },
            },
            { default: () => row.losses }
          );
        },
      },
      { sorter: true, align: 'center', width: '100px', title: '标签数量', key: 'count' },
      {
        sorter: true,
        align: 'center',
        width: '160px',
        title: '高风险标签数量',
        key: 'highRiskLabelCount',
        render(row) {
          return h(
            'span',
            {
              style: { color: getFinancialColor(row.highRiskLabelCount) },
            },
            { default: () => row.highRiskLabelCount }
          );
        },
      },
      {
        align: 'center',
        title: '标签内容',
        key: 'labels',
        ellipsis: {
          tooltip: true,
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  // 排序
  const handleSorterChange = () => {};

  const updatePage = (page) => {
    pageData.current = page;
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
  };
</script>

<style scoped></style>
