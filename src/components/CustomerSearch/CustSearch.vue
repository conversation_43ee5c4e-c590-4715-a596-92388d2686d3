<template>
  <!--  通用客户搜索封装-->
  <div>
    <n-select
      :style="{ width: width || '234px' }"
      v-model:value="selectedValues"
      filterable
      :placeholder="placeholder || '请输入客户代码或名称'"
      :options="options"
      label-field="custName"
      value-field="custId"
      :render-label="renderLabel"
      :loading="loading"
      clearable
      remote
      :clear-filter-after-select="false"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
    <!--  <n-button @click="transValue"></n-button>-->
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, watch } from 'vue';
  import { queryCustInfo } from '@/views/hualong/api/backend.ts';
  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  interface Props {
    placeholder?: string;
    defaultValue?: string;
    width?: string;
  }
  const props = defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<any>([]);
  const renderLabel = (option) => {
    return [
      option.custName as string,
      h(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.custId,
        }
      ),
    ];
  };
  //搜索
  const handleSearch = (val) => {
    query_CustInfo({ custId: val, size: '30' });
  };

  const query_CustInfo = async (params) => {
    loading.value = true;
    const { data, code } = await queryCustInfo(params);
    loading.value = false;
    if (code == 200) {
      options.value = data.records;
    }
  };
  watch(
    () => props.defaultValue,
    (newVal) => {
      if (newVal && newVal[0]) {
        options.value = [{ custName: newVal[0], custId: newVal[1] }];
        selectedValues.value = newVal[1];
      }
    },
    { deep: true, immediate: true }
  );
  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    //console.log(selectedValues.value);
    emit('getValue', selectedValues.value);
  };
</script>
