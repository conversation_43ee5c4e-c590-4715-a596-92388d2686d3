<!--  行业通用组件(包含子级)-->
<template>
  <n-cascader
    v-model:value="cascaderValue"
    :max-tag-count="props.maxTagCount || 20"
    :options="industryOptions"
    check-strategy="all"
    children-field="tierTwoIndustry"
    clearable
    filterable
    label-field="industryName"
    multiple
    placeholder="请选择行业（可多选）"
    value-field="industryId"
    @update:value="handleIndustryCascaderChange"
  />
</template>

<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { CascaderOption, useMessage } from 'naive-ui';
  import { getTierOneTwoStockIndustry } from '@/api/calculate/calculate';
  import { IndustriesInfoVO } from '@/models/industry/IndustriesInfoModels';

  // 🎯 定义扩展的 CascaderOption 类型
  interface IndustryCascaderOption extends CascaderOption {
    industryId: string;
    industryName: string;
  }

  interface Props {
    /** 默认选中的行业ID数组 */
    defaultSelectedIndustryIds?: string[];
    /** 默认选中的行业名称数组 */
    defaultSelectedIndustryNames?: string[];
    /** 最大显示标签数量，超出部分显示为 +N 形式 */
    maxTagCount?: number | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    defaultSelectedIndustryIds: () => [],
    defaultSelectedIndustryNames: () => [],
    maxTagCount: null,
  });
  /** 级联选择器当前选中的值（行业ID数组） */
  const cascaderValue = ref<string[]>([]);

  /** 行业选项列表，用于级联选择器的数据源 */
  const industryOptions = ref<IndustriesInfoVO[]>([]);

  /**
   * 组件事件定义
   * @description 定义组件向父组件发送的事件及其参数类型
   */
  interface EmitEvents {
    /**
     * 行业选择变化事件
     * @param selectedIndustryIds 选中的行业ID数组
     * @param selectedIndustryNames 选中的行业名称数组
     */
    onIndustrySelectionChange: [selectedIndustryIds: string[], selectedIndustryNames: string[]];
  }

  /** 向父组件发送行业选择变化的事件 */
  const emit = defineEmits<EmitEvents>();

  /** 消息提示实例 */
  const message = useMessage();

  /**
   * 清理二级行业的子级数据
   * @description 将二级行业的 tierTwoIndustry 设为 null，因为二级行业不应该有三级子行业
   * @param secondLevelIndustry 二级行业数据
   */
  const cleanSecondLevelIndustryChildren = (secondLevelIndustry: IndustriesInfoVO) => {
    secondLevelIndustry.tierTwoIndustry = null;
    return secondLevelIndustry;
  };

  /**
   * 标准化行业数据结构
   * @description 处理行业层级数据，清理空的子级数组和多余的嵌套层级
   * @param industryData 原始行业数据
   * @returns 标准化后的行业数据
   */
  const normalizeIndustryData = (industryData: IndustriesInfoVO) => {
    // 进行浅拷贝
    const normalizedIndustry = { ...industryData };
    const tierTwoIndustry = normalizedIndustry.tierTwoIndustry;

    // 如果二级行业列表为空数组，则设为 null（表示没有子级）
    if (tierTwoIndustry != null && tierTwoIndustry.length === 0) {
      normalizedIndustry.tierTwoIndustry = null;
    } else if (tierTwoIndustry != null && tierTwoIndustry.length > 0) {
      // 如果存在二级行业，清理其子级数据（二级行业不应该有三级）
      normalizedIndustry.tierTwoIndustry = tierTwoIndustry.map(cleanSecondLevelIndustryChildren);
    }

    return normalizedIndustry;
  };

  /**
   * 获取并处理一二级行业数据
   * @description 从后端获取行业数据，并进行数据结构标准化处理
   */
  const fetchTierOneTwoStockIndustry = async () => {
    try {
      const response = await getTierOneTwoStockIndustry();

      if (response.code === 200) {
        // 标准化行业数据结构
        industryOptions.value = response.data.map(normalizeIndustryData);
      } else {
        message.error(response.msg || '获取行业数据失败');
      }
    } catch (error) {
      console.error('获取行业数据失败:', error);
      message.error('获取行业数据失败，请稍后重试');
    }
  };

  /**
   * 获取所有行业选项（包括一级和二级行业）
   * @description 扁平化处理行业数据，便于查找和匹配
   * @returns 包含所有行业的扁平化数组
   */
  const getAllIndustryOptions = (): IndustriesInfoVO[] => {
    const result: IndustriesInfoVO[] = [];

    industryOptions.value.forEach((firstLevel) => {
      // 添加一级行业
      result.push(firstLevel);

      // 添加二级行业
      if (firstLevel.tierTwoIndustry && Array.isArray(firstLevel.tierTwoIndustry)) {
        result.push(...firstLevel.tierTwoIndustry);
      }
    });

    return result;
  };

  /**
   * 处理级联选择器值变化事件
   * @description 当用户选择/取消选择行业时触发，将选中的行业数据传递给父组件
   * @param selectedValues 选中的行业ID数组
   * @param selectedOptions 选中的行业选项对象数组
   */
  const handleIndustryCascaderChange = (
    selectedValues: string[],
    selectedOptions: CascaderOption[]
  ) => {
    // 🎯 标记为内部更新，防止 watch 触发循环
    isInternalUpdate.value = true;

    // 🔧 关键修复：同步更新本地状态，确保UI反馈及时显示
    cascaderValue.value = selectedValues;

    // 使用函数式编程提取选中行业的ID和名称
    const selectedIndustryIds = selectedOptions.map(
      (option: IndustryCascaderOption) => option.industryId
    );
    const selectedIndustryNames = selectedOptions.map(
      (option: IndustryCascaderOption) => option.industryName
    );

    // 向父组件发送选中的行业数据
    emit('onIndustrySelectionChange', selectedIndustryIds, selectedIndustryNames);

    // 🎯 重置标志，允许后续的 props 更新
    setTimeout(() => {
      isInternalUpdate.value = false;
    }, 0);
  };

  // 🎯 添加防重复触发标志
  const isInternalUpdate = ref<boolean>(false);

  watch(
    () => props.defaultSelectedIndustryIds,
    (newSelectedIds) => {
      // 🎯 防止内部更新触发无限循环
      if (isInternalUpdate.value) {
        return;
      }

      if (newSelectedIds && newSelectedIds.length > 0) {
        // 🔧 优化：避免不必要的更新，只在值真正改变时更新
        const currentIds = cascaderValue.value || [];
        const hasChanged =
          newSelectedIds.length !== currentIds.length ||
          !newSelectedIds.every((id) => currentIds.includes(id));

        if (hasChanged) {
          cascaderValue.value = [...newSelectedIds];

          // 🎯 标记为内部更新，避免触发父组件事件
          // 这里只更新UI状态，不触发事件，避免循环
        }
      } else if (newSelectedIds && newSelectedIds.length === 0) {
        // 处理清空选择的情况
        cascaderValue.value = [];
      }
    },
    { deep: true, immediate: true }
  );

  onMounted(() => {
    fetchTierOneTwoStockIndustry();
  });
</script>

<style scoped>
  /* 增强级联选择器的视觉反馈 */
  :deep(.n-cascader-menu) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* 选中项的视觉增强 */
  :deep(.n-cascader-option--selected) {
    background-color: #f0f8ff !important;
    color: #1890ff !important;
    font-weight: 500;
  }

  /* 选中项的勾选标记增强 */
  :deep(.n-cascader-option--selected::after) {
    content: '✓';
    position: absolute;
    right: 8px;
    color: #1890ff;
    font-weight: bold;
  }

  /* 悬停效果增强 */
  :deep(.n-cascader-option:hover) {
    background-color: #f5f5f5;
    transition: background-color 0.2s ease;
  }

  /* 多选标签样式优化 */
  :deep(.n-base-selection .n-base-selection-tag) {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }

  /* 输入框聚焦状态增强 */
  :deep(.n-base-selection.n-base-selection--focus) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
</style>
