<template>
  <!--  行业通用组件(不包含子级)-->
  <n-select
    v-model:value="value"
    clearable
    :max-tag-count="2"
    placeholder="请选择行业"
    :options="industryList"
    label-field="industryName"
    value-field="industryName"
    @update:value="handleUpdateValue"
  />
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { getTierOneTwoStockIndustry } from '@/api/calculate/calculate';
  import { useMessage } from 'naive-ui';

  const value = ref<any>(null);

  const options = ref([]);
  const industryList = ref<any>([]);
  const message = useMessage();

  //获取一二级行业
  const get_TierOneTwoStockIndustry = () => {
    getTierOneTwoStockIndustry()
      .then((res) => {
        if (res.code === 200) {
          industryList.value = res.data;
        } else {
          message.error(res.msg);
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };

  onMounted(() => {
    get_TierOneTwoStockIndustry();
  });
  const emit = defineEmits(['getValue']);

  const handleUpdateValue = (value) => {
    emit('getValue', value);
  };
</script>

<style scoped></style>
