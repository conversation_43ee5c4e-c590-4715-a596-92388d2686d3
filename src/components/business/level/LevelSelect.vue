<template>
  <!--  评级通用组件-->
  <n-select
    v-model:value="value"
    :max-tag-count="2"
    :options="levelList"
    clearable
    placeholder="请选择评级"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { defineProps, onMounted, ref, watch } from 'vue';
  import { getUserLevelCriteria } from '@/api/system/user';
  import type { LevelCriteriaDetailDO } from '@/models/common/basic/userModels';
  import { SelectOption } from 'naive-ui';
  import { BasicResponseModel } from '@/models/common/baseResponse';

  interface Props {
    defaultLevel?: string | null;
  }

  const props = defineProps<Props>();

  const value = ref<string | null>(null);
  const loading = ref<boolean>(false);

  /** 评级选项列表 - 用于下拉选择器 */
  const levelList = ref<SelectOption[]>([]);
  watch(
    () => props.defaultLevel,
    (newVal) => {
      value.value = newVal;
    },
    { deep: true, immediate: true }
  );
  const get_UserLevelCriteria = async (): Promise<void> => {
    if (loading.value) {
      return;
    }
    loading.value = true;

    try {
      const res: BasicResponseModel<LevelCriteriaDetailDO[]> = await getUserLevelCriteria(true);

      if (res.code === 200) {
        // 清空现有数据
        levelList.value = [];

        // 过滤并转换数据格式
        res.data
          .filter((item: LevelCriteriaDetailDO) => item.level !== '未分类')
          .forEach((item: LevelCriteriaDetailDO) => {
            levelList.value.push({
              label: item.level,
              value: item.level,
            });
          });
      }
    } catch (error) {
      console.error('获取用户评级标准失败:', error);
    } finally {
      loading.value = false;
    }
  };
  onMounted(() => {
    get_UserLevelCriteria();
  });
  const emit = defineEmits(['getValue']);

  const handleUpdateValue = (value) => {
    emit('getValue', value);
  };
</script>

<style scoped></style>
