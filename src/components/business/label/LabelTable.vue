<!--标签表格组件,用于显示标签列表-->
<template>
  <div>
    <n-spin :show="showLoading">
      <n-grid :x-gap="12" :y-gap="10" cols="6" item-responsive>
        <n-grid-item v-for="(item, index) in labelList" :key="item.name" span="1">
          <n-card>
            <template #header>
              <n-h3 class="mb-0" prefix="bar">
                {{ item.name }}
              </n-h3>
            </template>
            <n-data-table
              ref="tableRef"
              v-model:checked-row-keys="checkedRowKeys[index]"
              :columns="columns"
              :data="item.labels"
              :default-checked-row-keys="getDefaultCheckedRowKeys[index]"
              :row-key="
                (row) => {
                  return row.name;
                }
              "
              max-height="300"
              min-height="300"
              @update:checked-row-keys="updateKey"
            />
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-spin>
  </div>
</template>

<script setup>
  import { nextTick, onMounted, ref, toRefs, watch, h, computed } from 'vue';
  //获取标签列表
  import { NButton, NCard, NDataTable, NPopover, NTag, useMessage } from 'naive-ui';
  import { getLabelConfigs } from '@/api/label/labelConfigApi';

  const labelDescribe = ref('');

  const props = defineProps({
    defaultCheckedRowKeys: [],
    disabledCheckedRowKeys: [],
  });
  const emit = defineEmits(['getValue']);

  const { defaultCheckedRowKeys, disabledCheckedRowKeys } = toRefs(props);
  const message = useMessage();
  const labelList = ref([]);
  const showLoading = ref(true);
  const checkedRowKeys = ref([]);
  const getDefaultCheckedRowKeys = ref([]);
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        disabled(row) {
          return disabledCheckedRowKeys.value
            ? disabledCheckedRowKeys.value.includes(row.name)
            : false;
        },
      },
      {
        title: '标签名称',
        key: 'name',
        align: 'center',
      },
    ];
  });

  const get_LableTypes = async () => {
    showLoading.value = true;
    let { code, data, msg } = await getLabelConfigs();
    showLoading.value = false;

    if (code === 200) {
      labelList.value = data;
      //赋值
      labelList.value.forEach((item, index) => {
        let nameArr = item.labels.map((item) => item.name);
        let commonElements = nameArr.filter((x) => defaultCheckedRowKeys.value.includes(x));
        getDefaultCheckedRowKeys.value[index.toString()] = commonElements;
        checkedRowKeys.value[index.toString()] = commonElements;
      });
    } else {
      message.error(msg);
    }
  };

  const updateKey = (keys, rows) => {
    nextTick(() => {
      let selectLabel = [];

      //获取所有键
      let keys = Object.keys(checkedRowKeys.value);
      // 遍历这些键，并使用它们来获取对应的值
      for (let key of keys) {
        let value = checkedRowKeys.value[key];
        selectLabel = selectLabel.concat(value);
      }
      //给父组件
      emit('getValue', selectLabel);
    });
  };

  onMounted(() => {
    get_LableTypes();
  });
</script>

<style>
  .red {
    color: rgba(128, 0, 30, 0.75) !important;
  }
</style>
