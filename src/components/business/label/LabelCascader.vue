<!--  标签通用组件-->
<template>
  <n-cascader
    v-model:value="selectedLabelValue"
    :check-strategy="checkStrategyIsChild ? 'child' : 'all'"
    :max-tag-count="maxTagCount || 10"
    :multiple="!noMultiple"
    :options="labelOptions"
    :placeholder="noMultiple ? '请选择标签' : '请选择标签（可多选）'"
    children-field="labels"
    clearable
    filterable
    label-field="name"
    value-field="name"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { CascaderOption, useMessage } from 'naive-ui';
  import { getLabelConfigs } from '@/api/label/labelConfigApi';

  interface Props {
    /** 默认选中的标签值 */
    defaultSelectedLabelNames?: string[];
    /** 是否禁用多选模式 */
    noMultiple?: boolean;
    /** 是否只检查子节点策略 */
    checkStrategyIsChild?: boolean;
    /** 最大标签显示数量 */
    maxTagCount?: number | null;
    /** 是否只读模式 */
    readonly?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    maxTagCount: null,
    readonly: false,
  });
  /**
   * 已选标签值
   */
  const selectedLabelValue = ref<(string | number)[]>([]);
  const message = useMessage();

  /**
   * 标签选项列表
   */
  const labelOptions = ref<CascaderOption[]>([]);

  /**
   * 获取标签选项列表
   */
  const fetchLabelOptions = () => {
    getLabelConfigs()
      .then((res) => {
        if (res.code === 200) {
          //console.log(result);
          labelOptions.value = res.data.map((item) => {
            if (item.labels.length === 0) {
              delete item.labels;
            } else {
              item.labels.map((item2) => {
                delete item2.labels;
              });
            }
            return item;
          });
        } else {
          message.error(res.msg);
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };

  onMounted(() => {
    fetchLabelOptions();
  });

  // 定义事件类型 - 新的语义化事件名 + 向后兼容的旧事件名
  const emit = defineEmits<{
    // 新的语义化事件名（推荐使用）
    'selection-changed': [selectedLabels: string[]];
    // 向后兼容的旧事件名
    getValue: [selectedLabels: string[]];
  }>();

  /**
   * 处理标签值更新
   * @param selectedLabelValues 选中的标签值数组
   * @param options 选中的选项对象数组
   */
  const handleUpdateValue = (selectedLabelValues: string[], options: CascaderOption[]) => {
    // 发送新的语义化事件
    emit('selection-changed', selectedLabelValues);

    // 向后兼容：继续发送旧的事件名
    emit('getValue', selectedLabelValues);
  };

  watch(
    () => props.defaultSelectedLabelNames,
    (newVal, oldValue) => {
      if (newVal) {
        selectedLabelValue.value = newVal;
        // 发送新的语义化事件
        emit('selection-changed', newVal);
        // 向后兼容：继续发送旧的事件名
        emit('getValue', newVal);
      }
    },
    { deep: true, immediate: true }
  );

  /**
   * 清空已选标签值
   */
  const clearSelectedLabels = () => {
    selectedLabelValue.value = [];
    // 发送清空事件
    emit('selection-changed', []);
    emit('getValue', []);
  };

  /**
   * 向后兼容的清空方法
   * @deprecated 建议使用 clearSelectedLabels
   */
  const clearValue = () => {
    clearSelectedLabels();
  };

  // 暴露方法给父组件
  defineExpose({
    // 新的语义化方法名
    clearSelectedLabels,
    // 向后兼容的旧方法名
    clearValue,
  });
</script>

<style scoped>
  .label-cascader-wrapper {
    position: relative;
    width: 100%;
  }

  .enhanced-cascader {
    width: 100%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .enhanced-cascader:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .enhanced-cascader :deep(.n-base-selection) {
    border-radius: 8px;
    border-color: #e2e8f0;
    transition: all 0.3s ease;
  }

  .enhanced-cascader :deep(.n-base-selection:hover) {
    border-color: #3b82f6;
  }

  .enhanced-cascader :deep(.n-base-selection.n-base-selection--focus) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .custom-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 500;
    border-radius: 6px;
    animation: tagSlideIn 0.3s ease-out;
  }

  @keyframes tagSlideIn {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .tag-icon {
    font-size: 12px;
    margin-right: 4px;
  }

  .selection-info {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .enhanced-cascader {
      font-size: 14px;
    }

    .enhanced-cascader:hover {
      transform: none;
      box-shadow: none;
    }
  }

  /* 深色主题支持 */
  @media (prefers-color-scheme: dark) {
    .enhanced-cascader :deep(.n-base-selection) {
      border-color: #374151;
    }

    .enhanced-cascader :deep(.n-base-selection:hover) {
      border-color: #60a5fa;
    }
  }
</style>
