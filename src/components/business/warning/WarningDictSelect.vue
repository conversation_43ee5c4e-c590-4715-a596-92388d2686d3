<template>
  <!-- 预警事项组件-->
  <n-select
    v-model:value="value"
    :max-tag-count="2"
    :options="dataList"
    clearable
    filterable
    placeholder="请选择预警事项"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { getHaircutAdjustWarningDict } from '@/api/marginTrading/collateral/collateralBusiness';

  const value = ref<any>(null);

  const options = ref([]);
  const dataList = ref<any>([]);

  //获取
  const get_listCollateralHaircutAdjustWarningDict = () => {
    getHaircutAdjustWarningDict()
      .then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            dataList.value.push({ label: item, value: item });
          });
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };

  onMounted(() => {
    get_listCollateralHaircutAdjustWarningDict();
  });
  const emit = defineEmits(['getValue']);

  const handleUpdateValue = (value) => {
    emit('getValue', value);
  };
</script>

<style scoped></style>
