<template>
  <!--数据表组件-->
  <div>
    <n-data-table
      max-height="500"
      :columns="columns"
      :data="dataList"
      :row-key="(item) => item.tableName"
      virtual-scroll
    />
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue';

  const props = defineProps({
    dataList: [],
  });

  const columns = [
    {
      type: 'selection',
    },
    {
      title: '表名称',
      key: 'tableName',
    },
    {
      title: '表总数',
      key: 'tableRows',
    },
  ];

  onMounted(() => {});
</script>

<style scoped></style>
