<template>
  <!--同步自定义数据管理 / 数据表组件-->
  <div class="original-table">
    <n-card>
      <template #header>
        <n-form ref="formRef" inline label-placement="left">
          <n-space>
            <n-form-item label="表名称">
              <n-input
                v-model:value="formInline.tableName"
                clearable
                placeholder="请输入表名称"
                type="text"
              />
            </n-form-item>
            <n-form-item label="类型">
              <n-input
                v-model:value="formInline.type"
                clearable
                placeholder="请输入类型"
                type="text"
              />
            </n-form-item>
            <n-form-item label="表状态">
              <n-select
                v-model:value="formInline.tableStatus"
                :options="tableStatusOptions"
                clearable
                filterable
              />
            </n-form-item>
            <n-form-item label="是否启用">
              <n-select
                v-model:value="formInline.enableStatus"
                :options="enableStatusOptions"
                clearable
                filterable
              />
            </n-form-item>
            <n-form-item>
              <n-button type="primary" @click="submitForm">查询</n-button>
            </n-form-item>
          </n-space>
        </n-form>
      </template>
      <n-data-table
        :key="loading"
        :bordered="false"
        :checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="dataList"
        :default-checked-row-keys="defaultCheckedRowKeys"
        :loading="loading"
        :pagination="false"
        :row-key="(item) => item.schemaName + '.' + item.tableName"
        children-key="tableList"
        max-height="500px"
        @update:checked-row-keys="handleCheck"
      />
    </n-card>
  </div>
</template>

<script setup>
  import { queryTableMetaData } from '@/api/system/sysManageApi';
  import { h, onMounted, ref } from 'vue';
  import { NButton, useMessage } from 'naive-ui';

  const props = defineProps({
    defaultCheckedRowKeys: [],
  });
  const emit = defineEmits(['getDatabaseList']);
  const formInline = ref({
    tableName: null,
    tableStatus: '0',
    enableStatus: null,
    type: null,
  });
  const message = useMessage();

  const dataList = ref([]);
  const checkedRowKeys = ref(props.defaultCheckedRowKeys);
  const loading = ref(false);
  const columns = [
    {
      type: 'selection',
      disabled(row) {
        // return !row.id;
      },
    },
    {
      title: '表名称',
      key: 'tableName',
      render: (rowData) => {
        return !rowData.dbType
          ? h(
              'span',
              {
                style: 'color: #0085ff;cursor: pointer;padding-left: 15px;',
              },
              '数据库：' + rowData.tableName
            )
          : rowData.tableName;
      },
    },
    {
      title: '表总数',
      width: 100,
      align: 'center',
      key: 'tableRows',
      render: (rowData) => {
        return !rowData.dbType
          ? h(
              NButton,
              {
                size: 'small',
                onClick: () => {
                  rowData.ascending = !rowData.ascending;
                  if (!rowData.ascending) {
                    rowData.tableList.sort((a, b) => b.tableRows - a.tableRows);
                  } else {
                    rowData.tableList.sort((a, b) => a.tableRows - b.tableRows);
                  }
                },
              },
              !rowData.ascending ? '↑' : '↓'
            )
          : rowData.tableRows;
      },
    },
    {
      title: '表注释',
      key: 'tableComment',
    },
  ];

  const tableStatusOptions = ref([
    { label: '启用', value: '0' },
    { label: '禁用', value: '1' },
  ]);
  const enableStatusOptions = ref([
    { label: '启用', value: '0' },
    { label: '禁用', value: '1' },
  ]);

  //选中行
  const handleCheck = (rowKeys) => {
    if (rowKeys) {
      let defaultCheckedKey = rowKeys[rowKeys.length - 1].split('.');
      if (defaultCheckedKey.length > 1 && defaultCheckedKey[0] == defaultCheckedKey[1]) {
        checkedRowKeys.value = [rowKeys[rowKeys.length - 2]];
      } else {
        checkedRowKeys.value = [rowKeys[rowKeys.length - 1]];
      }
    }

    // checkedRowKeys.value = rowKeys;
    // let arr= rowKeys.map((item) => {
    //    let arr = item.split('|');
    //    let result = arr.length > 0 ? arr[0] : item;
    //  return result;
    //  })
    let values = rowKeys.length <= 0 ? null : checkedRowKeys.value[0];
    emit('getDatabaseList', values);
  };

  const query_TableMetaData = async () => {
    loading.value = true;
    const { data, code, msg } = await queryTableMetaData({ ...formInline.value });
    dataList.value = [];
    loading.value = false;
    if (code === 200) {
      data.forEach((item) => {
        item['tableName'] = item.schemaName;
      });
      dataList.value = data;
      //console.log(dataList.value);
    } else {
      message.error(msg);
    }
  };
  const submitForm = () => {
    checkedRowKeys.value = [];
    query_TableMetaData();
  };
  onMounted(() => {
    query_TableMetaData();
  });
</script>

<style scoped></style>
