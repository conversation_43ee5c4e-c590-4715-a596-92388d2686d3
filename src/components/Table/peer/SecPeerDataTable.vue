<!--证券同业券商信息通用表格 - 一般在证券档案中显示-->
<template>
  <n-card>
    <template #header>
      <n-flex>
        <n-h2 class="mb-0" prefix="bar"> 同业详情—{{ secName }}({{ secCode }}) </n-h2>
      </n-flex>
    </template>
    <template #header-extra>
      <n-text class="text-sm" depth="3">
        <!--      更新时间：2024-08-02-->
      </n-text>
    </template>
    <n-flex class="mb-3">
      <BrokerageSearchOne width="200px" @get-value="chooseSource" />
      <n-button type="warning" @click="exportDown">
        导出
        <template #icon>
          <n-icon>
            <CloudUploadOutline />
          </n-icon>
        </template>
      </n-button>
    </n-flex>
    <n-empty v-if="total <= 0" description="无数据" />
    <n-data-table
      v-if="total > 0"
      :columns="columns"
      :data="dataTable"
      :loading="loading"
      :max-height="550"
      :pagination="false"
      :row-key="(item) => item.id"
      :scroll-x="1600"
      :single-line="false"
      bordered
      @update:sorter="handleSorterChange"
    />

    <n-space justify="center">
      <n-pagination
        v-if="total > 0"
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        class="mt-5"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条 </template>
      </n-pagination>
    </n-space>
  </n-card>
</template>

<script lang="ts" setup>
  import { NButton, NDataTable, NIcon, NPopover, NTag, useMessage } from 'naive-ui';
  import { h, reactive, ref, toRefs, watch } from 'vue';
  import { getSecurityPeerData } from '@/api/system/FundInterfaceApi';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { setPeerLogo } from '@/utils/common/resource';
  import { useSorter } from '@/composables/useSorter';
  import { CloudUploadOutline, HelpCircleOutline } from '@vicons/ionicons5';
  import {
    getSecPeerMaintenanceToConcentration,
    securityFilesGetPeerDataInfoExcel,
  } from '@/api/peerSec/peerApi';
  import BrokerageSearchOne from '@/components/BrokerageSearch/BrokerageSearchOne.vue';

  //同业详情导出
  const exportDown = () => {
    const a = document.createElement('a');
    a.target = '_black';
    a.href = securityFilesGetPeerDataInfoExcel() + '?secCode=' + props.secCode;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  const message = useMessage();
  const sourceValue = ref('');

  // 定义 Props 接口
  interface Props {
    /** 证券代码 */
    secCode: string | null;
    /** 证券名称 */
    secName: string | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    secCode: '',
    secName: '',
  });
  const { secCode, secName } = toRefs(props);
  const chooseSource = (name) => {
    sourceValue.value = name;
    pageData.current = 1;
    get_SecurityPeerData();
  };

  const concentrationColumns = [
    {
      title: '集中度',
      key: 'concentration',
    },
    {
      title: '维保比例',
      key: 'mainRatio',
    },
  ];
  const columns = [
    {
      title: '序号',
      align: 'center',
      type: 'index',
      width: '70px',
      render(row, index) {
        return index + 1;
      },
    },
    {
      align: 'center',
      width: '140px',
      title: '券商名称',
      key: 'source',
      render(row) {
        return [
          h('img', {
            style: 'display:inline;border-radius:50%',
            width: '30',
            height: '30',
            src: setPeerLogo(JSON.parse(JSON.stringify(row.source))),
          }),
          ' ' + row.source,
        ];
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '担保品折算率',
      key: 'collateralHaircut',
      sorter: true,
      render(row) {
        return row.collateralHaircut == null ? '-' : row.collateralHaircut + '%';
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '是否担保品',
      key: 'collateral',
      sorter: true,
      render(row) {
        return h(
          NTag,
          {
            type: row.collateral == 0 ? 'success' : 'error',
          },
          {
            default: () => (row.collateral == 0 ? '是' : '否'),
          }
        );
      },
    },
    {
      width: '140px',
      key: 'secCategory',
      align: 'center',
      sorter: true,
      title: () => {
        return [
          '券商分类',
          h(
            NPopover,
            {},
            {
              trigger: () => h(NIcon, {}, { default: () => h(HelpCircleOutline) }),
              default: '括号内数字代表该券商全部分类数量',
            }
          ),
        ];
      },
      render(row) {
        return h(
          'span',
          {
            style: { color: setLevelColor(row.secCategory) },
          },
          {
            default: () =>
              row.secCategory == null && row.groupCount === 0
                ? '-'
                : (row.secCategory == null ? '-' : row.secCategory) +
                  (row.groupCount === 0 ? '-' : '（' + row.groupCount + '）'),
          }
        );
      },
    },
    {
      width: '140px',
      align: 'center',
      title: '上次修改评级',
      key: 'lastGroupAdjustDate',
      sorter: true,
    },
    {
      width: '140px',
      align: 'center',
      title: (row) => {
        return [
          '集中度区间',
          h(
            NPopover,
            {},
            {
              trigger: () => h(NIcon, {}, { default: () => h(HelpCircleOutline) }),
              default: '点击可查看详情',
            }
          ),
        ];
      },
      key: 'concentration',
      render(row) {
        return h(
          NPopover,
          { trigger: 'click', placement: 'right' },
          {
            trigger: () =>
              h(
                'span',
                {
                  onclick: () => {
                    get_CurrentStockPeerMaintenanceToConcentration(
                      JSON.parse(JSON.stringify(row.source))
                    );
                  },
                  style: 'color:#2d8cf0;cursor: pointer;',
                },
                row.concentration
              ),
            default: () =>
              h(NDataTable, {
                size: 'large',
                columns: concentrationColumns,
                data: concentrationData.value,
              }),
          }
        );
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '融资保证金比例',
      key: 'financeMarginRatio',
      sorter: true,
      render(row, text) {
        return row.financeMarginRatio == null ? '-' : row.financeMarginRatio + '%';
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '融券保证金比例',
      key: 'shortMarginRatio',
      sorter: true,
      render(row, text) {
        return row.shortMarginRatio == null ? '-' : row.shortMarginRatio + '%';
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '是否融资标的',
      key: 'financingTarget',
      sorter: true,
      render(row) {
        return h(
          NTag,
          {
            type: row.financingTarget == 0 ? 'success' : 'error',
          },
          {
            default: () => (row.financingTarget == 0 ? '是' : '否'),
          }
        );
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '是否融券标的',
      key: 'shortSellTarget',
      sorter: true,
      render(row) {
        return h(
          NTag,
          {
            type: row.shortSellTarget === 0 ? 'success' : 'error',
          },
          {
            default: () => (row.shortSellTarget == 0 ? '是' : '否'),
          }
        );
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '最新日期',
      key: 'date',
      sorter: true,
      render(row) {
        return row.date == null || row.date === '' ? '-' : row.date;
      },
    },
  ];
  const total = ref(0);
  const loading = ref(false);
  const pageData = reactive({
    size: 10,
    current: 1,
  });
  const { sorting, handleSorterChange } = useSorter(columns);
  const concentrationData = ref([]);
  const dataTable = ref([]);

  const get_CurrentStockPeerMaintenanceToConcentration = async (source) => {
    concentrationData.value = [];
    const parameter = {
      secCode: props.secCode,
      source,
    };
    const { data, code } = await getSecPeerMaintenanceToConcentration(parameter);
    if (code === 200) {
      concentrationData.value = data;
    }
  };
  const get_SecurityPeerData = async () => {
    loading.value = true;
    const params = {
      ...pageData,
      source: sourceValue.value,
      secCode: secCode.value,
      secName: secName.value,
      orderBy: sorting.value.orderBy,
      ascOrDesc: sorting.value.ascOrDesc,
    };
    let { code, data } = await getSecurityPeerData(params);
    if (code === 200) {
      total.value = data.total;
      dataTable.value = data.records;
      loading.value = false;
    }
  };

  watch(
    [() => sorting.value.orderBy, () => sorting.value.ascOrDesc, () => secCode.value],
    () => {
      if (secCode.value) {
        get_SecurityPeerData();
      }
    },
    { deep: true, immediate: true }
  );

  // onMounted(() => {
  //   get_SecurityPeerData();
  // });
  // watch(secCode, () => {
  //   get_SecurityPeerData();
  // });

  const updatePage = (page) => {
    pageData.current = page;
    get_SecurityPeerData();
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    get_SecurityPeerData();
  };
</script>

<style scoped></style>
