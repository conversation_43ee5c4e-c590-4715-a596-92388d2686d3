<!--
  通用股票信息表格组件

  功能特性：
  - 集成分页查询、搜索、排序功能
  - 支持多种预设列配置（标准、策略、池化等）
  - 内置同业详情弹窗和证券搜索
  - 基于 useEnhancedPageQuery Hook 的现代化数据管理
  - 可配置的表格列和额外参数支持
-->
<template>
  <div>
    <!-- 搜索和导出区域 -->
    <div v-if="showSearch || showExport" style="float: right; padding-bottom: 5px">
      <div style="display: flex; align-items: center; gap: 12px">
        <!-- 导出按钮 -->
        <n-button v-if="showExport" type="warning" @click="handleExport">
          {{ exportConfig?.buttonText || '导出Excel' }}
          <template #icon>
            <n-icon>
              <CloudDownloadOutline />
            </n-icon>
          </template>
        </n-button>

        <!-- 搜索框 -->
        <SecurityInfoSearch v-if="showSearch" @on-security-selected="handleSecuritySearch" />
      </div>
    </div>

    <!-- 数据表格 -->
    <n-data-table
      :bordered="false"
      :columns="computedColumns"
      :data="dataList"
      :loading="loading"
      :scroll-x="scrollX"
      :single-line="false"
      @update:sorter="handleSorterChangeWrapper"
    />

    <br />

    <!-- 分页组件 -->
    <n-space justify="center">
      <n-pagination
        v-show="total > 0"
        v-model:page="pageRequest.current"
        v-model:page-size="pageRequest.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条</template>
      </n-pagination>
    </n-space>

    <!-- 同业详情弹框 -->
    <n-modal v-model:show="showDetailModal" class="w-3/4">
      <SecPeerDataTable :secCode="selectedStockId" :secName="selectedStockName" />
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { DataTableColumns, NIcon } from 'naive-ui';
  import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue';
  import SecurityInfoSearch from '@/components/SecuritySearch/SecurityInfoSearch.vue';
  import { StockTableInfoVO } from '@/models/common/utilModels';
  import { PageRequest } from '@/models/common/baseRequest';
  import useEnhancedPageQuery from '@/hooks/useEnhancedPageQuery';
  import { createStockTableColumns } from '@/utils/ui/table/TableColumnFactory';
  import { exportExcel } from '@/api/system/https';
  import { useUserStore } from '@/store/modules/user';
  import { CloudDownloadOutline } from '@vicons/ionicons5';

  // 定义导出配置接口
  interface ExportConfig {
    /** 导出 URL */
    url: string;
    /** 导出按钮文本（可选，默认为"导出Excel"） */
    buttonText?: string;
    /** 导出参数处理函数（可选） */
    paramsHandler?: (params: any) => any;
  }

  // 定义 Props
  interface Props {
    /** 查询函数 - 分离分页参数和业务参数 */
    queryFunction: (pageRequest: PageRequest, businessParams: Record<string, any>) => Promise<any>;
    /** 表格列配置类型 */
    columnType?: 'standard' | 'strategy' | 'pool' | 'custom';
    /** 自定义列配置 */
    customColumns?: DataTableColumns<StockTableInfoVO>;
    /** 是否显示搜索 */
    showSearch?: boolean;
    /** 表格滚动宽度 */
    scrollX?: number;
    /** 是否自动初始化 */
    autoInit?: boolean;
    /** 额外的查询参数 */
    extraParams?: Record<string, any>;
    /** 是否显示导出功能 */
    showExport?: boolean;
    /** 导出配置 */
    exportConfig?: ExportConfig;
  }

  const props = withDefaults(defineProps<Props>(), {
    columnType: 'standard',
    showSearch: true,
    scrollX: 4500,
    autoInit: true,
    extraParams: () => ({}),
    /**
     * 默认导出配置为false，即不提供导出功能
     */
    showExport: false,
  });

  // 定义搜索表单接口
  interface SearchForm {
    secCode?: string | null;
  }

  // 弹窗相关状态
  const selectedStockId = ref<string | null>('');
  const selectedStockName = ref<string | null>('');
  const showDetailModal = ref(false);

  /**
   * 显示股票详情弹窗
   */
  const handleShowStockDetail = (row: StockTableInfoVO) => {
    console.log('show stock detail:', row);
    showDetailModal.value = true;
    selectedStockId.value = row.stockId;
    selectedStockName.value = row.stockName;
  };

  /**
   * 适配器函数：将新的分离参数格式转换为 useEnhancedPageQuery 期望的格式
   */
  const queryFunctionAdapter = async (params: any) => {
    // 提取分页参数
    const pageRequest: PageRequest = {
      current: params.current,
      size: params.size,
      ascOrDesc: params.ascOrDesc,
      orderBy: params.orderBy,
      secCode: params.secCode,
    };

    // 提取业务参数（排除分页相关字段）
    const businessParams = { ...params };
    delete businessParams.current;
    delete businessParams.size;
    delete businessParams.ascOrDesc;
    delete businessParams.orderBy;
    delete businessParams.secCode;

    // 调用原始查询函数，参数分离
    return await props.queryFunction(pageRequest, businessParams);
  };

  // 使用增强版 usePageQuery hook
  const {
    dataList,
    loading,
    total,
    pageRequest,
    searchForm,
    onSubmit,
    updatePage,
    updatePageSize,
    handleSorterChangeWrapper,
    updateSearchForm,
  } = useEnhancedPageQuery<StockTableInfoVO[], SearchForm>(queryFunctionAdapter, {
    autoInit: props.autoInit,
    initialFormData: {
      secCode: null,
    },
    extraParams: props.extraParams,
  });

  /**
   * 证券搜索处理
   */
  const handleSecuritySearch = (secCode: string) => {
    updateSearchForm('secCode', secCode);
    onSubmit();
  };

  /**
   * 计算表格列配置
   */
  const computedColumns = computed(() => {
    if (props.customColumns) {
      return props.customColumns;
    }

    return createStockTableColumns(props.columnType, {
      onShowDetail: handleShowStockDetail,
    });
  });

  /**
   * 处理导出功能
   */
  const handleExport = async () => {
    if (!props.exportConfig) {
      console.warn('导出配置未提供');
      return;
    }

    try {
      // 获取当前查询参数
      let exportParams = {
        ...searchForm,
        ...pageRequest,
        ...props.extraParams,
      };

      // 如果提供了参数处理函数，使用它来处理参数
      if (props.exportConfig.paramsHandler) {
        exportParams = props.exportConfig.paramsHandler(exportParams);
      }

      // 调用导出函数
      await exportExcel(exportParams, props.exportConfig.url);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 暴露方法给父组件
  defineExpose({
    refresh: onSubmit,
    updateSearchForm,
    handleExport,
    dataList,
    loading,
    total,
    pageRequest,
    searchForm,
  });
</script>

<style scoped></style>
