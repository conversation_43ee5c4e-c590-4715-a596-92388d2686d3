<template>
  <!--  前端分页-->
  <div>
    <div style="float: right; padding-bottom: 5px">
      <StockSearch @get-value="getValue" />
      <!--      <LevelSelect style="width: 234px" @getValue="getLevel"></LevelSelect>-->
    </div>
    <n-data-table
      :key="pageData.current"
      :bordered="false"
      :columns="createColumns"
      :data="
        filterTableData
          .filter((item) => (stockId ? item.secCode == stockId : item))
          .filter((item) => (levelValue ? item.level == levelValue : item))
          .slice((pageData.current - 1) * pageData.size, pageData.current * pageData.size)
      "
      :single-line="false"
      @update:sorter="handleSorterChange"
    />
    <br />
    <n-space justify="center">
      <n-pagination
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="
          filterTableData
            .filter((item) => (stockId ? item.secCode == stockId : item))
            .filter((item) => (levelValue ? item.level == levelValue : item)).length
        "
        :page-sizes="[10, 20, 50, 100, 300]"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix>
          共
          {{
            filterTableData
              .filter((item) => (stockId ? item.secCode == stockId : item))
              .filter((item) => (levelValue ? item.level == levelValue : item)).length
          }}
          条
        </template>
      </n-pagination>
    </n-space>
  </div>
</template>

<script lang="ts" setup>
  import { h, defineComponent, toRefs, ref, onMounted, reactive, watch, nextTick } from 'vue';
  import { NButton, NDataTable, NIcon, NPopover } from 'naive-ui';
  import { setLevelColor, getFinancialColor } from '@/utils/ui/color/LevelColor';
  import { openStockArchives } from '@/utils/goToArchives';
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { MatrixCellTableVO } from '@/models/matrix/matrixModels';
  import { Column } from '@/models/common/utilModels';

  const props = defineProps({
    dataList: {
      type: Array as PropType<MatrixCellTableVO[]>,
      required: true,
    },
  });
  const { dataList } = toRefs(props);
  const stockId = ref('');
  const levelValue = ref('');
  const filterTableData = ref<MatrixCellTableVO[]>([]);
  const pageData = reactive({
    size: 10,
    current: 1,
  });

  //证券搜索
  const getValue = (val) => {
    pageData.current = 1;
    pageData.size = 10;
    nextTick(() => {
      stockId.value = val;
    });
  };
  // const hasUserLevel= computed(() => stockData.value.some(item => 'userLevel' in item));
  const createColumns: Column<MatrixCellTableVO>[] = [
    {
      title: '证券代码',
      align: 'center',
      key: 'secCode',
      width: '100px',
      render(row: MatrixCellTableVO) {
        return h(
          NButton,
          {
            onClick: () => {
              openStockArchives(row.secCode, row.secName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.secCode }
        );
      },
    },
    {
      align: 'center',
      width: '100px',
      title: '证券名称',
      key: 'secName',
      render(row: MatrixCellTableVO) {
        return h(
          NButton,
          {
            onClick: () => {
              openStockArchives(row.secCode, row.secName, 1);
            },
            type: 'info',
            strong: true,
            tertiary: true,
            size: 'small',
          },
          { default: () => row.secName }
        );
      },
    },
    {
      align: 'center',
      width: '140px',
      title: '系统评级',
      key: 'level',
      render(row: MatrixCellTableVO) {
        return h(
          'span',
          {
            style: { color: setLevelColor(row.level) },
          },
          { default: () => row.level }
        );
      },
    },
    { sorter: true, align: 'center', width: '160px', title: '总市值(亿元)', key: 'totalAmount' },
    { sorter: true, align: 'center', width: '140px', title: '股价(元)', key: 'closePrice' },
    { align: 'center', width: '100px', title: '行业名称', key: 'industryName' },
    {
      sorter: true,
      align: 'center',
      width: '190px',
      title: '市场融资负债(万元)',
      key: 'marginBalance',
      render(row) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.marginBalance) },
          },
          { default: () => row.marginBalance }
        );
      },
    },
    {
      sorter: true,
      align: 'center',
      width: '180px',
      title: '扣非净利润(亿元)',
      key: 'kfNetProfit',
      render(row) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.kfNetProfit) },
          },
          { default: () => row.kfNetProfit }
        );
      },
    },
    { sorter: true, align: 'center', width: '140px', title: '高风险标签数量', key: 'highRiskNum' },
    {
      sorter: true,
      align: 'center',
      width: '140px',
      title: '每股净资产(元/股)',
      key: 'netAssetValuePerShare',
      render(row: MatrixCellTableVO) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.netAssetValuePerShare) },
          },
          { default: () => row.netAssetValuePerShare }
        );
      },
    },
    {
      sorter: true,
      align: 'center',
      width: '140px',
      title: '3年-5年营收负债增速差(%)',
      key: 'incomeDebtSub',
      render(row) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.incomeDebtSub) },
          },
          { default: () => row.incomeDebtSub }
        );
      },
    },
    {
      sorter: true,
      align: 'center',
      width: '160px',
      title: '3年-5年扣非净利润增速差(%)',
      key: 'kfSub',
      render(row) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.kfSub) },
          },
          { default: () => row.kfSub }
        );
      },
    },
    {
      sorter: true,
      align: 'center',
      width: '160px',
      title: '3年-5年毛利率增速差(%)',
      key: 'grossMarginSub',
      render(row: MatrixCellTableVO) {
        return h(
          'span',
          {
            style: { color: getFinancialColor(row.grossMarginSub) },
          },
          { default: () => row.grossMarginSub }
        );
      },
    },
  ];

  // 排序
  const handleSorterChange = (sorter) => {
    //console.log(sorter);
    //console.log(sorter.order);
    if (sorter.order) {
      filterTableData.value.sort((a, b) => {
        return sorter.order == 'ascend'
          ? b[sorter.columnKey] - a[sorter.columnKey]
          : a[sorter.columnKey] - b[sorter.columnKey];
      });
    } else {
      filterTableData.value = dataList.value as any;
    }
  };
  const getLevel = (level) => {
    levelValue.value = level;
  };

  watch(
    () => props.dataList,
    (newVal) => {
      filterTableData.value = [...(newVal as any)] as any;
    },
    { deep: true, immediate: true }
  );

  const updatePage = (page) => {
    pageData.current = page;
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
  };
</script>

<style scoped></style>
