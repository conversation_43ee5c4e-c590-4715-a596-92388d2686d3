<template>
  <!--预警列表通用表格组件， 我的工作台->预警列表  我的工作台->每日简报->预警列表   股票档案-》预警信息-->
  <div>
    <n-data-table
      :columns="columns"
      :data="riskWarningInfoTable"
      :loading="loading"
      :max-height="550"
      :min-height="550"
      :pagination="false"
      :row-key="(item) => item.id"
      :scroll-x="800"
      :single-line="false"
      bordered
      @update:sorter="handleSorterChange"
    />

    <n-space justify="center">
      <n-pagination
        v-model:page="pageData.current"
        v-model:page-size="pageData.size"
        :item-count="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        class="mt-5"
        show-size-picker
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #suffix> 共 {{ total }} 条 </template>
      </n-pagination>
    </n-space>
  </div>
</template>

<script lang="ts" setup>
  import { computed, h, reactive, ref, watch } from 'vue';
  import { NA, NButton, useMessage } from 'naive-ui';
  import { useSorter } from '@/composables/useSorter';
  import { useUserStore } from '@/store/modules/user';
  import { getSecRiskWarningList } from '@/api/label/riskWarningApi';
  import { formatTime } from '@/utils/common/date/dateUtil';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { openStockArchives } from '@/utils/goToArchives';
  import { queryLabelDetail } from '@/api/label/riskWarningApi';
  import { RiskWarningVO } from '@/models/label/riskWarningModels';

  const emit = defineEmits(['updateLevel']);
  const props = defineProps({
    params: {},
    hiddenFields: {
      type: Array,
      default: () => [],
    },
    updateNum: null,
  });
  const loading = ref(false);
  const message = useMessage();

  const pageData = reactive({
    size: 10,
    current: 1,
  });

  const total = ref(0);
  const riskWarningInfoTable = ref<RiskWarningVO[]>([]);
  const userStore = useUserStore();
  const ifSystemUser = userStore.getIfSystemUser;
  const columns = computed(() => {
    const cols = [
      {
        title: '序号',
        align: 'center',
        type: 'index',
        width: '70px',
        render(row, index) {
          return index + 1;
        },
      },
      {
        title: '证券代码',
        align: 'center',
        key: 'secCode',
        width: '120px',
        sorter: true,
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secCode }
          );
        },
      },
      {
        align: 'center',
        width: '100px',
        title: '证券名称',
        key: 'secName',
        render(row) {
          return h(
            NButton,
            {
              onClick: () => {
                openStockArchives(row.secCode, row.secName, 1);
              },
              type: 'info',
              strong: true,
              tertiary: true,
              size: 'small',
            },
            { default: () => row.secName }
          );
        },
      },
      {
        align: 'center',
        width: props.hiddenFields.includes('level') ? '0' : '120',
        title: '系统评级',
        key: 'level',
        sorter: true,
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.level) },
            },
            { default: () => row.level }
          );
        },
      },
      {
        align: 'center',
        width: props.hiddenFields.includes('userLevel') ? '0' : !ifSystemUser ? '120px' : '0',
        title: '用户评级',
        key: 'userLevel',
        sorter: true,
        render(row) {
          return h(
            'span',
            {
              style: { color: setLevelColor(row.userLevel) },
            },
            { default: () => row.userLevel }
          );
        },
      },
      { align: 'center', width: '140px', title: '预警内容', key: 'labelName' },
      { align: 'center', width: '120px', title: '预警类型', key: 'labelType' },
      {
        align: 'center',
        title: '预警时间',
        width: '140px',
        key: 'date',
        disabled: true,
        sorter: true,
        render(row) {
          return h('span', {}, { default: () => formatTime(row.date) });
        },
      },
      {
        align: 'center',
        width: props.hiddenFields.includes('infoDescribe') ? '0' : '',

        title: '预警摘要',
        key: 'infoDescribe',
        render(row) {
          return h(
            NA,
            {
              onClick: () => {
                linkClick(row);
              },
            },
            { default: () => row.infoDescribe }
          );
        },
      },

      {
        title: '操作',
        width: props.hiddenFields.includes('actions') ? '0' : '130',
        key: 'actions',
        fixed: 'right',
        render(row) {
          return [
            // h(
            //   NButton,
            //   {
            //     type: 'info',
            //     size: 'small',
            //     onClick: () => {
            //       openEventTrace(row.secCode, row.secName, 2);
            //     },
            //   },
            //   { default: () => '事件跟踪' }
            // ),
            // ' ',
            h(
              NButton,
              {
                type: 'warning',
                size: 'small',
                onClick: () => {
                  emit('updateLevel', row);
                },
              },
              { default: () => '修改评级' }
            ),
          ];
        },
      },
    ];
    return cols.filter((i) => i.width != '0');
  });

  const { sorting, handleSorterChange } = useSorter(pageData);
  watch(
    () => sorting.value,
    () => {
      get_SecRiskWarningList();
    },
    { deep: true }
  );
  //公告内容跳转链接
  const linkClick = (row) => {
    let data = {
      secCode: row.secCode,
      labelName: row.labelName,
      infoDescribe: row.infoDescribe,
      date: row.date,
    };
    queryLabelDetail(data)
      .then((res) => {
        if (res.code === 200) {
          if (res.data) {
            window.open(res.data);
          } else {
            message.error('无详细内容');
          }
        } else {
          message.error(res.msg);
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };
  //获取预警列表详情
  const get_SecRiskWarningList = async () => {
    if (loading.value) {
      return;
    }
    loading.value = true;
    let { code, data, msg } = await getSecRiskWarningList({
      ...(props.params || {}),
      ...pageData,
      orderBy: sorting.value.orderBy,
      ascOrDesc: sorting.value.ascOrDesc,
    });
    loading.value = false;

    if (code === 200) {
      riskWarningInfoTable.value = data.records;
      total.value = data.total;
    } else {
      message.error(msg);
    }
  };

  watch(
    () => props.updateNum,
    () => {
      pageData.current = 1;
      get_SecRiskWarningList();
    }
  );

  const updatePage = (page) => {
    pageData.current = page;
    get_SecRiskWarningList();
  };
  const updatePageSize = (pageSize) => {
    pageData.current = 1;
    pageData.size = pageSize;
    get_SecRiskWarningList();
  };
</script>

<style scoped></style>
