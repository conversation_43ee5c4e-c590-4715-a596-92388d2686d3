<template>
<!--  图表重合时，显示减少或增加的数值-->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script lang="ts">
import {defineComponent, ref, Ref, toRefs, watch} from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
export default defineComponent({
  props: ['xData','yData','yData2','showLoading','height','width'],
  setup(props) {
    const {xData, yData,yData2,showLoading,height,width}=toRefs(props)
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
    //渲染图
    const echartsDraw=()=> {
      let xAxisData = xData.value;
      let data1 = [...yData.value];
      let data2 = [...yData2.value];
      if(data2.length>0){
        try{


          data1.forEach((item,index)=>{
            let i=parseInt(item);
            let y=parseInt(data2[index]);
            if(i>= y){
              data1[index]=i-(i-y);
              data2[index]={
                name: '减少了',
                value:   i-y,
                itemStyle: {
                  color: '#f10d2f'
                },
              } ;
            }else{

              data1[index]=y;
              data2[index]= {
                name: '增加了',
                value: y-i,
                itemStyle: {
                  color: '#29a807'
                },
              } ;
            }
          })  }catch (e) {

        }
      }

      let data3 = <any>[];
      for(let i=0; i<data1.length; i++){
        data3.push(data1[i] + data2[i]||'');
      }
      setOptions(
        {
          tooltip: {},
          xAxis: {
            // x轴文字倾斜
            axisLabel:{
              interval:0,
              rotate:65,//倾斜度 -90 至 90 默认为0
              margin:10,
              textStyle:{
                fontWeight:"bolder",
              }
            },
            data: xAxisData,
            name: '',
            axisLine: { onZero: true },
            splitLine: { show: false },
            splitArea: { show: false }
          },
          yAxis: {},
          grid: {
            bottom: 100
          },
          series: [
            {
              name: '数值',
              type: 'bar',
              stack: 'one',
              color: 'rgb(12,143,224)',
              data: data1,

            },
            {

              type: 'bar',
              stack: 'one',
              color: 'rgb(234,43,43)',
              data: data2,
              itemStyle: {
                normal: {
                  label: {
                    formatter:  function (value, index) {
                      return value.value!=0? (value.name=='减少了'?'-':'+')+value.value :'';
                    },
                    show: true,
                    position: "top",
                    textStyle: {
                      fontWeight: "bolder",
                      fontSize: "12",
                      color: "#1f1717"
                    }
                  },

                }
              }
              //不同系列的柱间距离，为百分比,如果想要两个系列的柱子重叠，可以设置 barGap 为 '-100%'。
            },

          ]
        }
      );

    }


    watch(
      () => [props.yData2,props.xData],
      () => {
        //console.log(2)
        echartsDraw()
      },
      {
        deep: true,immediate:true
      }
    );
    // onMounted(()=>{
    //   echartsDraw();
    // })
    return { chartRef,echartsDraw,showLoading,height,width};
  },
});
</script>

<style scoped>

</style>
