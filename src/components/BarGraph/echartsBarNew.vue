<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width: '100%' }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, Ref, toRefs, watch, onMounted } from 'vue';

  import { useECharts } from '@/hooks/web/useECharts';
  import { getSrtIndustryAnalysis } from '@/api/levelStrategy/levelStrategyPoolApi';
  import { useMessage } from 'naive-ui';

  export default defineComponent({
    props: ['id', 'height', 'labelColor', 'isOne', 'labelName'],
    emits: ['parentMethod'],
    setup(props) {
      const { id, height, labelColor, isOne, labelName } = toRefs(props);
      const showLoading = ref(true);
      const message = useMessage();

      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
      //渲染图
      const echartsDraw = async () => {
        showLoading.value = true;
        const { data, code, msg } = await getSrtIndustryAnalysis(id.value, labelName.value);
        showLoading.value = false;

        if (code === 200) {
          let x = [];
          let y = [];
          if (data.length <= 0) {
          } else {
            let { industry } = data;

            const mapData = (data, key) => {
              x = data.map((item) => item[key]);
              y = data.map((item) => item.count);
            };

            mapData(industry, 'typeName');

            if (isOne.value) {
              y = y.slice(0, isOne.value);
              x = x.slice(0, isOne.value);
            }
          }
          setOptions({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
              },
            },
            xAxis: {
              // x轴文字倾斜
              axisLabel: {
                interval: 0,
                rotate: 45, //倾斜度 -90 至 90 默认为0
                margin: 10,
                textStyle: {
                  fontWeight: 'bolder',
                },
              },
              type: 'category',
              data: x,
              axisTick: {
                alignWithLabel: true,
              },
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                data: y,
                type: 'bar',
                color: [labelName.value ? '#ec3535' : ''],
              },
            ],
          });
        } else {
          message.error(msg);
        }
      };

      watch(
        () => [props.id],
        () => {
          echartsDraw();
        },
        {
          deep: true,
        }
      );
      onMounted(() => {
        echartsDraw();
      });
      return { chartRef, echartsDraw, showLoading, height };
    },
  });
</script>

<style scoped></style>
