<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, Ref, toRefs, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  export default defineComponent({
    props: ['xData', 'yData', 'height', 'width', 'showLoading'],
    setup(props) {
      const { xData, yData, height, showLoading, width } = toRefs(props);
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
      //渲染图
      const echartsDraw = () => {
        setOptions({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          grid: {
            x: 0,
            y: 10,
            y2: 20,
            containLabel: true,
          },
          xAxis: {
            interval: 22,
          },
          yAxis: {
            type: 'category',

            data: xData.value,
          },
          series: [
            {
              data: yData.value,
              type: 'bar',
            },
          ],
        });
      };

      watch(
        () => [props.xData],
        () => {
          echartsDraw();
        },
        {
          deep: true,
        }
      );
      // onMounted(()=>{
      //   echartsDraw();
      // })
      return { chartRef, echartsDraw, showLoading, height, width };
    },
  });
</script>

<style scoped></style>
