<template>
  <!--
  多个柱状图通用组件
  xData 格式： ['2019-01', '2019-02', '2019-03', '2019-04', '2019-05', '2019-06', '2019-07']
 seriesData 格式： {
      name:'平均值',
      data: [120, 200, 150, 80, 70, 110, 130],
    }, {
       name:'市值',
      data: [120, 200, 150, 80, 70, 110, 130],
    }

-->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, Ref, toRefs, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { setBarColor } from '@/utils/ui/color/colorUtil'

  export default defineComponent({
    props: ['height', 'width', 'showLoading', 'xData', 'seriesData'],
    setup(props) {
      const { height, showLoading, xData, width, seriesData } = toRefs(props);
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
      //渲染图
      const echartsDraw = () => {
        setOptions({
          tooltip: {
            trigger: 'axis',
          },
          legend: {},
          grid: {
            left: '2%',
            right: '2%',
            bottom: '2%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: xData.value,
          },
          yAxis: {
            name: '',
            type: 'value',
          },
          series: seriesData.value.map((item) => {
            return {
              name: item.name,
              data: item.data,
              type: 'bar',
            };
          }),
        });
      };

      watch(
        () => [props.xData],
        () => {
          echartsDraw();
        },
        {
          deep: true,
        }
      );
      // onMounted(()=>{
      //   echartsDraw();
      // })
      return { chartRef, echartsDraw, showLoading, height, width };
    },
  });
</script>

<style scoped></style>
