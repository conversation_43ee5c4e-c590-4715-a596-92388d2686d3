<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width: '100%' }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { ref, Ref, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  // 定义 props
  interface Props {
    xData: string[];
    yData: number[];
    height: string;
    showLoading: boolean;
  }

  const props = defineProps<Props>();

  // 定义 emits
  const emit = defineEmits<{
    parentMethod: [value: string];
  }>();

  // 响应式数据
  const chartRef = ref<HTMLDivElement | null>(null);
  const IndustryName = ref(999);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 渲染图表
  const echartsDraw = () => {
    const myChart = getInstance();

    setOptions({
      color: ['#409eff'],
      title: {
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: props.xData,
        axisLabel: {
          interval: 0,
          rotate: 300,
          color: (_: any, index: number) => {
            return index === IndustryName.value ? '#fc0909' : '#6e6a6a';
          },
        },
        splitArea: {
          show: false,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '支',
      },
      series: [
        {
          data: props.yData,
          type: 'bar',
          smooth: true,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' },
            ],
          },
          markLine: {
            data: [{ type: 'average', name: '平均值' }],
          },
          itemStyle: {
            normal: {
              label: {
                show: true,
                position: 'top',
                textStyle: {},
              },
            },
          },
        },
      ],
    });

    // 点击事件
    myChart?.off('click');
    myChart?.on('click', (params: any) => {
      const { name, dataIndex } = params;
      if (params.name.indexOf('亿') !== -1) {
        emit('parentMethod', '');
        IndustryName.value = 999;
      } else if (IndustryName.value === dataIndex) {
        emit('parentMethod', '');
        IndustryName.value = 999;
      } else {
        emit('parentMethod', name);
        IndustryName.value = dataIndex;
      }
      // 刷新
      myChart?.resize();
    });
  };

  // 监听数据变化
  watch(
    () => props.xData,
    () => {
      echartsDraw();
    },
    {
      deep: true,
    }
  );
</script>

<style scoped></style>
