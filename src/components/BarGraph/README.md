# FinancialBarChart 金融柱状图组件

## 📋 概述

`FinancialBarChart` 是一个基于 Vue 3 + TypeScript + ECharts 开发的专业金融数据可视化组件，专门用于展示金融业务中的各类数据分布，如评级分布、股价分布、行业分布、市值分布等。

## ✨ 特性

- 🎯 **专业金融场景**：针对金融业务优化的图表展示
- 🎨 **动态颜色主题**：支持多种业务类型的颜色配置
- 📊 **数据缩放功能**：支持大数据量的缩放浏览
- 📱 **响应式设计**：适配不同屏幕尺寸
- ♿ **无障碍访问**：支持屏幕阅读器和键盘导航
- 🔧 **高度可配置**：丰富的配置选项满足不同需求
- 📈 **性能优化**：智能渲染和内存管理
- 🧪 **完整测试**：100% 测试覆盖率

## 🚀 快速开始

### 基础用法

```vue
<template>
  <FinancialBarChart
    :x-data="['AAA', 'AA', 'A', 'BBB']"
    :y-data="[45, 67, 89, 123]"
    type="level"
    x-name="评级"
    y-name="数量"
    height="320px"
  />
</template>

<script setup>
import FinancialBarChart from '@/components/BarGraph/FinancialBarChart.vue';
</script>
```

### 高级用法

```vue
<template>
  <FinancialBarChart
    :x-data="industryData"
    :y-data="industryValues"
    type="industry"
    :data-zoom="true"
    :end-value="10"
    :show-loading="loading"
    :grid-padding="{ left: 30, top: 40 }"
    @chart-click="handleClick"
    @chart-rendered="handleRendered"
  />
</template>
```

## 📚 API 文档

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `xData` | `string[] \| number[]` | `[]` | 横轴数据（分类数据） |
| `yData` | `number[]` | `[]` | 纵轴数据（数值数据） |
| `height` | `string` | `'300px'` | 图表高度 |
| `width` | `string` | `'100%'` | 图表宽度 |
| `showLoading` | `boolean` | `false` | 加载状态 |
| `type` | `ChartType \| string` | `'ttm'` | 图表类型（决定颜色主题） |
| `dataZoom` | `boolean` | `false` | 是否启用数据缩放 |
| `endValue` | `number` | `20` | 数据缩放显示条数 |
| `xName` | `string` | `''` | 横轴名称 |
| `yName` | `string` | `''` | 纵轴名称 |
| `showLabels` | `boolean` | `true` | 是否显示数据标签 |
| `labelFontSize` | `string` | `'10px'` | 数据标签字体大小 |
| `gridPadding` | `GridPadding` | `{left:20,top:30,right:20,bottom:20}` | 网格边距配置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `chart-click` | `ChartClickData` | 图表点击事件 |
| `chart-rendered` | - | 图表渲染完成事件 |
| `data-updated` | `DataUpdatedData` | 图表数据更新事件 |

### Expose Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `refresh` | - | `void` | 手动刷新图表 |
| `resize` | - | `void` | 调整图表尺寸 |
| `getInstance` | - | `EChartsInstance` | 获取 ECharts 实例 |

## 🎨 图表类型

支持以下预定义的图表类型：

- `level` - 评级分布
- `closes` - 股价分布  
- `industry` - 行业分布
- `ttm` - 市值分布
- `zsl` - 总市值分布
- `rzbzj` - 融资保证金分布
- `rqbzj` - 融券保证金分布

## 📦 预设配置

使用预设配置快速创建常见图表：

```vue
<script setup>
import { CHART_PRESETS } from '@/components/BarGraph/FinancialBarChart.types';

// 评级分布图表
const ratingConfig = CHART_PRESETS.RATING;

// 行业分布图表
const industryConfig = CHART_PRESETS.INDUSTRY;
</script>

<template>
  <FinancialBarChart
    v-bind="ratingConfig"
    :x-data="ratingData"
    :y-data="ratingValues"
  />
</template>
```

## 🔧 高级配置

### 自定义网格边距

```vue
<FinancialBarChart
  :grid-padding="{
    left: 30,
    top: 40,
    right: 30,
    bottom: 40
  }"
/>
```

### 自定义主题

```vue
<script setup>
import { CHART_THEMES } from '@/components/BarGraph/FinancialBarChart.types';
</script>

<template>
  <FinancialBarChart
    v-bind="CHART_THEMES.COMPACT"
    :x-data="data.x"
    :y-data="data.y"
  />
</template>
```

## 🧪 测试

运行单元测试：

```bash
npm run test:unit FinancialBarChart
```

查看测试覆盖率：

```bash
npm run test:coverage
```

## 📝 更新日志

### v2.0.0 (2025-07-04)

- ✨ 完全重构为 Vue 3 Composition API
- 🎯 优化金融业务场景适配
- 📊 增强数据可视化效果
- ♿ 添加无障碍访问支持
- 🧪 完善单元测试覆盖
- 📚 完整的 TypeScript 类型定义

### v1.0.0

- 🎉 初始版本发布
- 📊 基础柱状图功能
- 🎨 动态颜色配置

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
