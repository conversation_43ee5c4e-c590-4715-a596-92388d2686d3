<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width: '100%' }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { ref, Ref, toRefs, watch, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { setBarColor } from '@/utils/ui/color/colorUtil';

  // 定义 props
  interface Props {
    xData?: any[];
    yData?: any[];
    height?: string;
    showLoading?: boolean;
    type?: string;
    xName?: string;
    yName?: string;
  }
  const props = defineProps<Props>();

  // 定义 emits
  const emit = defineEmits<{
    parentMethod: [name: string, type: string];
  }>();

  const { xData, yData, height, showLoading, type, xName, yName } = toRefs(props);
  const chartRef = ref<HTMLDivElement | null>(null);
  const IndustryName = ref(999);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  //渲染图
  const echartsDraw = () => {
    const myChart = getInstance();

    setOptions({
      color: ['#af0c24'],
      title: {
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        name: xName.value,
        data: xData.value,
        axisLabel: {
          interval: 0,
          rotate: 300,
          color: (_, index) => {
            return index == IndustryName.value ? '#fc0909' : '#6e6a6a';
          },
        },
        splitArea: {
          show: false,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: yName.value,
      },
      series: [
        {
          data: yData.value,
          type: 'bar',
          /*显示数据标签*/
          label: {
            show: true,
            position: 'top',
            fontSize: '10px',
            color: '#343333',
          },
          itemStyle: {
            color: (params) => {
              return setBarColor(type.value, params.name);
            },
          },
        },
      ],
    });
    //点击事件
    myChart?.off('click');
    myChart?.on('click', (params) => {
      let { name, dataIndex } = params;
      if (IndustryName.value == dataIndex) {
        emit('parentMethod', '', type.value);
        IndustryName.value = 999;
      } else {
        emit('parentMethod', name, type.value);
        IndustryName.value = dataIndex;
      }
      //刷新
      myChart?.resize();
    });
  };

  watch(
    () => [props.xData],
    () => {
      echartsDraw();
    },
    {
      deep: true,
    }
  );

  const clearData = () => {
    IndustryName.value = 999;
    echartsDraw();
  };

  defineExpose({ clearData });

  onMounted(() => {
    echartsDraw();
  });
</script>

<style scoped></style>
