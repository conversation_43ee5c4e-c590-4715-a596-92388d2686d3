<!--
  @component FinancialBarChart
  @description 金融数据分布柱状图组件
  @date 2025/07/04
  @version 2.0.0
  用于展示金融业务中的各类数据分布，如评级分布、股价分布、行业分布、市值分布等。
  支持数据缩放、动态颜色配置、响应式布局等高级功能。
  @example
  <FinancialBarChart
    type="level"
    :x-data="['AAA', 'AA', 'A']"
    :y-data="[10, 20, 15]"
    x-name="评级"
    y-name="数量"
    height="320px"
    :data-zoom="true"
    :end-value="10"
  />
-->
<template>
  <div class="financial-bar-chart">
    <!-- 加载状态遮罩 -->
    <n-spin :show="loading" size="large">
      <!-- 图表容器 -->
      <div ref="chartRef" class="chart-container" :style="chartContainerStyle" role="img"></div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { setBarColor } from '@/utils/ui/color/colorUtil';
  import type { EChartsOption } from 'echarts';
  import { BarChartType } from '@/enums/chartEnum';

  // ==================== 类型定义 ====================

  /**
   * 组件属性接口定义
   */
  interface Props {
    /** 横轴数据（分类数据） */
    xData?: string[] | number[];
    /** 纵轴数据（数值数据） */
    yData?: number[];
    /** 图表高度 */
    height?: string;
    /** 图表宽度 */
    width?: string;
    /** 加载状态 */
    showLoading?: boolean;
    /** 数据缩放结束值（显示条数） */
    endValue?: number;
    /** 是否启用数据缩放 */
    dataZoom?: boolean;
    /** 是否为普通模式（保留字段，向后兼容） */
    normal?: boolean;
    /** 横轴名称 */
    xName?: string;
    /** 纵轴名称 */
    yName?: string;
    /** 图表类型（决定颜色主题） */
    type?: BarChartType | string;
    /** 是否显示数据标签 */
    showLabels?: boolean;
    /** 数据标签字体大小 */
    labelFontSize?: string;
    /** 当前激活的标签页 */
    activeTab?: string;
    /** 网格边距配置 */
    gridPadding?: {
      left?: number;
      top?: number;
      right?: number;
      bottom?: number;
    };
  }

  /**
   * 组件事件接口定义
   */
  interface Emits {
    /** 图表点击事件 */
    (event: 'chart-click', data: { name: string; value: number; index: number }): void;
    /** 图表渲染完成事件 */
    (event: 'chart-rendered'): void;
    /** 图表数据更新事件 */
    (event: 'data-updated', data: { xData: Props['xData']; yData: Props['yData'] }): void;
  }

  // ==================== 组件配置 ====================

  /**
   * 定义组件属性，使用 withDefaults 提供默认值
   */
  const props = withDefaults(defineProps<Props>(), {
    xData: () => [],
    yData: () => [],
    height: '300px',
    width: '100%',
    showLoading: false,
    endValue: 20,
    dataZoom: false,
    normal: true,
    xName: '',
    yName: '',
    activeTab: '',
    type: BarChartType.TTM,
    showLabels: true,
    labelFontSize: '10px',
    gridPadding: () => ({
      left: 25, // 适中的左边距
      top: 30, // 适中的上边距
      right: 20, // 较小的右边距
      bottom: 25, // 适中的下边距
    }),
  });

  /**
   * 定义组件事件
   */
  const emit = defineEmits<Emits>();

  // ==================== 响应式数据 ====================

  /** 图表容器引用 */
  const chartRef = ref<HTMLDivElement | null>(null);

  /** 内部加载状态 */
  const loading = computed(() => props.showLoading);

  /** 图表容器样式 */
  const chartContainerStyle = computed(() => ({
    height: props.height,
    width: props.width,
    minHeight: '200px', // 确保最小高度
  }));

  // ==================== ECharts 集成 ====================

  /** ECharts 实例管理 */
  const { setOptions, getInstance, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // ==================== 图表配置 ====================

  /**
   * 生成 ECharts 配置选项
   * @returns ECharts 配置对象
   */
  const generateChartOptions = (): EChartsOption => {
    const hasData = props.xData && props.yData && props.xData.length > 0 && props.yData.length > 0;

    if (!hasData) {
      return {
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#999',
            fontSize: 14,
          },
        },
      };
    }

    return {
      // 数据缩放配置
      dataZoom: props.dataZoom
        ? [
            {
              type: 'slider',
              show: true,
              start: 0,
              end: Math.min(100, (props.endValue / props.xData.length) * 100),
              height: 20,
              bottom: 10,
              textStyle: {
                color: '#666',
              },
              dataBackground: {
                areaStyle: {
                  color: '#f0f2f5',
                },
                lineStyle: {
                  color: '#d9d9d9',
                },
              },
              selectedDataBackground: {
                areaStyle: {
                  color: '#e6f7ff',
                },
                lineStyle: {
                  color: '#1890ff',
                },
              },
            },
          ]
        : undefined,

      // 工具提示配置
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e8e8e8',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        formatter: (params: any) => {
          if (Array.isArray(params) && params.length > 0) {
            const param = params[0];
            return `
            <div style="padding: 4px 0;">
              <div style="margin-bottom: 4px; font-weight: 500;">${param.name}</div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 8px; height: 8px; background: ${
                  param.color
                }; border-radius: 50%; margin-right: 6px;"></span>
                <span>${props.yName || '数值'}: ${param.value}</span>
              </div>
            </div>
          `;
          }
          return '';
        },
      },

      // 网格配置 - 优化边距，确保轴名称完整显示
      grid: {
        left: 40, // 左边距
        top: props.yName ? 40 : 20, // 为纵轴名称预留顶部空间
        right: 20, // 右侧边距
        // 底部边距
        bottom: props.dataZoom
          ? props.xName
            ? 30
            : 25 // 数据缩放 + 横轴名称，增加底部空间
          : props.xName
          ? 30
          : 25, // 仅横轴名称，确保有足够空间显示
        containLabel: true,
      },

      // 横轴配置
      xAxis: {
        type: 'category',
        name: props.xName,
        nameLocation: 'middle', // 名称居中显示，避免被截断
        nameGap: 30, // 增加名称与轴的距离，确保有足够空间
        nameTextStyle: {
          color: '#666',
          fontSize: 12,
          fontWeight: 500,
          padding: [8, 0, 0, 0], // 增加上边距
        },
        data: props.xData,
        axisLabel: {
          color: '#666',
          fontSize: 11,
          interval: 'auto',
          rotate: props.xData.length > 10 ? 45 : 0, // 数据较多时倾斜显示
          formatter: (value: string) => {
            // 长文本截断处理
            return value.length > 8 ? `${value.substring(0, 8)}...` : value;
          },
        },
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
      },

      // 纵轴配置
      yAxis: {
        type: 'value',
        name: props.yName,
        nameLocation: 'end', // 名称显示在纵轴顶端（左上角区域）
        nameGap: 20, // 适当的名称与轴的距离
        nameRotate: 0, // 纵轴名称水平显示
        nameTextStyle: {
          color: '#666',
          fontSize: 12,
          fontWeight: 500,
          padding: [0, 0, 0, 0], // 不需要额外padding
        },
        axisLabel: {
          color: '#666',
          fontSize: 11,
          formatter: (value: number) => {
            // 数值格式化
            if (value >= 10000) {
              return `${(value / 10000).toFixed(1)}万`;
            } else if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}k`;
            }
            return value.toString();
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed',
          },
        },
      },

      // 系列配置
      series: [
        {
          type: 'bar',
          name: props.yName || '数值',
          data: props.yData,
          barWidth: '60%', // 设置柱子宽度为可用空间的60%，使柱子更宽
          barMaxWidth: 80, // 增加柱子最大宽度限制

          // 数据标签配置
          label: {
            show: props.showLabels,
            position: 'top',
            fontSize: parseInt(props.labelFontSize),
            color: '#343333',
            fontWeight: 500,
            formatter: (params: any) => {
              const value = params.value;
              // 数值格式化显示
              if (value >= 10000) {
                return `${(value / 10000).toFixed(1)}万`;
              } else if (value >= 1000) {
                return `${(value / 1000).toFixed(1)}k`;
              }
              return value.toString();
            },
          },

          // 柱子样式配置
          itemStyle: {
            borderRadius: [2, 2, 0, 0], // 顶部圆角
            color: (params: any) => {
              try {
                return setBarColor(props.type, params.name);
              } catch (error) {
                console.warn('设置柱状图颜色失败:', error);
                return '#1890ff'; // 默认颜色
              }
            },
          },

          // 高亮样式
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowOffsetY: 2,
            },
          },

          // 动画配置
          animationDuration: 800,
          animationEasing: 'cubicOut',
        },
      ],

      // 全局动画配置
      animation: true,
      animationThreshold: 2000,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };
  };

  // ==================== 图表渲染方法 ====================

  /**
   * 渲染图表
   * 根据当前属性生成图表配置并渲染
   */
  const renderChart = async (): Promise<void> => {
    try {
      // 等待 DOM 更新
      await nextTick();

      // 检查容器是否存在
      if (!chartRef.value) {
        console.warn('图表容器未找到，跳过渲染');
        return;
      }

      // 生成并设置图表配置
      const options = generateChartOptions();
      setOptions(options);

      // 绑定点击事件
      const chartInstance = getInstance();
      if (chartInstance) {
        // 移除之前的事件监听
        chartInstance.off('click');

        // 添加点击事件监听
        chartInstance.on('click', (params: any) => {
          if (params.componentType === 'series') {
            emit('chart-click', {
              name: params.name,
              value: params.value,
              index: params.dataIndex,
            });
          }
        });
      }

      // 触发渲染完成事件
      emit('chart-rendered');
    } catch (error) {
      console.error('图表渲染失败:', error);
    }
  };

  /**
   * 手动触发图表重新渲染
   * 外部可调用此方法强制刷新图表
   */
  const refresh = (): void => {
    renderChart();
  };

  /**
   * 手动触发图表尺寸调整
   * 当容器尺寸变化时调用
   */
  const resizeChart = (): void => {
    resize();
  };

  // ==================== 生命周期钩子 ====================

  /**
   * 组件挂载后初始化图表
   */
  onMounted(() => {
    // 延迟渲染，确保 DOM 完全挂载
    setTimeout(() => {
      renderChart();
    }, 100);
  });

  /**
   * 组件卸载前清理资源
   */
  onUnmounted(() => {
    const chartInstance = getInstance();
    if (chartInstance) {
      // 移除所有事件监听
      chartInstance.off('click');
      // ECharts 实例会由 useECharts hook 自动清理
    }
  });

  // ==================== 数据监听 ====================

  /**
   * 监听数据变化，自动重新渲染图表
   */
  watch(
    [
      () => props.xData,
      () => props.yData,
      () => props.type,
      () => props.showLabels,
      () => props.dataZoom,
      () => props.endValue,
    ],
    (newValues, oldValues) => {
      // 检查是否有实际变化
      const hasChanged = newValues.some((newVal, index) => {
        const oldVal = oldValues?.[index];
        if (Array.isArray(newVal) && Array.isArray(oldVal)) {
          return JSON.stringify(newVal) !== JSON.stringify(oldVal);
        }
        return newVal !== oldVal;
      });

      if (hasChanged) {
        // 触发数据更新事件
        emit('data-updated', {
          xData: props.xData,
          yData: props.yData,
        });

        // 重新渲染图表
        renderChart();
      }
    },
    {
      deep: true,
      immediate: false,
    }
  );

  /**
   * 监听尺寸变化
   */
  watch([() => props.height, () => props.width], () => {
    nextTick(() => {
      resizeChart();
    });
  });
  /**
   * 监听标签页变化
   */
  watch([() => props.activeTab, () => props.activeTab], () => {
    nextTick(() => {
      resizeChart();
    });
  });

  // ==================== 暴露给父组件的方法 ====================

  /**
   * 暴露给父组件的方法和属性
   */
  defineExpose({
    /** 手动刷新图表 */
    refresh,
    /** 调整图表尺寸 */
    resize: resizeChart,
    /** 获取图表实例 */
    getInstance,
    /** 图表容器引用 */
    chartRef,
  });
</script>

<style scoped lang="scss">
  /**
 * 组件样式
 * 使用 SCSS 预处理器，支持嵌套和变量
 */

  .financial-bar-chart {
    position: relative;
    width: 100%;

    // 图表容器样式
    .chart-container {
      width: 100%;
      transition: all 0.3s ease;

      // 确保图表在不同设备上的响应式表现
      @media (max-width: 768px) {
        min-height: 250px;
      }

      @media (max-width: 480px) {
        min-height: 200px;
      }
    }

    // 加载状态样式优化
    :deep(.n-spin-container) {
      border-radius: 4px;
      overflow: hidden;
    }

    // 确保图表在暗色主题下的兼容性
    @media (prefers-color-scheme: dark) {
      .chart-container {
        background-color: transparent;
      }
    }
  }

  // 图表无障碍访问优化
  .chart-container[role='img'] {
    &:focus {
      outline: 2px solid #1890ff;
      outline-offset: 2px;
    }
  }
</style>
