<template>
  <!--用户角色通用弹框-->
  <n-select
    v-model:value="selectedValues"
    :options="options"
    :style="{ width: width || '234px' }"
    clearable
    filterable
    placeholder="请选择"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  import { queryLevelSrtUsers } from '@/api/levelStrategy/levelStrategyPoolApi';
  interface Props {
    width?: string;
  }
  defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  const get_PeerAgencyList = async () => {
    const { data, code } = await queryLevelSrtUsers();
    if (code == 200) {
      data.forEach((item) => {
        options.value.push({ label: item, value: item });
      });
    }
  };
  get_PeerAgencyList();

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
</script>

<style scoped></style>
