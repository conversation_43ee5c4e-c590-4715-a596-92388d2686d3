<template>
  <!--  财务数据通用组件-->
  <n-cascader
    v-model:value="value"
    :max-tag-count="1"
    :options="industryList"
    check-strategy="child"
    clearable
    filterable
    placeholder="请选择财务数据"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { onMounted, ref, watch } from 'vue';
  import { getPolicyFinancialOptions } from '@/api/calculate/calculate';
  import { CascaderOption, useMessage } from 'naive-ui';
  import { transformData } from '@/utils/business/financial';

  interface Props {
    type: string;
    isNumber: boolean;
    defaultVal?: string | null;
  }

  const props = defineProps<Props>();

  const value = ref<any>(null);
  const message = useMessage();

  const options = ref([]);
  const industryList = ref<any>([]);
  const industryList2 = ref<any>([]);
  //
  // [
  //   {label:"财务数据", value:"财务数据",type:'1',offset:0,children:[
  //       {label:"常用项", value:"常用项",children:[
  //           {label:"经营性现金流", value:"经营性现金流"},
  //           {label:"净利润", value:"净利润"},
  //           {label:"归母净利润", value:"归母净利润"},
  //         ]
  //       },
  //       {label:"资产负债表", value:"资产负债表",children:[
  //               {label:"应收票据及应收账款", value:"应收票据及应收账款"},
  //             ]
  //           }
  //     ]
  //   },
  //     {label:"财务数据趋势", value:"财务数据趋势",type:'2',offset:0,children:[
  //         {label:"净利润增速", value:"净利润增速" },
  //         {label:"扣非净利润增速", value:"扣非净利润增速" },
  //         }
  //       ]
  //     }
  //
  // ]
  //
  // [
  //   {
  //     "itemName":"应收票据及应收账款",
  //     "itemType":"精确查找@财务数据@资产负债表",
  //     "offset":0
  //   },
  //     {
  //       "itemName":"经营性现金流",
  //       "itemType":"精确查找@财务数据@常用项",
  //       "offset":0
  //     },
  //     {
  //       "itemName":"净利润",
  //       "itemType":"精确查找@财务数据@常用项",
  //       "offset":0
  //     },
  //     {
  //       "itemName":"归母净利润",
  //       "itemType":"精确查找@财务数据@常用项",
  //       "offset":0
  //     },
  //   {
  //     "itemName":"净利润增速",
  //     "itemType":"模糊查找@财务数据趋势",
  //     "offset":0
  //   },
  //   {
  //     "itemName":"扣非净利润增速",
  //     "itemType":"模糊查找@财务数据趋势",
  //     "offset":0
  //   }
  //   ]

  //获取标签
  const get_PolicyFinancialOptions = () => {
    getPolicyFinancialOptions({})
      .then((res: any) => {
        if (res.code === 200) {
          industryList2.value = transformData(res.data);
          if (props.type == '1') {
            industryList.value = industryList2.value.filter((option) => option.type == props.type);
          } else if (props.type == '2') {
            industryList.value = industryList2.value.filter((option) => option.type == props.type);
            //模糊查找自选
          } else if (props.type == '3') {
            industryList.value = [
              industryList2.value.filter((option) => option.type == '1')[0].children[1],
            ];
          } else if (props.type == 'cwsj') {
            //只获取第一个财务数据
            industryList.value = industryList2.value.filter((option) => option.value == '财务数据');
          } else if (props.type == 'sjyj') {
            //只获取第二个审计意见
            industryList.value = industryList2.value.filter((option) => option.value == '审计意见');
          }
        } else {
          message.error(res.msg);
        }
      })
      .catch(function (error) {
        //console.log(error);
      });
  };
  // //获取标签
  // const get_PolicyFinancialOptions=()=> {
  //   getPolicyFinancialOptions({}).then( (res:any) =>{
  //     if (res.code === 200) {
  //       industryList2.value =_(res.data)
  //         .groupBy('type')
  //         .map((items, type) => ({
  //           value: type,
  //           label: type,
  //           children: items.map(item => ({value: item.item, label: item.item}))
  //         }))
  //         .value();
  //       //console.log(industryList2.value)
  //       nextTick(()=>{
  //         if(props.type=='1'){
  //           industryList.value=industryList2.value.filter(option => option.value !== '财务数据趋势')
  //           //console.log(industryList.value)
  //         }else if(props.type=='2'){
  //
  //           industryList.value=industryList2.value.filter(option => option.value == ('财务数据趋势'))
  //
  //         }
  //       })
  //
  //     }
  //   }).catch(function (error) {
  //     //console.log(error);
  //   });
  // };
  watch(
    () => props.isNumber,
    (newval: boolean) => {
      if (props.type == '2') {
        industryList.value = industryList2.value.filter((option) => option.type == props.type);
        value.value = null;
      } else if (props.type == '3') {
        industryList.value = [
          industryList2.value.filter((option) => option.type == '1')[0].children[1],
        ];
        value.value = null;
      }
    }
  );

  onMounted(() => {
    get_PolicyFinancialOptions();
  });
  const emit = defineEmits(['getValue']);

  const handleUpdateValue = (value, options: CascaderOption[]) => {
    emit('getValue', value);
  };
  watch(
    () => props.defaultVal,
    (newVal, oldValue) => {
      value.value = newVal;
      emit('getValue', newVal);
    },
    { deep: true, immediate: true }
  );
</script>

<style scoped></style>
