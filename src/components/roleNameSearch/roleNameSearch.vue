<template>
  <!--  角色名称-->
  <div>
    <n-select
      :style="{ width: width || '234px' }"
      v-model:value="selectedValues"
      label-field="label"
      :consistent-menu-width="false"
      value-field="value"
      :placeholder="placeholder || '请输入角色名称'"
      :options="options"
      :loading="loading"
      clearable
      tag
      :filterable="true"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { SelectOption, SelectGroupOption } from 'naive-ui';
  import { vagueQueryRoleList } from '@/api/system/role';
  interface Props {
    placeholder?: string;
    width?: string;
  }
  defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  //搜索
  const handleSearch = (val) => {
    get_vagueQueryRoleList(val);
  };

  const get_vagueQueryRoleList = async (roleName) => {
    loading.value = true;
    const { data, code } = await vagueQueryRoleList(roleName);
    loading.value = false;
    options.value = [];
    if (code == 200) {
      [...new Set(data)].forEach((item: any) => {
        options.value.push({ label: item, value: item });
      });
    }
  };

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
</script>
<style></style>
