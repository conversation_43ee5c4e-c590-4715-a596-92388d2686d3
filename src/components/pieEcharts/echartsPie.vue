<!--饼图封装-->
<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height }" class="w-full"></div>
  </n-spin>
</template>

<script lang="ts">
import {defineComponent, ref, Ref, toRefs, watch,onMounted} from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
export default defineComponent({
  props: ['xData','height' ,'yData','showLoading'],
  setup(props) {
    const {height,showLoading,xData,
      yData}=toRefs(props)
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
    //渲染图
    const echartsDraw=()=> {

      let data=<any>[];
      xData.value.forEach((item,index)=>{
        data.push({ value:yData.value[index] , name: item})
      })

      setOptions(
        {
          tooltip: {
            trigger: 'item',
            formatter: " {b}: {c} ({d}%)",
          },

          series: [
            {
              label: {
                normal: {
                  formatter: '{b}：{d}%',
                }
              },
              type: 'pie',
              radius: '50%',
             data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      );

    }


    watch(
      () => [props.xData],
      () => {
        //console.log(1)
        echartsDraw()
      },
      {
        deep: true,
      }
    );
    onMounted(()=>{
      echartsDraw();
    })
    return { chartRef,echartsDraw,showLoading,height,xData,yData};
  },
});
</script>

<style scoped>

</style>
