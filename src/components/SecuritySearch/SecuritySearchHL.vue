<template>
  <!--  华龙通用股票搜索封装-->
  <div class="w-full">
    <n-select
      :style="{ width: width || '234px' }"
      v-model:value="selectedValues"
      label-field="stkname"
      value-field="stkcode"
      filterable
      :placeholder="placeholder || '请输入证券代码或名称'"
      :options="options"
      :render-label="renderLabel"
      :loading="loading"
      clearable
      remote
      :clear-filter-after-select="false"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
    <!--  <n-button @click="transValue"></n-button>-->
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';

  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  import { queryStkInfo } from '@/views/hualong/api/backend.ts';

  interface Props {
    placeholder?: string;
    width?: string;
  }

  defineProps<Props>();

  const selectedValues = ref<string | null>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);
  const renderLabel = (option) => {
    return [
      option.stkname as string,
      h(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.stkcode,
        }
      ),
    ];
  };
  //搜索
  const handleSearch = (val) => {
    getlistCodeAndName({ stkcode: val, size: 30 });
  };

  const getlistCodeAndName = async (params) => {
    loading.value = true;
    const { data, code, msg } = await queryStkInfo(params);
    loading.value = false;
    if (code === 200) {
      options.value = data;
    } else {
    }
  };

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    emit('getValue', selectedValues.value);
  };
  // 定义一个方法来更新 selectedValues
  const clearSelectedValue = () => {
    selectedValues.value = null;
    options.value = [];
  };

  // 暴露方法给父组件
  defineExpose({
    clearSelectedValue,
  });
</script>
