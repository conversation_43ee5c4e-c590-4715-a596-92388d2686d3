<template>
  <!--  通用股票搜索封装，包含基金等其他类型-->
  <div class="security-search-container">
    <div class="search-wrapper">
      <n-icon class="search-icon" size="20">
        <Search />
      </n-icon>
      <n-select
        v-model:value="selectedValues"
        :clear-filter-after-select="false"
        :loading="loading"
        :options="options"
        :placeholder="placeholder || '请输入证券代码或名称'"
        :render-label="renderLabel"
        :reset-menu-on-options-change="false"
        :style="{ width: width || '100%', height: '40px' }"
        class="enhanced-select"
        clearable
        filterable
        label-field="secName"
        remote
        size="medium"
        value-field="secCode"
        @scroll="handleScroll"
        @search="handleSearch"
        @update:value="handleUpdateValue"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { NIcon, SelectOption, SelectGroupOption, NText, NTag, NSpace } from 'naive-ui';
  import { SecTypeInfo } from '@/enums/secEnum';
  import { getEnumProperties } from '@/enums/baseEnum';
  import { Search } from '@vicons/ionicons5';
  import { vagueQuerySecCodes } from '@/api/sec/secInfoApi';
  import { PageRequest } from '@/models/common/baseRequest';

  interface Props {
    placeholder?: string;
    width?: string;
    size?: string;
    secTypeEnum?: string;
    /** 是否显示证券分类标签 - 控制下拉选项中是否显示证券分类(如：股票、基金、债券等)的标签 */
    showSecCategory?: boolean;
  }

  const pageRequest = ref<PageRequest>({
    size: 30,
    current: 1,
  });
  const queryStr = ref<string>('');

  const props = withDefaults(defineProps<Props>(), {
    showSecCategory: false, // 默认不显示分类标签，保持界面简洁
  });

  const selectedValues = ref<string | null>();
  const loading = ref<boolean>(false);
  const current = ref<number>(1);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);
  const renderLabel = (option) => {
    return [
      option.secName as string,
      h(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.secCode.split(',')[0],
        }
      ),
      h(
        NSpace,
        {
          style: 'position: absolute;right: 16px;top:5px',
        },
        {
          default: () => [
            h(
              NTag,
              {
                type:
                  option.secType == '1' ? 'success' : option.secType != '0' ? 'warning' : 'default',
                bordered: false,
                size: 'small',
              },
              {
                default: () => getEnumProperties(SecTypeInfo, option.secType).label,
              }
            ),
            // 根据showSecCategory属性控制是否显示证券分类标签
            // 当为true时显示证券分类(如：主板、创业板、科创板等)，为false时不显示
            props.showSecCategory
              ? h(
                  NTag,
                  {
                    type: 'default',
                    bordered: false,
                    size: 'small',
                  },
                  {
                    default: () => option.secCategory, // 显示证券分类名称
                  }
                )
              : '', // 不显示分类标签
          ],
        }
      ),
    ];
  };
  //搜索
  const handleSearch = (val) => {
    selectedValues.value = val;
    pageRequest.value.current = 1;
    queryStr.value = val || '';
    doVagueQuerySecCodes(pageRequest.value, queryStr.value);
  };

  const doVagueQuerySecCodes = async (pageRequest: PageRequest, queryStr: string) => {
    loading.value = true;

    const { data, code } = await vagueQuerySecCodes(pageRequest, queryStr, null);
    loading.value = false;
    if (code === 200) {
      data.records.forEach((item) => (item.secCode = item.secCode + ',' + item.secName));
      if (current.value == 1) {
        options.value = [];
        options.value = data.records;
      } else {
        options.value = options.value.concat(data.records);
      }
    }
  };

  const emit = defineEmits(['getValue']);
  // 传递value参数至父组件
  const handleUpdateValue = () => {
    let arr = options.value.filter((item) => item.secCode === selectedValues.value);
    emit(
      'getValue',
      selectedValues.value ? selectedValues.value.split(',')[0] : '',
      arr && arr[0] ? arr[0].secName : '',
      arr && arr[0] ? arr[0].secType : ''
    );
  };
  // 定义一个方法来更新 selectedValues
  const clearSelectedValue = () => {
    selectedValues.value = null;
    options.value = [];
  };
  const handleScroll = (e: Event) => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      current.value++;

      // 构造正确的 PageRequest 对象
      const scrollPageRequest: PageRequest = {
        current: current.value,
        size: 30,
        ascOrDesc: null,
        orderBy: null,
        secCode: null,
      };

      // 设置查询字符串
      const scrollQueryStr = selectedValues.value || '';

      doVagueQuerySecCodes(scrollPageRequest, scrollQueryStr);
    }
  };

  // 暴露方法给父组件
  defineExpose({
    clearSelectedValue,
  });
</script>

<style lang="less" scoped>
  .security-search-container {
    width: 100%;
    position: relative;
  }

  .search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 12px;
    z-index: 10;
    color: #6b7280;
    pointer-events: none;
  }

  .enhanced-select {
    width: 100%;

    :deep(.n-base-selection) {
      height: 40px !important;
      border-radius: 8px !important;
      border: 1px solid #d1d5db !important;
      background: #ffffff !important;
      transition: all 0.3s ease !important;

      &:hover {
        border-color: #9ca3af !important;
      }

      &.n-base-selection--focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
      }
    }

    :deep(.n-base-selection-input) {
      padding-left: 40px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #374151 !important;

      &::placeholder {
        color: #9ca3af !important;
        font-weight: 400 !important;
      }
    }

    :deep(.n-base-selection-placeholder) {
      padding-left: 40px !important;
      color: #9ca3af !important;
      font-size: 14px !important;
      font-weight: 400 !important;
    }

    :deep(.n-base-selection__border) {
      border: none !important;
    }

    :deep(.n-base-selection__state-border) {
      border: none !important;
    }

    :deep(.n-base-loading) {
      color: #6b7280 !important;
    }

    :deep(.n-base-clear) {
      color: #6b7280 !important;

      &:hover {
        color: #374151 !important;
      }
    }

    :deep(.n-base-suffix) {
      color: #6b7280 !important;
    }
  }

  /* 移除全局样式，避免影响其他组件 */

  /* 响应式设计 */
  @media (max-width: 768px) {
    .enhanced-select {
      :deep(.n-base-selection) {
        height: 44px !important;
      }

      :deep(.n-base-selection-input) {
        font-size: 14px !important;
        padding-left: 44px !important;
      }

      :deep(.n-base-selection-placeholder) {
        font-size: 14px !important;
        padding-left: 44px !important;
      }
    }

    .search-icon {
      left: 14px;
      size: 18px;
    }
  }
</style>
