<template>
  <!-- 简化版证券选择器 - 基于预设配置 -->
  <UniversalSecuritySelector
    v-model:value="modelValue"
    v-bind="mergedProps"
    @clear="handleClear"
    @focus="handleFocus"
    @scroll="handleScroll"
    @search="handleSearch"
    @select="handleSelect"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { computed, type ModelRef } from 'vue';
  import UniversalSecuritySelector from './test/UniversalSecuritySelector.vue';
  import {
    getPresetConfig,
    mergeConfig,
    type SecuritySelectorProps,
    type BaseSecurityData,
  } from './types';

  // 组件属性接口
  interface Props extends Partial<SecuritySelectorProps> {
    /** 预设配置名称 */
    preset?: 'stock-basic' | 'security-full' | 'fund-selector' | 'bond-selector' | 'multi-security';
    /** 双向绑定的值 */
    modelValue?: string | string[] | null;
  }

  // 定义props
  const props = withDefaults(defineProps<Props>(), {
    preset: 'stock-basic',
    modelValue: null,
  });

  // 定义emits
  const emit = defineEmits<{
    'update:modelValue': [value: string | string[] | null];
    select: [option: BaseSecurityData | null, value: string | string[] | null];
    clear: [];
    search: [query: string];
    focus: [];
    scroll: [event: Event];
  }>();

  // 双向绑定
  const modelValue: ModelRef<string | string[] | null> = defineModel();

  // 合并预设配置和自定义配置
  const mergedProps = computed(() => {
    const presetConfig = getPresetConfig(props.preset || 'stock-basic') || {};
    const { preset, modelValue: _, ...customProps } = props;
    return mergeConfig(presetConfig, customProps);
  });

  // 事件处理函数
  const handleUpdateValue = (value: string | string[] | null) => {
    emit('update:modelValue', value);
  };

  const handleSelect = (option: BaseSecurityData | null, value: string | string[] | null) => {
    emit('select', option, value);
  };

  const handleClear = () => {
    emit('clear');
  };

  const handleSearch = (query: string) => {
    emit('search', query);
  };

  const handleFocus = () => {
    emit('focus');
  };

  const handleScroll = (event: Event) => {
    emit('scroll', event);
  };
</script>

<script lang="ts">
  export default {
    name: 'SecuritySelector',
  };
</script>
