<!--  通用证券搜索封装，证券代码带后缀 -->
<template>
  <div class="w-full security-search-wrapper">
    <!-- 搜索图标 -->
    <n-icon :component="Search" :size="16" class="search-icon" />
    <n-select
      v-model:value="selectedValues"
      :clear-filter-after-select="false"
      :loading="loading"
      :options="securityOptions"
      :placeholder="placeholder || '请输入证券代码或名称'"
      :render-label="renderLabel"
      :render-tag="renderSearchIcon"
      :size="size"
      :style="{ width: width || '236px' }"
      class="security-select"
      clearable
      filterable
      label-field="secName"
      remote
      value-field="secCode"
      @focus="focusSearch"
      @scroll="handleScroll"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
  </div>
  <!--  <n-button @click="transValue"></n-button>-->
</template>

<script lang="ts" setup>
  // ==================== Imports ====================
  // Vue 相关
  import { ref, h as createElement, VNodeChild } from 'vue';

  // API 相关

  // UI 组件相关
  import { SelectOption, SelectGroupOption, NText, NTag, NIcon } from 'naive-ui';

  // 图标相关
  import { Search } from '@vicons/ionicons5';

  // 枚举相关
  import { getEnumProperties } from '@/enums/baseEnum';
  import { SecType, SecTypeInfo } from '@/enums/secEnum';
  import { Size } from 'naive-ui/lib/select/src/interface';
  import { vagueQuerySecCodes } from '@/api/sec/secInfoApi';

  // 类型相关
  import { PageRequest } from '@/models/common/baseRequest';

  // ==================== Type Declarations ====================
  interface Props {
    /** 占位符文本 */
    placeholder?: string;
    /** 组件宽度 */
    width?: string;
    /** 组件尺寸：小、中、大 */
    size?: Size;
    /**
     * 证券类型
     */
    secType?: SecType;
  }

  // ==================== Props & Emits ====================
  const props = defineProps<Props>();

  interface Emits {
    /**
     * 获取值事件（🤮 保留旧的垃圾命名，向后兼容）
     * @deprecated 建议使用更语义化的事件名
     * @param e 事件名
     * @param securityCode 证券代码
     * @param securityName 证券名称
     */
    (e: 'getValue', securityCode: string, securityName: string | null): void;
    /**
     * 证券选择事件（推荐使用）
     * @param e 事件名
     * @param securityCode 证券代码
     * @param securityName 证券名称
     */
    (e: 'onSecuritySelected', securityCode: string, securityName: string | null): void;
  }

  const emit = defineEmits<Emits>();

  // ==================== Local State ====================
  const selectedValues = ref<string | null>();
  const loading = ref<boolean>(false);
  const securityOptions = ref<Array<SelectOption | SelectGroupOption>>([]);
  const current = ref(1);

  // ==================== Business Functions ====================

  /**
   * 渲染选项标签：证券名称 + 代码 + 类型标签
   */
  const renderLabel = (option) => {
    return [
      option.secName as string,
      createElement(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.secCode,
        }
      ),
      createElement(
        NTag,
        {
          type: getEnumProperties(SecTypeInfo, option.secType || '').type as
            | 'success'
            | 'warning'
            | 'error'
            | 'info',
          bordered: false,
          size: 'small',
          style: 'position: absolute;right: 10px;',
        },
        {
          default: () => getEnumProperties(SecTypeInfo, option.secType || '').label,
        }
      ),
    ];
  };

  /**
   * 渲染选中标签（不包含搜索图标）
   */
  const renderSearchIcon = ({ option, handleClose }) => {
    return createElement(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          padding: '0 8px',
        },
      },
      [
        createElement(
          'span',
          {
            style: {
              marginRight: '6px',
            },
          },
          option.secName
        ),
        createElement(
          NText,
          {
            depth: '3',
            style: {
              fontSize: '12px',
            },
          },
          {
            default: () => option.secCode,
          }
        ),
      ]
    );
  };

  /**
   * 模糊查询证券代码
   * @param params 查询参数，包含 queryStr, size, current
   */
  const doVagueQuerySecCodes = async (params: {
    queryStr: string;
    size: number;
    current: number;
  }) => {
    loading.value = true;

    // 构造 PageRequest 对象
    const pageRequest: PageRequest = {
      current: params.current,
      size: params.size,
    };

    // 调用 API，传递两个参数：pageRequest 和 queryStr
    const { data, code } = await vagueQuerySecCodes(
      pageRequest,
      params.queryStr,
      props.secType ? props.secType : null
    );
    loading.value = false;

    if (code === 200) {
      if (current.value == 1) {
        securityOptions.value = [];
        securityOptions.value = data.records;
      } else {
        securityOptions.value = securityOptions.value.concat(data.records);
      }
    }
  };

  /**
   * 处理搜索输入事件
   */
  const handleSearch = (searchValue: string) => {
    current.value = 1;
    doVagueQuerySecCodes({ queryStr: searchValue, size: 30, current: current.value });
  };

  /**
   * 处理焦点事件，首次加载数据
   */
  const focusSearch = () => {
    if (securityOptions.value.length <= 0) {
      doVagueQuerySecCodes({ queryStr: '', size: 30, current: current.value });
    }
  };

  /**
   * 处理证券选择事件
   * 同时触发新旧两个事件，保证向后兼容
   */
  const handleUpdateValue = () => {
    const selectedSecurity = securityOptions.value.filter(
      (item) => item.secCode === selectedValues.value
    );
    const securityCode = selectedValues.value;
    const securityName = selectedSecurity && selectedSecurity[0] ? selectedSecurity[0].name : '';

    // 🤮 触发旧的垃圾事件（向后兼容）
    emit('getValue', securityCode, securityName);

    // ✅ 触发新的语义化事件
    emit('onSecuritySelected', securityCode, securityName);
  };

  /**
   * 处理滚动事件，实现分页加载
   */
  const handleScroll = (e: Event) => {
    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      current.value++;
      doVagueQuerySecCodes({
        queryStr: selectedValues.value || '',
        size: 30,
        current: current.value,
      });
    }
  };

  /**
   * 清空选中值和选项列表
   */
  const clearSelectedValue = () => {
    selectedValues.value = '';
    securityOptions.value = [];
  };

  // ==================== Exports ====================
  defineExpose({
    clearSelectedValue,
  });
</script>

<style scoped>
  .security-search-wrapper {
    position: relative;
  }

  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 10;
    pointer-events: none;
  }

  .security-select :deep(.n-base-selection) {
    padding-left: 35px !important;
  }
</style>
