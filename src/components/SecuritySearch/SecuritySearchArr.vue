<template>
  <!--  通用股票搜索封装 多选-->
  <div :style="{ width: width || '234px' }">
    <n-select
      v-model:value="selectedValues"
      :clear-filter-after-select="false"
      :default-value="defaultKeys"
      :loading="loading"
      :max-tag-count="2"
      :options="options"
      :placeholder="placeholder || '请输入证券代码或名称'"
      :render-label="renderLabel"
      clearable
      filterable
      multiple
      remote
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
    <!--  <n-button @click="transValue"></n-button>-->
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild, watch } from 'vue';
  import { NIcon, SelectOption, SelectGroupOption, NText, NTag } from 'naive-ui';
  import { SecStatusInfo } from '@/enums/secEnum';
  import { listCodeAndName } from '@/api/sec/secInfoApi';
  interface Props {
    placeholder?: string;
    width?: string;
    defaultVal?: string;
  }
  const props = defineProps<Props>();

  const selectedValues = ref<any>([]);
  const defaultKeys = ref<any>([]);

  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);
  const renderLabel = (option) => {
    return [
      option.value as string,
      option.status && option.status != 0
        ? h(
            NTag,
            {
              type: SecStatusInfo[option.status]?.type,
              bordered: false,
              size: 'small',
              style: 'position: absolute;right: 10px;',
            },
            {
              default: () => SecStatusInfo[option.status].label,
            }
          )
        : '',
    ];
  };

  watch(
    () => props.defaultVal,
    (newVal, oldValue) => {
      if (newVal) {
        // defaultKeys.value=newVal.split(',');
        selectedValues.value = newVal.split(',');
      } else {
        selectedValues.value = [];
      }
    },
    { deep: true, immediate: true }
  );

  //清空值
  const clearValue = () => {
    selectedValues.value = [];
  };
  //搜索
  const handleSearch = (val) => {
    getlistCodeAndName({ searchKeyWords: val, size: '30' });
  };

  const getlistCodeAndName = async (params) => {
    loading.value = true;
    const { data, code } = await listCodeAndName(params);
    loading.value = false;
    options.value = [];
    if (code == 200) {
      data.records.forEach((item) => {
        options.value.push({ ...item, label: item.name, value: item.name + '(' + item.code + ')' });
      });
    }
  };

  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    if (selectedValues.value && selectedValues.value.length > 0) {
      const reg = /\(([^)]+)\)/;
      const ids = selectedValues.value.map((item) => item.match(reg)[1]);
      emit('getValue', ids, selectedValues.value);
    } else {
      emit('getValue', [], []);
    }
  };

  defineExpose({
    clearValue,
  });
</script>
