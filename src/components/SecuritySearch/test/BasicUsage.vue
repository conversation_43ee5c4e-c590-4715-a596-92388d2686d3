<template>
  <div class="security-selector-examples">
    <n-card title="通用证券选择器使用示例">
      <n-space size="large" vertical>
        <!-- 基础股票选择器 -->
        <n-card size="small" title="1. 基础股票选择器">
          <template #header-extra>
            <n-tag type="info">stock-basic</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="stockValue"
            :show-sec-status="true"
            api-type="stock"
            placeholder="请输入股票代码或名称"
            @select="handleStockSelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ stockValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 全功能证券选择器 -->
        <n-card size="small" title="2. 全功能证券选择器">
          <template #header-extra>
            <n-tag type="success">security-full</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="securityValue"
            :show-sec-category="true"
            :show-sec-status="true"
            :show-sec-type="true"
            api-type="security"
            placeholder="请输入证券代码或名称"
            @select="handleSecuritySelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ securityValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 基金选择器 -->
        <n-card size="small" title="3. 基金选择器">
          <template #header-extra>
            <n-tag type="warning">fund-selector</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="fundValue"
            :show-sec-type="true"
            api-type="fund"
            placeholder="请输入基金代码或名称"
            sec-type-filter="1"
            @select="handleFundSelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ fundValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 债券选择器 -->
        <n-card size="small" title="4. 债券选择器">
          <template #header-extra>
            <n-tag type="error">bond-selector</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="bondValue"
            :show-sec-type="true"
            api-type="security"
            placeholder="请输入债券代码或名称"
            sec-type-filter="2"
            @select="handleBondSelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ bondValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 多选证券选择器 -->
        <n-card size="small" title="5. 多选证券选择器">
          <template #header-extra>
            <n-tag type="primary">multi-security</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="multiValue"
            :max-tag-count="3"
            :multiple="true"
            :show-sec-type="true"
            api-type="security"
            placeholder="请选择多个证券"
            @select="handleMultiSelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ Array.isArray(multiValue) ? multiValue.join(', ') : '未选择' }}
          </n-text>
        </n-card>

        <!-- 自定义样式选择器 -->
        <n-card size="small" title="6. 自定义样式选择器">
          <template #header-extra>
            <n-tag>custom-style</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="customValue"
            :show-icon="false"
            :show-search-icon="false"
            api-type="stock"
            placeholder="自定义宽度和尺寸"
            size="large"
            width="400px"
            @select="handleCustomSelect"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ customValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 禁用状态选择器 -->
        <n-card size="small" title="7. 禁用状态选择器">
          <template #header-extra>
            <n-tag type="default">disabled</n-tag>
          </template>
          <UniversalSecuritySelector
            v-model:value="disabledValue"
            :disabled="true"
            api-type="stock"
            default-value="000001"
            placeholder="禁用状态"
          />
          <n-text depth="3" style="margin-top: 8px; display: block">
            选中值: {{ disabledValue || '未选择' }}
          </n-text>
        </n-card>

        <!-- 操作按钮 -->
        <n-card size="small" title="8. 操作示例">
          <n-space>
            <n-button type="warning" @click="clearAllValues"> 清空所有选择 </n-button>
            <n-button type="info" @click="setDefaultValues"> 设置默认值 </n-button>
            <n-button type="success" @click="showSelectedValues"> 显示所有选中值 </n-button>
          </n-space>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMessage } from 'naive-ui';
  import UniversalSecuritySelector from './UniversalSecuritySelector.vue';
  import type { BaseSecurityData } from '../types';

  const message = useMessage();

  // 响应式数据
  const stockValue = ref<string | null>(null);
  const securityValue = ref<string | null>(null);
  const fundValue = ref<string | null>(null);
  const bondValue = ref<string | null>(null);
  const multiValue = ref<string[]>([]);
  const customValue = ref<string | null>(null);
  const disabledValue = ref<string | null>('000001');

  // 事件处理函数
  const handleStockSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('股票选择:', { option, value });
    if (option) {
      message.success(`选择了股票: ${option.name} (${option.code})`);
    }
  };

  const handleSecuritySelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('证券选择:', { option, value });
    if (option) {
      message.success(
        `选择了证券: ${option.secName || option.name} (${option.secCode || option.code})`
      );
    }
  };

  const handleFundSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('基金选择:', { option, value });
    if (option) {
      message.success(
        `选择了基金: ${option.secName || option.name} (${option.secCode || option.code})`
      );
    }
  };

  const handleBondSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('债券选择:', { option, value });
    if (option) {
      message.success(
        `选择了债券: ${option.secName || option.name} (${option.secCode || option.code})`
      );
    }
  };

  const handleMultiSelect = (option: BaseSecurityData | null, value: string[] | null) => {
    console.log('多选证券:', { option, value });
    if (Array.isArray(value) && value.length > 0) {
      message.success(`已选择 ${value.length} 个证券`);
    }
  };

  const handleCustomSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('自定义选择:', { option, value });
  };

  // 操作函数
  const clearAllValues = () => {
    stockValue.value = null;
    securityValue.value = null;
    fundValue.value = null;
    bondValue.value = null;
    multiValue.value = [];
    customValue.value = null;
    message.info('已清空所有选择');
  };

  const setDefaultValues = () => {
    stockValue.value = '000001';
    securityValue.value = '000002';
    fundValue.value = '159919';
    bondValue.value = '110001';
    multiValue.value = ['000001', '000002'];
    customValue.value = '000003';
    message.success('已设置默认值');
  };

  const showSelectedValues = () => {
    const values = {
      股票: stockValue.value,
      证券: securityValue.value,
      基金: fundValue.value,
      债券: bondValue.value,
      多选: multiValue.value,
      自定义: customValue.value,
    };

    console.log('所有选中值:', values);
    message.info('请查看控制台输出');
  };
</script>

<style scoped>
  .security-selector-examples {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .n-card {
    margin-bottom: 16px;
  }

  .n-text {
    font-size: 12px;
  }
</style>
