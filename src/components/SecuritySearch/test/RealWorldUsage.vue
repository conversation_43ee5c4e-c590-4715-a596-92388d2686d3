<template>
  <!-- 实际项目使用示例 - 替换 PeerDataTable.vue 中的证券类型选择器 -->
  <div class="peer-data-enhanced">
    <n-card title="同业数据查询 - 使用通用证券选择器">
      <!-- 筛选条件区域 -->
      <template #header-extra>
        <n-space>
          <!-- 证券类型选择器 - 使用简化版组件 -->
          <SecuritySelector
            v-model="secType"
            preset="security-full"
            placeholder="请选择证券类型"
            width="220px"
            :show-sec-type="true"
            api-type="security"
            @select="handleSecTypeSelect"
          />

          <!-- 证券搜索框 - 使用通用组件 -->
          <UniversalSecuritySelector
            v-model:value="secCode"
            api-type="stock"
            placeholder="请输入证券代码或名称"
            width="300px"
            :show-sec-status="true"
            @select="handleSecuritySelect"
          />

          <!-- 查询按钮 -->
          <n-button type="primary" @click="handleQuery"> 查询 </n-button>

          <!-- 重置按钮 -->
          <n-button @click="handleReset"> 重置 </n-button>
        </n-space>
      </template>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
      />
    </n-card>

    <!-- 高级筛选示例 -->
    <n-card title="高级筛选示例" style="margin-top: 20px">
      <n-form inline label-placement="left">
        <n-form-item label="股票选择">
          <SecuritySelector v-model="advancedFilters.stock" preset="stock-basic" width="200px" />
        </n-form-item>

        <n-form-item label="基金选择">
          <SecuritySelector v-model="advancedFilters.fund" preset="fund-selector" width="200px" />
        </n-form-item>

        <n-form-item label="债券选择">
          <SecuritySelector v-model="advancedFilters.bond" preset="bond-selector" width="200px" />
        </n-form-item>

        <n-form-item label="多选证券">
          <SecuritySelector
            v-model="advancedFilters.multiSec"
            preset="multi-security"
            width="300px"
          />
        </n-form-item>

        <n-form-item>
          <n-space>
            <n-button type="info" @click="handleAdvancedQuery"> 高级查询 </n-button>
            <n-button @click="handleAdvancedReset"> 重置筛选 </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <!-- 自定义API示例 -->
    <n-card title="自定义API示例" style="margin-top: 20px">
      <n-form inline label-placement="left">
        <n-form-item label="自定义证券搜索">
          <UniversalSecuritySelector
            v-model:value="customSecCode"
            api-type="custom"
            :custom-api="customSecurityApi"
            :custom-render-label="customRenderLabel"
            placeholder="使用自定义API搜索"
            width="300px"
            @select="handleCustomSelect"
          />
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, h, onMounted } from 'vue';
  import { useMessage, NButton, NTag } from 'naive-ui';
  import { UniversalSecuritySelector, SecuritySelector } from '../index';
  import type { BaseSecurityData, SearchParams, ApiResponse } from '../types';

  const message = useMessage();

  // 响应式数据
  const secType = ref<string | null>(null);
  const secCode = ref<string | null>(null);
  const customSecCode = ref<string | null>(null);
  const loading = ref(false);
  const tableData = ref([]);

  // 高级筛选
  const advancedFilters = reactive({
    stock: null as string | null,
    fund: null as string | null,
    bond: null as string | null,
    multiSec: [] as string[],
  });

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
  });

  // 表格列配置
  const columns = [
    {
      title: '证券代码',
      key: 'secCode',
      width: 120,
    },
    {
      title: '证券名称',
      key: 'secName',
      width: 150,
      render(row: any) {
        return h(
          NButton,
          {
            text: true,
            type: 'primary',
            onClick: () => handleViewDetail(row),
          },
          { default: () => row.secName }
        );
      },
    },
    {
      title: '证券类型',
      key: 'secType',
      width: 100,
      render(row: any) {
        const typeMap: Record<string, { label: string; type: string }> = {
          '0': { label: '股票', type: 'info' },
          '1': { label: '基金', type: 'warning' },
          '2': { label: '债券', type: 'success' },
        };
        const typeInfo = typeMap[row.secType] || { label: '未知', type: 'default' };
        return h(NTag, { type: typeInfo.type as any }, { default: () => typeInfo.label });
      },
    },
    {
      title: '最新价格',
      key: 'price',
      width: 100,
      align: 'right',
    },
    {
      title: '涨跌幅',
      key: 'changePercent',
      width: 100,
      align: 'right',
      render(row: any) {
        const value = row.changePercent;
        const color = value > 0 ? 'red' : value < 0 ? 'green' : 'gray';
        return h('span', { style: { color } }, `${value > 0 ? '+' : ''}${value}%`);
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render(row: any) {
        return h('div', [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: { marginRight: '8px' },
              onClick: () => handleEdit(row),
            },
            { default: () => '编辑' }
          ),
          h(
            NButton,
            {
              size: 'small',
              type: 'error',
              onClick: () => handleDelete(row),
            },
            { default: () => '删除' }
          ),
        ]);
      },
    },
  ];

  // 事件处理函数
  const handleSecTypeSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('证券类型选择:', { option, value });
    secType.value = value;
    // 自动触发查询
    handleQuery();
  };

  const handleSecuritySelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('证券选择:', { option, value });
    secCode.value = value;
    if (option) {
      message.success(
        `选择了证券: ${option.name || option.secName} (${option.code || option.secCode})`
      );
    }
  };

  const handleCustomSelect = (option: BaseSecurityData | null, value: string | null) => {
    console.log('自定义证券选择:', { option, value });
    if (option) {
      message.success(`通过自定义API选择了: ${option.customName} (${option.customCode})`);
    }
  };

  // 查询函数
  const handleQuery = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟数据
      tableData.value = Array.from({ length: pagination.pageSize }, (_, index) => ({
        id: pagination.page * pagination.pageSize + index + 1,
        secCode: `00000${index + 1}`.slice(-6),
        secName: `测试证券${index + 1}`,
        secType: ['0', '1', '2'][index % 3],
        price: (Math.random() * 100 + 10).toFixed(2),
        changePercent: (Math.random() * 20 - 10).toFixed(2),
      }));

      pagination.itemCount = 100; // 模拟总数
      message.success('查询成功');
    } catch (error) {
      message.error('查询失败');
    } finally {
      loading.value = false;
    }
  };

  const handleReset = () => {
    secType.value = null;
    secCode.value = null;
    pagination.page = 1;
    handleQuery();
    message.info('已重置筛选条件');
  };

  const handleAdvancedQuery = () => {
    console.log('高级查询参数:', advancedFilters);
    message.info('执行高级查询');
    handleQuery();
  };

  const handleAdvancedReset = () => {
    advancedFilters.stock = null;
    advancedFilters.fund = null;
    advancedFilters.bond = null;
    advancedFilters.multiSec = [];
    message.info('已重置高级筛选');
  };

  // 表格事件处理
  const handlePageChange = (page: number) => {
    pagination.page = page;
    handleQuery();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    handleQuery();
  };

  const handleSorterChange = (sorter: any) => {
    console.log('排序变更:', sorter);
    handleQuery();
  };

  const handleViewDetail = (row: any) => {
    message.info(`查看详情: ${row.secName}`);
  };

  const handleEdit = (row: any) => {
    message.info(`编辑: ${row.secName}`);
  };

  const handleDelete = (row: any) => {
    message.warning(`删除: ${row.secName}`);
  };

  // 自定义API函数
  const customSecurityApi = async (params: SearchParams): Promise<ApiResponse> => {
    // 模拟自定义API调用
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockData = Array.from({ length: 10 }, (_, index) => ({
      customCode: `CUSTOM${index + 1}`,
      customName: `自定义证券${index + 1}`,
      customType: 'custom',
      customCategory: '自定义分类',
    }));

    return {
      code: 200,
      data: {
        records: mockData.filter(
          (item) =>
            !params.queryStr ||
            item.customName.includes(params.queryStr) ||
            item.customCode.includes(params.queryStr)
        ),
        total: mockData.length,
      },
    };
  };

  // 自定义渲染函数
  const customRenderLabel = (option: any) => {
    return [
      h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, option.customName),
      h('span', { style: { marginLeft: '8px', color: '#666' } }, `(${option.customCode})`),
      h(
        NTag,
        {
          type: 'warning',
          size: 'small',
          style: { marginLeft: '8px' },
        },
        { default: () => option.customCategory }
      ),
    ];
  };

  // 组件挂载时执行初始查询
  onMounted(() => {
    handleQuery();
  });
</script>

<style scoped>
  .peer-data-enhanced {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .n-card {
    margin-bottom: 20px;
  }

  .n-form-item {
    margin-bottom: 16px;
  }
</style>
