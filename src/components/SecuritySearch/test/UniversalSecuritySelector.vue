<template>
  <!-- 通用证券选择器组件 -->
  <div class="universal-security-selector">
    <div class="selector-wrapper">
      <!-- 搜索图标 -->
      <div v-if="showSearchIcon" class="search-icon-wrapper">
        <n-icon :size="iconSize" class="search-icon">
          <SearchOutline />
        </n-icon>
      </div>

      <!-- 主要选择器 -->
      <n-select
        v-model:value="selectedValues"
        :clear-filter-after-select="false"
        :clearable="clearable"
        :disabled="disabled"
        :filterable="filterable"
        :label-field="labelField"
        :loading="loading"
        :max-tag-count="maxTagCount"
        :multiple="multiple"
        :options="options"
        :placeholder="placeholder || '请输入证券代码或名称'"
        :remote="remote"
        :render-label="renderLabel"
        :render-tag="renderTag"
        :size="size"
        :style="{ width: width || '300px' }"
        :value-field="valueField"
        class="security-select"
        @focus="handleFocus"
        @scroll="handleScroll"
        @search="handleSearch"
        @update:value="handleUpdateValue"
      >
        <template #arrow>
          <n-icon>
            <ChevronDownOutline />
          </n-icon>
        </template>
        <template #empty>
          <div class="empty-state">
            <n-icon class="empty-icon" size="24">
              <DocumentTextOutline />
            </n-icon>
            <span class="empty-text">{{ emptyText || '暂无匹配的证券' }}</span>
          </div>
        </template>
      </n-select>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, computed, type VNodeChild } from 'vue';
  import {
    useMessage,
    type SelectOption,
    type SelectGroupOption,
    NText,
    NTag,
    NSpace,
  } from 'naive-ui';
  import { getEnumProperties } from '@/enums/baseEnum';
  import { SecStatusInfo, SecTypeInfo } from '@/enums/secEnum';
  import {
    SearchOutline,
    ChevronDownOutline,
    DocumentTextOutline,
    TrendingUpOutline,
  } from '@vicons/ionicons5';

  // 组件属性接口
  interface Props {
    // 基础配置
    placeholder?: string;
    width?: string;
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;

    // 功能配置
    multiple?: boolean;
    maxTagCount?: number;
    clearable?: boolean;
    filterable?: boolean;
    remote?: boolean;
    showSearchIcon?: boolean;

    // 数据配置
    apiType?: 'stock' | 'security' | 'fund' | 'custom';
    secTypeFilter?: string | string[];
    labelField?: string;
    valueField?: string;

    // 显示配置
    showSecType?: boolean;
    showSecStatus?: boolean;
    showSecCategory?: boolean;
    showIcon?: boolean;
    iconSize?: number;

    // 自定义配置
    customApi?: Function;
    customRenderLabel?: Function;
    customRenderTag?: Function;
    emptyText?: string;

    // 默认值
    defaultValue?: string | string[] | null;
  }

  // 定义props
  const props = withDefaults(defineProps<Props>(), {
    size: 'medium',
    disabled: false,
    multiple: false,
    maxTagCount: 2,
    clearable: true,
    filterable: true,
    remote: true,
    showSearchIcon: true,
    apiType: 'stock',
    labelField: 'name',
    valueField: 'code',
    showSecType: false,
    showSecStatus: false,
    showSecCategory: false,
    showIcon: true,
    iconSize: 20,
    emptyText: '暂无匹配的证券',
  });

  // 定义emits
  const emit = defineEmits<{
    'update:value': [value: string | string[] | null];
    select: [option: any, value: string | string[] | null];
    clear: [];
    search: [query: string];
    focus: [];
    scroll: [event: Event];
  }>();

  // 响应式数据
  const message = useMessage();
  const selectedValues = ref<string | string[] | null>(props.defaultValue || null);
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);
  const current = ref<number>(1);

  // 计算属性
  const apiFunction = computed(() => {
    if (props.customApi) return props.customApi;

    // switch (props.apiType) {
    //   case 'stock':
    //     return listCodeAndName;
    //   case 'security':
    //   case 'fund':
    //     return vagueQuerySecCodes;
    //   default:
    //     return listCodeAndName;
    // }
  });

  // 渲染标签函数
  const renderLabel = (option: any): VNodeChild => {
    if (props.customRenderLabel) {
      return props.customRenderLabel(option);
    }

    const elements: VNodeChild[] = [];

    // 添加图标
    if (props.showIcon) {
      elements.push(
        h(
          'div',
          {
            style: {
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold',
              marginRight: '8px',
              verticalAlign: 'middle',
            },
          },
          [h('span', {}, getSecTypeIcon(option.secType || option.type))]
        )
      );
    }

    // 添加证券名称
    elements.push(option[props.labelField] as string);

    // 添加证券代码
    elements.push(
      h(
        NText,
        {
          depth: '3',
          style: { marginLeft: '8px' },
        },
        { default: () => `(${option[props.valueField]})` }
      )
    );

    // 添加标签容器
    const tags: VNodeChild[] = [];

    // 证券类型标签
    if (props.showSecType && (option.secType || option.type)) {
      const secTypeInfo = getEnumProperties(SecTypeInfo, option.secType || option.type);
      tags.push(
        h(
          NTag,
          {
            type: secTypeInfo.type as 'success' | 'warning' | 'error' | 'info',
            bordered: false,
            size: 'small',
          },
          { default: () => secTypeInfo.label }
        )
      );
    }

    // 证券状态标签
    if (props.showSecStatus && option.status) {
      const statusInfo = getEnumProperties(SecStatusInfo, option.status);
      tags.push(
        h(
          NTag,
          {
            type: statusInfo.type as 'success' | 'warning' | 'error' | 'info',
            bordered: false,
            size: 'small',
          },
          { default: () => statusInfo.label }
        )
      );
    }

    // 证券分类标签
    if (props.showSecCategory && option.secCategory) {
      tags.push(
        h(
          NTag,
          {
            type: 'default',
            bordered: false,
            size: 'small',
          },
          { default: () => option.secCategory }
        )
      );
    }

    // 如果有标签，添加标签容器
    if (tags.length > 0) {
      elements.push(
        h(
          NSpace,
          {
            style: 'position: absolute; right: 10px; top: 50%; transform: translateY(-50%);',
            size: 'small',
          },
          { default: () => tags }
        )
      );
    }

    return elements;
  };

  // 自定义选中状态渲染函数
  const renderTag = ({ option }: any): VNodeChild => {
    if (props.customRenderTag) {
      return props.customRenderTag({ option });
    }

    return h(
      'span',
      {
        style: {
          fontSize: '16px',
          fontWeight: '600',
          color: '#374151',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          display: 'flex',
          alignItems: 'center',
          height: '100%',
          width: '100%',
          padding: '0',
          margin: '0',
          lineHeight: '1.5',
        },
      },
      `${option[props.labelField]} (${option[props.valueField]})`
    );
  };

  // 获取证券类型图标
  const getSecTypeIcon = (secType: string | number): string => {
    const typeMap: Record<string, string> = {
      '0': '股',
      '1': '基',
      '2': '债',
      STOCK: '股',
      FUND: '基',
      BOND: '债',
    };
    return typeMap[String(secType)] || '券';
  };

  // 搜索处理函数
  const handleSearch = (val: string): void => {
    current.value = 1;
    fetchData(val);
    emit('search', val);
  };

  // 聚焦处理函数
  const handleFocus = (): void => {
    if (options.value.length <= 0) {
      fetchData('');
    }
    emit('focus');
  };

  // 滚动处理函数（分页加载）
  const handleScroll = (e: Event): void => {
    if (!props.remote) return;

    const currentTarget = e.currentTarget as HTMLElement;
    if (currentTarget.scrollTop + currentTarget.offsetHeight >= currentTarget.scrollHeight) {
      current.value++;
      fetchData(selectedValues.value as string, true);
    }
    emit('scroll', e);
  };

  // 获取数据
  const fetchData = async (query: string = '', append: boolean = false): Promise<void> => {
    loading.value = true;
    try {
      const params = buildApiParams(query);
      const { data, code, msg } = await apiFunction.value(params);

      if (code === 200) {
        const records = processApiResponse(data.records || data);

        if (append) {
          options.value = options.value.concat(records);
        } else {
          options.value = records;
        }
      } else {
        message.error(msg || '搜索失败');
      }
    } catch (error) {
      message.error('搜索失败，请重试');
      console.error('Security search error:', error);
    } finally {
      loading.value = false;
    }
  };

  // 构建API参数
  const buildApiParams = (query: string) => {
    const baseParams = {
      size: '30',
      current: current.value,
    };

    switch (props.apiType) {
      case 'stock':
        return {
          ...baseParams,
          searchKeyWords: query,
        };
      case 'security':
      case 'fund':
        return {
          ...baseParams,
          queryStr: query,
          secTypeEnum: props.secTypeFilter,
        };
      default:
        return {
          ...baseParams,
          searchKeyWords: query,
        };
    }
  };

  // 处理API响应
  const processApiResponse = (records: any[]) => {
    return records.map((item) => {
      // 根据API类型处理数据格式
      if (props.apiType === 'security' || props.apiType === 'fund') {
        return {
          ...item,
          [props.valueField]: item.secCode || item.code,
          [props.labelField]: item.secName || item.name,
        };
      }
      return item;
    });
  };

  // 处理值更新
  const handleUpdateValue = (): void => {
    const selectedOption = options.value.find(
      (item: any) =>
        item[props.valueField] === selectedValues.value ||
        (Array.isArray(selectedValues.value) &&
          selectedValues.value.includes(item[props.valueField]))
    );

    emit('update:value', selectedValues.value);
    emit('select', selectedOption, selectedValues.value);
  };

  // 清除选中值
  const clearSelectedValue = (): void => {
    selectedValues.value = props.multiple ? [] : null;
    options.value = [];
    emit('update:value', selectedValues.value);
    emit('clear');
  };

  // 设置选中值
  const setSelectedValue = (value: string | string[] | null): void => {
    selectedValues.value = value;
    emit('update:value', value);
  };

  // 暴露方法给父组件
  defineExpose({
    clearSelectedValue,
    setSelectedValue,
    fetchData,
  });
</script>

<style scoped>
  /* 容器样式 */
  .universal-security-selector {
    width: 100%;
    position: relative;
  }

  .selector-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }

  /* 搜索图标样式 */
  .search-icon-wrapper {
    position: absolute;
    left: 12px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 10;
    pointer-events: none;
  }

  .search-icon {
    color: #6b7280;
    transition: color 0.2s ease;
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 24px;
    color: #9ca3af;
  }

  .empty-icon {
    color: #d1d5db;
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
  }

  /* 响应式适配 */
  @media (max-width: 768px) {
    .search-icon-wrapper {
      left: 10px;
    }
  }
</style>

<!-- 全局样式覆盖 -->
<style>
  /* 通用证券选择器样式优化 */
  .universal-security-selector .security-select {
    width: 100%;
  }

  .universal-security-selector .security-select .n-base-selection {
    min-height: 52px !important;
    border-radius: 10px !important;
    border: 2px solid #d1d5db !important;
    background: white !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    padding-left: 44px !important;
    padding-right: 12px !important;
    display: flex !important;
    align-items: center !important;
  }

  .universal-security-selector .security-select .n-base-selection.n-base-selection--focused {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  /* 修复选中状态下的文字位置偏移问题 */
  .universal-security-selector .security-select .n-base-selection.n-base-selection--selected {
    padding-left: 44px !important;
    padding-right: 12px !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection.n-base-selection--selected:active {
    padding-left: 44px !important;
    padding-right: 12px !important;
  }

  .universal-security-selector .security-select .n-base-selection.n-base-selection--selected:focus {
    padding-left: 44px !important;
    padding-right: 12px !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection.n-base-selection--selected.n-base-selection--focused {
    padding-left: 44px !important;
    padding-right: 12px !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection.n-base-selection--focused
    .search-icon {
    color: #3b82f6;
  }

  .universal-security-selector .security-select .n-base-selection-input {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #374151 !important;
    line-height: 1.5 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .universal-security-selector .security-select .n-base-selection-placeholder {
    font-size: 16px !important;
    color: #9ca3af !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
  }

  /* 占位符文字与图标垂直对齐 */
  .universal-security-selector .security-select .n-base-selection-input__content {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
  }

  /* 选中状态下的文字显示优化 */
  .universal-security-selector .security-select .n-base-selection-label {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    height: 100% !important;
    overflow: visible !important;
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
  }

  /* 确保选中状态在所有交互状态下文字位置保持一致 */
  .universal-security-selector
    .security-select
    .n-base-selection--selected
    .n-base-selection-label {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:active
    .n-base-selection-label {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:focus
    .n-base-selection-label {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector .security-select .n-base-selection-tags {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    height: 100% !important;
    overflow: visible !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 确保tags在所有状态下位置一致 */
  .universal-security-selector .security-select .n-base-selection--selected .n-base-selection-tags {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:active
    .n-base-selection-tags {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:focus
    .n-base-selection-tags {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  /* 选中状态文字内容样式 */
  .universal-security-selector .security-select .n-base-selection-label > span,
  .universal-security-selector .security-select .n-base-selection-tags > span {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    max-width: 100% !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
    transform: none !important;
  }

  /* 确保选中状态下文字内容在所有交互状态下位置一致 */
  .universal-security-selector
    .security-select
    .n-base-selection--selected
    .n-base-selection-label
    > span,
  .universal-security-selector
    .security-select
    .n-base-selection--selected
    .n-base-selection-tags
    > span {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:active
    .n-base-selection-label
    > span,
  .universal-security-selector
    .security-select
    .n-base-selection--selected:active
    .n-base-selection-tags
    > span {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  .universal-security-selector
    .security-select
    .n-base-selection--selected:focus
    .n-base-selection-label
    > span,
  .universal-security-selector
    .security-select
    .n-base-selection--selected:focus
    .n-base-selection-tags
    > span {
    padding: 0 !important;
    margin: 0 !important;
    transform: none !important;
  }

  /* 下拉选项样式优化 */
  .universal-security-selector .n-base-select-menu .n-base-select-option {
    position: relative !important;
    padding: 8px 12px !important;
    border-radius: 8px !important;
    margin: 2px 4px !important;
    transition: all 0.2s ease !important;
  }

  .universal-security-selector .n-base-select-menu .n-base-select-option:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  }

  .universal-security-selector
    .n-base-select-menu
    .n-base-select-option.n-base-select-option--selected {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
    color: #1e40af !important;
  }

  /* 加载状态样式 */
  .universal-security-selector .security-select .n-base-loading {
    color: #3b82f6 !important;
  }

  /* 响应式适配 */
  @media (max-width: 768px) {
    .universal-security-selector .security-select .n-base-selection {
      min-height: 48px !important;
      padding-left: 40px !important;
      padding-right: 12px !important;
    }

    .universal-security-selector .security-select .n-base-selection-input {
      font-size: 14px !important;
    }

    .universal-security-selector .security-select .n-base-selection-placeholder {
      font-size: 14px !important;
    }
  }
</style>
