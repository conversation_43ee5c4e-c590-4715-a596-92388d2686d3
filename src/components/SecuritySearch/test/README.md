# 通用证券选择器组件 (UniversalSecuritySelector)

## 📋 概述

`UniversalSecuritySelector` 是一个高度可配置的通用证券选择器组件，基于 Naive UI 的 `n-select` 组件开发，支持股票、基金、债券等多种证券类型的搜索和选择。

## ✨ 特性

- 🔍 **智能搜索**: 支持远程搜索，实时匹配证券代码和名称
- 🎯 **多种类型**: 支持股票、基金、债券等多种证券类型
- 🏷️ **丰富标签**: 可显示证券类型、状态、分类等标签信息
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🎨 **高度定制**: 支持自定义渲染、API接口、样式等
- 🔄 **多选支持**: 支持单选和多选模式
- ⚡ **性能优化**: 分页加载、防抖搜索、缓存机制

## 🚀 快速开始

### 基础用法

```vue
<template>
  <UniversalSecuritySelector
    v-model:value="selectedValue"
    api-type="stock"
    placeholder="请输入股票代码或名称"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref } from 'vue';
import UniversalSecuritySelector from '@/components/SecuritySearch/UniversalSecuritySelector.vue';

const selectedValue = ref(null);

const handleSelect = (option, value) => {
  console.log('选择了:', option, value);
};
</script>
```

## 📚 API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `placeholder` | `string` | `'请输入证券代码或名称'` | 占位符文本 |
| `width` | `string` | `'300px'` | 组件宽度 |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 组件尺寸 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `multiple` | `boolean` | `false` | 是否多选 |
| `maxTagCount` | `number` | `2` | 多选时最大显示标签数 |
| `clearable` | `boolean` | `true` | 是否可清空 |
| `filterable` | `boolean` | `true` | 是否可过滤 |
| `remote` | `boolean` | `true` | 是否远程搜索 |
| `showSearchIcon` | `boolean` | `true` | 是否显示搜索图标 |
| `apiType` | `'stock' \| 'security' \| 'fund' \| 'custom'` | `'stock'` | API类型 |
| `secTypeFilter` | `string \| string[]` | `undefined` | 证券类型过滤 |
| `labelField` | `string` | `'name'` | 标签字段名 |
| `valueField` | `string` | `'code'` | 值字段名 |
| `showSecType` | `boolean` | `false` | 是否显示证券类型标签 |
| `showSecStatus` | `boolean` | `false` | 是否显示证券状态标签 |
| `showSecCategory` | `boolean` | `false` | 是否显示证券分类标签 |
| `showIcon` | `boolean` | `true` | 是否显示图标 |
| `iconSize` | `number` | `20` | 图标尺寸 |
| `customApi` | `Function` | `undefined` | 自定义API函数 |
| `customRenderLabel` | `Function` | `undefined` | 自定义标签渲染函数 |
| `customRenderTag` | `Function` | `undefined` | 自定义选中标签渲染函数 |
| `emptyText` | `string` | `'暂无匹配的证券'` | 空状态文本 |
| `defaultValue` | `string \| string[] \| null` | `null` | 默认值 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `(value: string \| string[] \| null)` | 值更新事件 |
| `select` | `(option: BaseSecurityData \| null, value: string \| string[] \| null)` | 选择事件 |
| `clear` | `()` | 清空事件 |
| `search` | `(query: string)` | 搜索事件 |
| `focus` | `()` | 聚焦事件 |
| `scroll` | `(event: Event)` | 滚动事件 |

### Exposed Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `clearSelectedValue` | `()` | `void` | 清除选中值 |
| `setSelectedValue` | `(value: string \| string[] \| null)` | `void` | 设置选中值 |
| `fetchData` | `(query?: string, append?: boolean)` | `Promise<void>` | 获取数据 |

## 🎯 使用场景

### 1. 基础股票选择器

```vue
<UniversalSecuritySelector
  v-model:value="stockCode"
  api-type="stock"
  placeholder="请输入股票代码或名称"
  :show-sec-status="true"
/>
```

### 2. 全功能证券选择器

```vue
<UniversalSecuritySelector
  v-model:value="securityCode"
  api-type="security"
  :show-sec-type="true"
  :show-sec-status="true"
  :show-sec-category="true"
  placeholder="请输入证券代码或名称"
/>
```

### 3. 基金选择器

```vue
<UniversalSecuritySelector
  v-model:value="fundCode"
  api-type="fund"
  sec-type-filter="1"
  :show-sec-type="true"
  placeholder="请输入基金代码或名称"
/>
```

### 4. 债券选择器

```vue
<UniversalSecuritySelector
  v-model:value="bondCode"
  api-type="security"
  sec-type-filter="2"
  :show-sec-type="true"
  placeholder="请输入债券代码或名称"
/>
```

### 5. 多选证券选择器

```vue
<UniversalSecuritySelector
  v-model:value="selectedCodes"
  api-type="security"
  :multiple="true"
  :max-tag-count="3"
  :show-sec-type="true"
  placeholder="请选择多个证券"
/>
```

### 6. 自定义API

```vue
<UniversalSecuritySelector
  v-model:value="customValue"
  api-type="custom"
  :custom-api="myCustomApi"
  :custom-render-label="myCustomRenderLabel"
  placeholder="使用自定义API"
/>
```

## 🔧 高级配置

### 自定义API函数

```typescript
import type { SearchParams, ApiResponse } from '@/components/SecuritySearch/types';

const myCustomApi = async (params: SearchParams): Promise<ApiResponse> => {
  const response = await fetch('/api/my-securities', {
    method: 'POST',
    body: JSON.stringify(params),
  });
  return response.json();
};
```

### 自定义渲染函数

```typescript
import { h } from 'vue';
import { NTag } from 'naive-ui';

const myCustomRenderLabel = (option: any) => {
  return [
    option.name,
    h(NTag, { type: 'info', size: 'small' }, { default: () => option.customField }),
  ];
};
```

## 🎨 样式定制

组件支持通过CSS变量进行样式定制：

```css
.universal-security-selector {
  --selector-border-color: #d1d5db;
  --selector-border-radius: 10px;
  --selector-padding-left: 44px;
  --selector-min-height: 52px;
  --selector-font-size: 16px;
  --selector-focus-color: #3b82f6;
}
```

## 📱 响应式支持

组件内置响应式支持，在移动端会自动调整样式：

- 小屏幕设备：减小内边距和字体大小
- 触摸设备：优化触摸体验
- 高DPI屏幕：优化图标显示

## 🔄 迁移指南

### 从 StockSearchOptimized 迁移

```vue
<!-- 旧版本 -->
<StockSearchOptimized
  @get-value="handleGetValue"
  placeholder="请输入股票代码"
  width="300px"
/>

<!-- 新版本 -->
<UniversalSecuritySelector
  v-model:value="selectedValue"
  @select="handleSelect"
  placeholder="请输入股票代码"
  width="300px"
  api-type="stock"
/>
```

### 从 SecuritySearchFund 迁移

```vue
<!-- 旧版本 -->
<SecuritySearchFund
  @getValue="handleGetValue"
  :showSecCategory="true"
  secTypeEnum="1"
/>

<!-- 新版本 -->
<UniversalSecuritySelector
  v-model:value="selectedValue"
  @select="handleSelect"
  api-type="fund"
  sec-type-filter="1"
  :show-sec-category="true"
/>
```

## 🐛 常见问题

### Q: 如何处理不同的数据字段映射？

A: 使用 `labelField` 和 `valueField` 属性来映射不同的字段名：

```vue
<UniversalSecuritySelector
  label-field="secName"
  value-field="secCode"
  api-type="security"
/>
```

### Q: 如何实现自定义的搜索逻辑？

A: 使用 `customApi` 属性提供自定义的API函数：

```vue
<UniversalSecuritySelector
  api-type="custom"
  :custom-api="mySearchFunction"
/>
```

### Q: 如何优化大数据量的性能？

A: 组件内置了分页加载和防抖搜索，对于超大数据量，建议：

1. 调整搜索参数的 `size` 值
2. 实现服务端分页
3. 使用虚拟滚动（如需要）

## 📄 许可证

MIT License
