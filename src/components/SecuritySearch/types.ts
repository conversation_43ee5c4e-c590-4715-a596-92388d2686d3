/**
 * @file: types.ts
 * @description: 通用证券选择器组件类型定义
 * @date: 2024/12/19
 * @author: <PERSON>
 */

/**
 * 证券选择器API类型
 */
export type SecurityApiType = 'stock' | 'security' | 'fund' | 'custom';

/**
 * 证券选择器尺寸
 */
export type SecuritySelectorSize = 'small' | 'medium' | 'large';

/**
 * 基础证券数据接口
 */
export interface BaseSecurityData {
  /** 证券代码 */
  code?: string;
  /** 证券名称 */
  name?: string;
  /** 证券代码（备用字段） */
  secCode?: string;
  /** 证券名称（备用字段） */
  secName?: string;
  /** 证券类型 */
  secType?: string | number;
  /** 证券状态 */
  status?: string | number;
  /** 证券分类 */
  secCategory?: string;
  /** 证券类型（备用字段） */
  type?: string | number;
  [key: string]: any;
}

/**
 * 股票搜索API响应数据
 */
export interface StockSearchData extends BaseSecurityData {
  code: string;
  name: string;
  status?: string;
}

/**
 * 证券搜索API响应数据
 */
export interface SecuritySearchData extends BaseSecurityData {
  secCode: string;
  secName: string;
  secType: string | number;
  secCategory?: string;
}

/**
 * API响应格式
 */
export interface ApiResponse<T = any> {
  code: number;
  data:
    | {
        records: T[];
        total?: number;
        current?: number;
        size?: number;
      }
    | T[];
  msg?: string;
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  /** 搜索关键词 */
  searchKeyWords?: string;
  /** 查询字符串 */
  queryStr?: string;
  /** 页面大小 */
  size?: string | number;
  /** 当前页 */
  current?: number;
  /** 证券类型过滤 */
  secTypeEnum?: string | string[];
}

/**
 * 自定义渲染函数类型
 */
export type CustomRenderFunction = (option: BaseSecurityData) => any;

/**
 * 自定义API函数类型
 */
export type CustomApiFunction = (params: SearchParams) => Promise<ApiResponse>;

/**
 * 组件事件类型
 */
export interface SecuritySelectorEvents {
  /** 值更新事件 */
  'update:value': [value: string | string[] | null];
  /** 选择事件 */
  select: [option: BaseSecurityData | null, value: string | string[] | null];
  /** 清空事件 */
  clear: [];
  /** 搜索事件 */
  search: [query: string];
  /** 聚焦事件 */
  focus: [];
  /** 滚动事件 */
  scroll: [event: Event];
}

/**
 * 组件暴露的方法接口
 */
export interface SecuritySelectorExposed {
  /** 清除选中值 */
  clearSelectedValue: () => void;
  /** 设置选中值 */
  setSelectedValue: (value: string | string[] | null) => void;
  /** 获取数据 */
  fetchData: (query?: string, append?: boolean) => Promise<void>;
}

/**
 * 预设配置类型
 */
export interface SecuritySelectorPreset {
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设配置 */
  config: Partial<SecuritySelectorProps>;
}

/**
 * 组件完整属性接口
 */
export interface SecuritySelectorProps {
  // 基础配置
  placeholder?: string;
  width?: string;
  size?: SecuritySelectorSize;
  disabled?: boolean;

  // 功能配置
  multiple?: boolean;
  maxTagCount?: number;
  clearable?: boolean;
  filterable?: boolean;
  remote?: boolean;
  showSearchIcon?: boolean;

  // 数据配置
  apiType?: SecurityApiType;
  secTypeFilter?: string | string[];
  labelField?: string;
  valueField?: string;

  // 显示配置
  showSecType?: boolean;
  showSecStatus?: boolean;
  showSecCategory?: boolean;
  showIcon?: boolean;
  iconSize?: number;

  // 自定义配置
  customApi?: CustomApiFunction;
  customRenderLabel?: CustomRenderFunction;
  customRenderTag?: CustomRenderFunction;
  emptyText?: string;

  // 默认值
  defaultValue?: string | string[] | null;
}

/**
 * 常用预设配置
 */
export const SECURITY_SELECTOR_PRESETS: SecuritySelectorPreset[] = [
  {
    name: 'stock-basic',
    description: '基础股票选择器',
    config: {
      apiType: 'stock',
      showIcon: true,
      showSecType: false,
      showSecStatus: true,
      placeholder: '请输入股票代码或名称',
    },
  },
  {
    name: 'security-full',
    description: '全功能证券选择器',
    config: {
      apiType: 'security',
      showIcon: true,
      showSecType: true,
      showSecStatus: true,
      showSecCategory: true,
      placeholder: '请输入证券代码或名称',
    },
  },
  {
    name: 'fund-selector',
    description: '基金选择器',
    config: {
      apiType: 'fund',
      secTypeFilter: '1',
      showIcon: true,
      showSecType: true,
      placeholder: '请输入基金代码或名称',
    },
  },
  {
    name: 'bond-selector',
    description: '债券选择器',
    config: {
      apiType: 'security',
      secTypeFilter: '2',
      showIcon: true,
      showSecType: true,
      placeholder: '请输入债券代码或名称',
    },
  },
  {
    name: 'multi-security',
    description: '多选证券选择器',
    config: {
      apiType: 'security',
      multiple: true,
      maxTagCount: 3,
      showIcon: true,
      showSecType: true,
      placeholder: '请选择多个证券',
    },
  },
];

/**
 * 获取预设配置
 */
export function getPresetConfig(presetName: string): Partial<SecuritySelectorProps> | null {
  const preset = SECURITY_SELECTOR_PRESETS.find((p) => p.name === presetName);
  return preset ? preset.config : null;
}

/**
 * 合并配置
 */
export function mergeConfig(
  baseConfig: Partial<SecuritySelectorProps>,
  customConfig: Partial<SecuritySelectorProps>
): Partial<SecuritySelectorProps> {
  return {
    ...baseConfig,
    ...customConfig,
  };
}
