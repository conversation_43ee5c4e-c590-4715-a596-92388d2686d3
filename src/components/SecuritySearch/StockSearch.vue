<!--  通用股票搜索封装（不包含基金/债券），证券代码不带后缀 尽量替换成SecurityInfoSearch -->
<template>
  <div class="w-full">
    <n-select
      v-model:value="selectedValues"
      :clear-filter-after-select="false"
      :loading="loading"
      :options="options"
      :placeholder="placeholder || '请输入证券代码或名称'"
      :render-label="renderLabel"
      :size="size || ''"
      :style="{ width: width || '234px' }"
      clearable
      filterable
      label-field="name"
      remote
      value-field="code"
      @focus="focusSearch"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
    <!--  <n-button @click="transValue"></n-button>-->
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { useMessage, SelectOption, SelectGroupOption, NText, NTag } from 'naive-ui';
  import { getEnumProperties } from '@/enums/baseEnum';
  import { SecStatusInfo, SecTypeInfo } from '@/enums/secEnum';
  import { listCodeAndName } from '@/api/sec/secInfoApi';
  const message = useMessage();
  interface Props {
    placeholder?: string;
    width?: string;
    size?: string;
  }

  defineProps<Props>();

  const selectedValues = ref<string | null>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  const renderLabel = (option) => {
    return [
      option.name as string,
      h(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.code,
        }
      ),
      option.status && option.status != 0
        ? h(
            NTag,
            {
              type: SecStatusInfo[option.status]?.type as 'success' | 'warning' | 'error' | 'info',
              bordered: false,
              size: 'small',
              style: 'position: absolute;right: 10px;',
            },
            {
              default: () => SecStatusInfo[option.status].label,
            }
          )
        : '',
    ];
  };
  //搜索
  const handleSearch = (val) => {
    getlistCodeAndName({ searchKeyWords: val, size: '30' });
  };

  //搜索
  const focusSearch = () => {
    if (options.value.length <= 0) {
      getlistCodeAndName({ searchKeyWords: '', size: '30' });
    }
  };

  const getlistCodeAndName = async (params) => {
    loading.value = true;
    const { data, code, msg } = await listCodeAndName(params);
    loading.value = false;
    if (code === 200) {
      options.value = data.records;
    } else {
      message.error(msg);
    }
  };

  const emit = defineEmits([
    'getValue', // 🤮 保留旧的垃圾命名，向后兼容
    'onStockSelected', // ✅ 新的语义化命名
  ]);

  /**
   * 处理股票选择事件
   * 同时触发新旧两个事件，保证向后兼容
   */
  const handleUpdateValue = () => {
    const selectedStock = options.value.filter((item) => item.code === selectedValues.value);
    const stockCode = selectedValues.value;
    const stockName = selectedStock && selectedStock[0] ? selectedStock[0].name : '';

    // 🤮 触发旧的垃圾事件（向后兼容）
    emit('getValue', stockCode, stockName);

    // ✅ 触发新的语义化事件
    emit('onStockSelected', {
      stockCode,
      stockName,
      stockData: selectedStock[0] || null,
    });
  };
  // 定义一个方法来更新 selectedValues
  const clearSelectedValue = () => {
    selectedValues.value = null;
    options.value = [];
  };

  // 暴露方法给父组件
  defineExpose({
    clearSelectedValue,
  });
</script>
