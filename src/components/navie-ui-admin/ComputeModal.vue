<template>
  <div>
    <n-modal v-model:show="computeModal" :close-on-esc="false" :mask-closable="false">
      <n-card style="width: 400px">
        <n-result
          :status="status == 1 ? 'warning' : status == 2 ? 'success' : 'error'"
          :title="
            status == 1
              ? typeName + '中'
              : status == 2
              ? typeName + '完成'
              : status == 4
              ? computeMsg
              : typeName + '失败'
          "
        />
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  //computeStatus 1计算中 2计算完成 3计算失败 4计算失败提示信息
  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    typeName: {
      type: String,
      required: true,
    },
    status: {
      type: Number,
      default: 1,
    },
    computeMsg: {
      type: String,
      required: true,
    },
  });
  const computeModal = ref(false);

  watch(
    () => props.loading,
    (newVal, oldValV) => {
      computeModal.value = newVal;
    }
  );
</script>

<style scoped></style>
