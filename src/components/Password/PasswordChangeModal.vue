<template>
  <n-modal
    v-model:show="visible"
    class="custom-card"
    preset="card"
    title="修改密码"
    size="medium"
    :bordered="false"
    :closable="true"
    :mask-closable="false"
    style="width: 500px"
  >
    <PasswordChangeForm
      :loading="loading"
      :show-cancel-button="true"
      :show-detailed-requirements="false"
      submit-text="确认修改"
      submit-loading-text="修改中..."
      cancel-text="取消"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </n-modal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useMessage } from 'naive-ui';
  import { useRouter } from 'vue-router';
  import { PasswordChangeForm } from '@/components/Password';
  import { changePassword } from '@/api/system/user';
  import { ResultEnum } from '@/enums/base/httpEnum';
  import { useUserStore } from '@/store/modules/user';
  import { TABS_ROUTES } from '@/store/mutation-types';

  interface Props {
    /** 是否显示弹窗 */
    show?: boolean;
  }

  interface Emits {
    /** 更新显示状态 */
    'update:show': [value: boolean];
    /** 修改成功事件 */
    success: [];
    /** 取消事件 */
    cancel: [];
  }

  const props = withDefaults(defineProps<Props>(), {
    show: false,
  });

  const emit = defineEmits<Emits>();

  const message = useMessage();
  const router = useRouter();
  const userStore = useUserStore();

  // 状态
  const loading = ref(false);

  // 双向绑定显示状态
  const visible = computed({
    get: () => props.show,
    set: (value) => emit('update:show', value),
  });

  // 提交表单
  const handleSubmit = async (data: { oldPassword: string; newPassword: string }) => {
    try {
      loading.value = true;

      const response = await changePassword(data.oldPassword, data.newPassword);

      if (response.code === ResultEnum.SUCCESS) {
        message.success('密码修改成功！');
        message.info('即将跳转登录页面');

        // 关闭弹窗
        visible.value = false;

        // 延迟跳转到登录页
        setTimeout(async () => {
          await userStore.logout();
          // 移除标签页
          localStorage.removeItem(TABS_ROUTES);
          router
            .replace({
              name: 'Login',
            })
            .finally(() => {
              location.reload();
            });
        }, 2000);

        emit('success');
      } else {
        message.error(response.msg || '密码修改失败');
      }
    } catch (error: any) {
      message.error('密码修改失败：' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };

  // 取消操作
  const handleCancel = () => {
    visible.value = false;
    emit('cancel');
  };
</script>

<style scoped lang="less">
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }

  :deep(.n-card__content) {
    padding-bottom: 0;
  }

  :deep(.password-change-form) {
    .form-actions {
      display: none; // 隐藏表单内部的按钮，使用弹窗的按钮
    }
  }
</style>
