<template>
  <n-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-placement="top"
    size="large"
    class="password-change-form"
  >
    <!-- 原密码 -->
    <n-form-item label="原密码" path="oldPassword">
      <n-input
        v-model:value="formData.oldPassword"
        type="password"
        placeholder="请输入原密码"
        show-password-on="click"
        :disabled="loading"
        clearable
      />
    </n-form-item>

    <!-- 新密码 -->
    <n-form-item label="新密码" path="newPassword">
      <n-input
        v-model:value="formData.newPassword"
        type="password"
        placeholder="请输入新密码"
        show-password-on="click"
        :disabled="loading"
        clearable
        @input="handlePasswordInput"
      />

      <!-- 密码强度指示器 -->
      <PasswordStrengthIndicator
        :password="formData.newPassword"
        :show-percentage="showStrengthPercentage"
      />
    </n-form-item>

    <!-- 确认新密码 -->
    <n-form-item label="确认新密码" path="confirmPassword">
      <n-input
        v-model:value="formData.confirmPassword"
        type="password"
        placeholder="请再次输入新密码"
        show-password-on="click"
        :disabled="loading"
        clearable
      />
    </n-form-item>

    <!-- 密码要求 -->
    <PasswordRequirements :password="formData.newPassword" :detailed="showDetailedRequirements" />

    <!-- 操作按钮 -->
    <div class="form-actions">
      <n-button
        type="primary"
        size="large"
        :loading="loading"
        :disabled="!isFormValid"
        @click="handleSubmit"
        class="submit-btn"
      >
        {{ loading ? submitLoadingText : submitText }}
      </n-button>

      <n-button
        v-if="showCancelButton"
        size="large"
        @click="handleCancel"
        :disabled="loading"
        class="cancel-btn"
      >
        {{ cancelText }}
      </n-button>

      <slot name="extra-actions" :loading="loading" :isFormValid="isFormValid"></slot>
    </div>
  </n-form>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { useMessage } from 'naive-ui';
  import { validatePasswordComplexity } from '@/utils/password/passwordValidator';
  import PasswordStrengthIndicator from './PasswordStrengthIndicator.vue';
  import PasswordRequirements from './PasswordRequirements.vue';

  interface Props {
    /** 提交按钮文本 */
    submitText?: string;
    /** 提交中按钮文本 */
    submitLoadingText?: string;
    /** 取消按钮文本 */
    cancelText?: string;
    /** 是否显示取消按钮 */
    showCancelButton?: boolean;
    /** 是否显示强度百分比 */
    showStrengthPercentage?: boolean;
    /** 是否显示详细要求 */
    showDetailedRequirements?: boolean;
    /** 是否加载中 */
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    submitText: '确认修改',
    submitLoadingText: '修改中...',
    cancelText: '取消',
    showCancelButton: true,
    showStrengthPercentage: true,
    showDetailedRequirements: false,
    loading: false,
  });

  interface Emits {
    /** 提交事件 */
    submit: [data: { oldPassword: string; newPassword: string }];
    /** 取消事件 */
    cancel: [];
    /** 密码输入事件 */
    passwordInput: [password: string];
  }

  const emit = defineEmits<Emits>();

  const message = useMessage();
  const formRef = ref();
  const loading = computed(() => props.loading);

  // 表单数据
  const formData = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // 表单验证规则
  const rules = {
    oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        validator: (rule: any, value: string) => {
          if (!value) return true;
          return validatePasswordComplexity(value);
        },
        message: '密码必须8-20位，包含至少3种字符类型',
        trigger: 'blur',
      },
    ],
    confirmPassword: [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (rule: any, value: string) => {
          return value === formData.newPassword;
        },
        message: '两次输入的密码不一致',
        trigger: 'blur',
      },
    ],
  };

  // 表单是否有效
  const isFormValid = computed(() => {
    return (
      formData.oldPassword &&
      formData.newPassword &&
      formData.confirmPassword &&
      formData.newPassword === formData.confirmPassword &&
      validatePasswordComplexity(formData.newPassword)
    );
  });

  // 处理密码输入
  const handlePasswordInput = () => {
    emit('passwordInput', formData.newPassword);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();

      emit('submit', {
        oldPassword: formData.oldPassword,
        newPassword: formData.newPassword,
      });
    } catch (error: any) {
      if (error?.length) {
        message.error('请检查表单输入');
      }
    }
  };

  // 取消操作
  const handleCancel = () => {
    emit('cancel');
  };

  // 重置表单
  const resetForm = () => {
    formData.oldPassword = '';
    formData.newPassword = '';
    formData.confirmPassword = '';
    formRef.value?.restoreValidation();
  };

  // 暴露方法给父组件
  defineExpose({
    resetForm,
    validate: () => formRef.value?.validate(),
    restoreValidation: () => formRef.value?.restoreValidation(),
  });
</script>

<style scoped lang="less">
  .password-change-form {
    .n-form-item {
      margin-bottom: 24px;
    }
  }

  .form-actions {
    display: flex;
    gap: 16px;
    margin-top: 32px;
    align-items: center;
  }

  .submit-btn {
    flex: 1;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }

  .cancel-btn {
    height: 48px;
    min-width: 100px;
  }
</style>
