<template>
  <div class="password-strength-indicator" v-if="password">
    <!-- 强度进度条 -->
    <div class="strength-bar">
      <div
        class="strength-fill"
        :class="strengthInfo.level"
        :style="{
          width: strengthInfo.percentage + '%',
          backgroundColor: strengthInfo.color,
        }"
      ></div>
    </div>

    <!-- 强度文本 -->
    <div class="strength-info">
      <span class="strength-text" :style="{ color: strengthInfo.color }">
        密码强度：{{ strengthInfo.text }}
      </span>
      <span class="strength-percentage"> {{ Math.round(strengthInfo.percentage) }}% </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import {
    calculatePasswordStrength,
    type PasswordStrengthInfo,
  } from '@/utils/password/passwordValidator';

  interface Props {
    /** 密码值 */
    password: string;
    /** 是否显示百分比 */
    showPercentage?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    showPercentage: true,
  });

  // 计算密码强度
  const strengthInfo = computed((): PasswordStrengthInfo => {
    if (!props.password) {
      return {
        level: 'weak' as any,
        percentage: 0,
        text: '无',
        color: '#d9d9d9',
      };
    }
    return calculatePasswordStrength(props.password);
  });
</script>

<style scoped lang="less">
  .password-strength-indicator {
    margin-top: 8px;
  }

  .strength-bar {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 6px;
  }

  .strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;

    &.weak {
      background-color: #ff4757;
    }

    &.medium {
      background-color: #ffa502;
    }

    &.strong {
      background-color: #2ed573;
    }
  }

  .strength-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
  }

  .strength-text {
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .strength-percentage {
    color: #666;
    font-size: 11px;
  }
</style>
