<template>
  <div class="password-requirements">
    <h4 class="requirements-title">密码要求：</h4>
    <ul class="requirements-list">
      <li
        v-for="requirement in requirements"
        :key="requirement.key"
        class="requirement-item"
        :class="{ valid: isRequirementMet(requirement.key) }"
      >
        <n-icon
          :component="isRequirementMet(requirement.key) ? CheckmarkCircle : CloseCircle"
          class="requirement-icon"
        />
        <span class="requirement-text">{{ requirement.text }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { NIcon } from 'naive-ui';
  import { CheckmarkCircle, CloseCircle } from '@vicons/ionicons5';
  import {
    checkPasswordComplexity,
    defaultPasswordValidator,
    type PasswordComplexityCheck,
  } from '@/utils/password/passwordValidator';

  interface Props {
    /** 密码值 */
    password: string;
    /** 是否显示详细要求 */
    detailed?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    detailed: false,
  });

  // 获取密码复杂度检查结果
  const complexityCheck = computed((): PasswordComplexityCheck => {
    if (!props.password) {
      return {
        length: false,
        lowercase: false,
        uppercase: false,
        number: false,
        special: false,
        typeCount: 0,
        isValid: false,
      };
    }
    return checkPasswordComplexity(props.password);
  });

  // 获取要求列表
  const requirements = computed(() => {
    return props.detailed
      ? defaultPasswordValidator.getDetailedRequirements()
      : defaultPasswordValidator.getRequirements();
  });

  // 检查要求是否满足
  const isRequirementMet = (key: keyof PasswordComplexityCheck): boolean => {
    const check = complexityCheck.value;

    if (key === 'isValid') {
      // 对于总体验证，检查类型数量是否足够
      return check.typeCount >= 3;
    }

    return Boolean(check[key]);
  };
</script>

<style scoped lang="less">
  .password-requirements {
    margin: 16px 0;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .requirements-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  .requirements-list {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .requirement-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
    transition: color 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &.valid {
      color: #2ed573;

      .requirement-icon {
        color: #2ed573;
      }
    }
  }

  .requirement-icon {
    font-size: 16px;
    color: #ff4757;
    transition: color 0.3s ease;
    flex-shrink: 0;
  }

  .requirement-text {
    line-height: 1.4;
  }
</style>
