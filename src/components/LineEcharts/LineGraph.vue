<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width: '100%' }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, onMounted, ref, Ref, toRefs, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  export default defineComponent({
    props: ['xData', 'yData', 'height', 'showLoading', 'unit'],
    emits: ['parentMethod'],
    setup(props, context) {
      const { xData, yData, height, showLoading, unit } = toRefs(props);
      const chartRef = ref<HTMLDivElement | null>(null);
      const IndustryName = ref(999);
      const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

      //渲染图
      const echartsDraw = () => {
        const myChart = getInstance();

        setOptions({
          grid: {
            top: '20%',
            left: '5%',
            right: '5%',
            bottom: '8%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(1, 13, 19, 0.5)',
            borderWidth: 1,
            axisPointer: {
              type: 'shadow',
            },
            formatter: function (params) {
              var str = '';
              if (params.length > 0) {
                str = params[0].name + '<br/>';
              }
              for (var i = 0; i < params.length; i++) {
                if (params[i].seriesName !== '') {
                  str += params[i].seriesName + ':' + params[i].value + '<br/>';
                }
              }
              return str;
            },
            textStyle: {
              color: 'rgba(212, 232, 254, 1)',
            },
            extraCssText: 'z-index:2',
          },
          legend: {
            right: 20,
            top: 30,
            icon: 'circle',
            itemWidth: 15,
            itemHeight: 10,
            itemGap: 15,
            borderRadius: 4,
            textStyle: {
              color: '#000',
              fontFamily: 'Alibaba PuHuiTi',
              fontSize: 14,
              fontWeight: 400,
            },
          },
          xAxis: {
            type: 'category',
            data: xData.value,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939', //X轴文字颜色
              },
            },
          },
          yAxis: [
            {
              type: 'value',
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#eeeeee',
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                color: '#393939',
                fontSize: 14,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#e7e9ef',
                  type: 'dashed',
                },
              },
            },
          ],
          series: [
            {
              name: unit.value,
              type: 'line',
              symbol: 'circle', //标记的图形为实心圆
              symbolSize: 0, //标记的大小
              lineStyle: {
                width: 3,
              },
              data: yData.value,
            },
          ],
        });
        //点击事件
        myChart?.off('click');
        myChart?.on('click', (params) => {
          let { name, dataIndex } = params;
          if (params.name.indexOf('亿') != -1) {
            context.emit('parentMethod', '');
            IndustryName.value = 999;
          } else if (IndustryName.value == dataIndex) {
            context.emit('parentMethod', '');
            IndustryName.value = 999;
          } else {
            context.emit('parentMethod', name);
            IndustryName.value = dataIndex;
          }
          //刷新
          myChart?.resize();
        });
      };

      watch(
        () => [props.xData],
        () => {
          echartsDraw();
        },
        {
          deep: true,
        }
      );
      onMounted(() => {
        echartsDraw();
      });
      return { chartRef, echartsDraw, showLoading, height };
    },
  });
</script>

<style scoped></style>
