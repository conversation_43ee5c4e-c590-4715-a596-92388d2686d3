<template>
  <!--  KS曲线-->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, Ref, toRefs, watch, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  export default defineComponent({
    props: ['ksData', 'height', 'width', 'showLoading'],
    setup(props) {
      const { height, showLoading, ksData, width } = toRefs(props);
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
      //渲染图
      const echartsDraw = () => {
        // 计算 KS 值
        function calculateKS(data) {
          if (!data) {
            return null;
          }
          let ksValue = 0;
          data.forEach((point) => {
            const difference = Math.abs(point.cumulativePositive - point.cumulativeNegative);
            if (difference > ksValue) {
              ksValue = difference;
            }
          });
          return ksValue;
        }

        const ksValue = calculateKS(ksData.value);

        setOptions({
          tooltip: {
            trigger: 'axis',
          },
          dataZoom: [
            {
              // 设置滚动条的隐藏与显示
              show: true,
            },
          ],
          legend: {},
          grid: {
            top: '2%',
            left: '5%',
            right: '4%',
            bottom: '23%',
          },
          title: {
            subtext: `KS 值: ${ksValue && ksValue.toFixed(4)}`,
            left: '60px',
          },
          xAxis: {
            inverse: true,
            type: 'value',
            name: '阈值',
            min: 0,
            max: 1,
            splitLine: {
              show: false,
            },
          },
          yAxis: {
            type: 'value',
            name: '累积概率',
            min: 0,
            max: 1,
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              name: '真正类率(TPR)',
              type: 'line',
              symbol: 'none',
              data:
                ksData.value &&
                ksData.value.map((item) => [item.threshold, item.cumulativePositive]),
              lineStyle: {
                width: 2,
                color: 'blue',
              },
            },
            {
              name: '假正类率(FPR)',
              type: 'line',
              symbol: 'none',
              data:
                ksData.value &&
                ksData.value.map((item) => [item.threshold, item.cumulativeNegative]),
              lineStyle: {
                width: 2,
                color: 'green',
              },
            },
            {
              name: 'KS',
              type: 'line',
              symbol: 'none',
              data:
                ksData.value &&
                ksData.value.map((item) => [
                  item.threshold,
                  Math.abs(item.cumulativePositive - item.cumulativeNegative).toFixed(2),
                ]),
              lineStyle: {
                width: 2,
                color: 'red',
              },
            },
          ],
        });
      };

      watch(
        () => [showLoading.value],
        () => {
          echartsDraw();
        },
        {
          deep: true,
          immediate: true,
        }
      );
      // onMounted(()=>{
      //   echartsDraw();
      // })
      return { chartRef, echartsDraw, showLoading, height, width, ksData };
    },
  });
</script>

<style scoped></style>
