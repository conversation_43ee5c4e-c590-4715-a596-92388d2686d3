<template>
  <!--  ROC曲线
 rocData:[ { fpr: 0, tpr: 0 },]

  -->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { ref, Ref, watch, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  // 定义 props
  interface Props {
    rocData: Array<{ x: number; y: number }>;
    height: string;
    width: string;
    showLoading: boolean;
  }

  const props = defineProps<Props>();

  // 响应式数据
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 计算AUC值
  function calculateAUC(data: Array<{ x: number; y: number }>) {
    let auc = 0;
    for (let i = 1; i < data.length; i++) {
      const width = data[i].x - data[i - 1].x;
      const height = (data[i].y + data[i - 1].y) / 2;
      auc += width * height;
    }
    return auc;
  }

  // 渲染图表
  const echartsDraw = () => {
    const aucValue = calculateAUC(props.rocData);

    setOptions({
      tooltip: {
        backgroundColor: '#ffffff',
        confine: true,
        appendToBody: true,
        formatter: (params: any) => {
          return params.value[2]
            ? '<div style="font-size:12px">' +
                params.value[0] +
                '<br><span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:#1d5a9f"></span> ROC Curve：' +
                params.value[1] +
                '<br/><span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:#9f1d1d"></span> ' +
                params.value[2] +
                '</div>'
            : null;
        },
      },
      legend: {},
      grid: {
        top: '15%',
        left: '6%',
        right: '10%',
        bottom: '13%',
      },
      title: {
        subtext: `AUC: ${aucValue.toFixed(4)}`,
        left: '15%',
      },
      xAxis: {
        type: 'value',
        name: '假阳性率 (FPR)',
        min: 0,
        max: 1,
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        name: '真阳性率 (TPR)',
        min: 0,
        max: 1,
        splitLine: {
          show: false,
        },
      },
      series: [
        {
          name: 'ROC',
          type: 'line',
          smooth: true,
          data: props.rocData && props.rocData.map((item: any) => [item.x, item.y, item.pointName]),
          lineStyle: {
            width: 2,
            color: 'blue',
          },
          areaStyle: {
            color: 'rgba(0, 150, 255, 0.2)',
          },
        },
      ],
    });
  };

  // 监听数据变化
  watch(
    () => props.showLoading,
    () => {
      echartsDraw();
    },
    {
      deep: true,
    }
  );
</script>

<style scoped></style>
