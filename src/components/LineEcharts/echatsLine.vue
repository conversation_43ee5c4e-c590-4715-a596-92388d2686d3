<!--财务分析，线形图封装-->
<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { ref, Ref, toRefs, watch, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  // 定义 props
  interface Props {
    content?: any;
    height?: string;
    width?: string;
    showLoading?: boolean;
  }
  const props = defineProps<Props>();
  const { height, showLoading, content, width } = toRefs(props);

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  //渲染图
  const echartsDraw = () => {
    const data = content.value;
    // const textName = data.textName;
    var dateList = data.dateList;
    var seriesData = data.seriesData;
    var unitName = data.unitName;
    var lendData = <any>[];
    if (seriesData && seriesData.length > 0) {
      seriesData.forEach((row: any) => {
        lendData.push(row.name);
      });
    }
    setOptions({
      dataZoom: [],
      title: {
        // text: textName,
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '5%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      legend: {
        data: lendData,
        x: 'center',
        y: 'bottom',
        textStyle: {
          fontSize: 13,
        },
      },
      xAxis: {
        type: 'category',
        data: dateList,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#888',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#1d057e',
          },
        },
        axisLabel: {
          interval: '0',
          rotate: data.rotate ? data.rotate : 60,
        },
      },
      yAxis: {
        type: 'value',
        name: unitName,
        axisLine: {
          lineStyle: {
            color: '#888',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#888',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#e4e2e5',
          },
        },
      },
      series: seriesData.map((item) => {
        if (item.type === 'line') {
          let data = item.data;
          data[data.length - 1] = {
            value: data[data.length - 1],
            label: {},
          };
          return {
            ...item,
            smooth: true,
            tooltip: {
              valueFormatter: function (value) {
                return value + '%';
              },
            },
            emphasis: {
              focus: 'series',
              lineStyle: {
                width: 3,
              },
            },
          };
        }
      }),
    });
  };

  watch(
    () => [showLoading.value],
    () => {
      echartsDraw();
    },
    {
      deep: true,
    }
  );
  // onMounted(()=>{
  //   echartsDraw();
  // })
</script>

<style scoped>

</style>
