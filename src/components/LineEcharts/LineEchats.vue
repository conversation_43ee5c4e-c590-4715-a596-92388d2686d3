<template>
  <!--  通用线型图封装-->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width: '100%' }"></div>
  </n-spin>
</template>

<script lang="ts">
  import { defineComponent, ref, Ref, toRefs, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  export default defineComponent({
    props: ['xData', 'yData', 'height', 'unit', 'showLoading'],
    emits: ['parentMethod'],
    setup(props, context) {
      const { xData, yData, height, unit, showLoading } = toRefs(props);
      const chartRef = ref<HTMLDivElement | null>(null);
      const IndustryName = ref(999);
      const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

      //渲染图
      const echartsDraw = () => {
        const myChart = getInstance();

        setOptions({
          tooltip: {
            trigger: 'axis',
          },
          xAxis: {
            type: 'category',
            data: xData.value,
          },
          yAxis: {
            type: 'value',
            name: unit.value ? unit.value : '',
          },
          series: [
            {
              label: {
                show: true,
                position: 'top',
              },
              data: yData.value,
              type: 'line',
              smooth: true,
            },
          ],
        });
        //点击事件
        myChart?.off('click');
        myChart?.on('click', (params) => {});
      };

      watch(
        () => [props.xData],
        () => {
          echartsDraw();
        },
        {
          deep: true,
        }
      );
      // onMounted(()=>{
      //   echartsDraw();
      // })
      return { chartRef, echartsDraw, showLoading, height };
    },
  });
</script>

<style scoped></style>
