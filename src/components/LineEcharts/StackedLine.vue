<template>
  <!--  堆叠线型图
参数：
      legendData：图例数据 类型：Array<string> ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
      xAxisData：x轴数据 类型：Array<string> ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      seriesData：堆叠线型图数据 类型：Array<Object>  [ {name: 'Email', data: [120, 132, 101, 134, 90, 230, 210] }]
      height：图表高度 类型：number 400
      width：图表宽度 类型：number 800
      showLoading：是否显示loading 类型：boolean true
  -->
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { ref, Ref, toRefs, watch, onMounted } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  // 定义 props
  interface Props {
    legendData?: any[];
    xAxisData?: any[];
    height?: string;
    width?: string;
    showLoading?: boolean;
    seriesData?: any[];
  }
  const props = defineProps<Props>();
  const { height, showLoading, legendData, width, xAxisData, seriesData } = toRefs(props);

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  //渲染图
  const echartsDraw = () => {
    setOptions({
      dataZoom: [
        {
          // 设置滚动条的隐藏与显示
          show: true,
          // 数据窗口范围的起始数值
          startValue: 0,
          // 数据窗口范围的结束数值（一页显示多少条数据）
          endValue: xAxisData.value.length / 2,
        },
      ],
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data:
          legendData.value && legendData.value[0]
            ? legendData.value
            : ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
      },
      grid: {
        left: '5%',
        right: '4%',
        bottom: '13%',
      },

      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData.value,
      },
      yAxis: {
        type: 'log',
      },
      series: seriesData.value.map((item) => {
        return {
          name: item.name,
          type: 'line',
          stack: 'Total',
          data: item.data,
        };
      }),
    });
  };

  watch(
    () => [showLoading.value],
    () => {
      echartsDraw();
    },
    {
      deep: true,
    }
  );
  // onMounted(()=>{
  //   echartsDraw();
  // })
</script>

<style scoped></style>
