<!--主营业务分类详情-->
<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height,'width':'100%'}"></div>
  </n-spin>
</template>

<script lang="ts">
import {defineComponent, ref, Ref, toRefs, watch} from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
export default defineComponent({
  props: ['contentList','height' ,'xAxis','showLoading'],
  setup(props) {
    const {height,showLoading,contentList,
      xAxis}=toRefs(props);
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
    //渲染图
    const echartsDraw=()=> {
      let legendData=contentList.value.map(item=>item.name);
      const colors = ['#5470C6', '#91CC75', '#EE6666','#50beb5', '#c69554', '#8754c6','#5470C6', '#91CC75', '#EE6666',];
      let yAxisData=<any>[];
      let series=<any>[];
      contentList.value.forEach((item,index)=>{
        yAxisData.push({
          type: 'value',
          name: item.name,
          position: item.name=='当期数额'?'left':'right',
          offset:index*66,
          axisLine: {
            show: true,
            lineStyle: {
              color:colors[index]
            }
          },
          axisLabel: {
            formatter: '{value}'+(item.name=='当期数额'?'亿':'%')
          }
        })

        series.push({
          name: item.name,
          data:item.value,
          yAxisIndex: index,
          tooltip: {
            valueFormatter: function (value) {
              return value + (item.name=='当期数额'?'亿':'%');
            }
          },
          type: item.name=='当期数额'?'bar':'line'

        })
      })
      setOptions(
        {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          grid: {
            right: legendData.length+20+ '%'
          },
          legend: {
            data: legendData
          },
          xAxis: {
            type: 'category',
            data: xAxis.value
          },
          yAxis:yAxisData,
          series:series
        }
      );

    }


    watch(
      () => [contentList.value],
      () => {
        //console.log(12)
        echartsDraw()
      },
      {
        deep: true,
      }
    );
    // onMounted(()=>{
    //   echartsDraw();
    // })
    return { chartRef,echartsDraw,showLoading,height};
  },
});
</script>

<style scoped>

</style>
