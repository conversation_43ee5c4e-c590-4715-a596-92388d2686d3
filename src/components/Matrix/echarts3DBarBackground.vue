<template>
  <div>
    <!--    矩阵通用弹框,背景-->
    <n-spin :show="showMatrixLoading">
      <div ref="chartRef" :style="{ height, width }"></div>
    </n-spin>
    <n-modal
      v-model:show="showModal"
      :bordered="false"
      :title="parameters.type + '详情'"
      class="custom-card mt-8"
      preset="card"
      size="huge"
      style="width: 80%"
    >
      <!--      基本信息-->
      <n-card>
        <template #header>
          <n-h2 prefix="bar" type="error"> 基本信息</n-h2>
        </template>
        <n-descriptions :column="4" bordered label-placement="left">
          <n-descriptions-item label="数量">
            <span style="font-size: 22px; color: #b01313">{{ matrixCellSecNum }}</span>
          </n-descriptions-item>
          <n-descriptions-item label="财务评分区间">
            <span style="font-size: 22px"
              >{{ financialScoreConst?.minValue }}-{{ financialScoreConst?.maxValue }}分</span
            >
          </n-descriptions-item>
          <n-descriptions-item label="综合评分区间">
            <span style="font-size: 22px"
              >{{ overallScoreConst?.minValue }}-{{ overallScoreConst?.maxValue }}分</span
            >
          </n-descriptions-item>
          <n-descriptions-item label="营收区间">
            <span style="font-size: 22px">
              {{ incomeRangeConst?.minValue }}-{{ incomeRangeConst?.maxValue }}亿元</span
            >
          </n-descriptions-item>
        </n-descriptions>
      </n-card>
      <br />

      <!--      股票明细-->
      <n-card>
        <template #header>
          <n-h2 class="mb-0" prefix="bar" type="error"> 股票明细</n-h2>
        </template>
        <pagingTable :data-list="infoList" />
      </n-card>
      <br />
      <n-grid :x-gap="12" :y-gap="10" cols="4" item-responsive>
        <n-grid-item span="2">
          <n-card title="评级分布">
            <strategyDetailBar
              :showLoading="showLoading"
              :xData="levelXAxis"
              :yData="levelYAxis"
              height="250px"
              type="level"
            />
          </n-card>
        </n-grid-item>
        <n-grid-item span="2">
          <n-card title="市值区间">
            <strategyDetailBar
              :showLoading="showLoading"
              :xData="ttmXAxis"
              :yData="ttmYAxis"
              height="250px"
              type="ttm"
            />
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { nextTick, onMounted, reactive, Ref, ref, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import pagingTable from '@/components/Table/matrix/matrixCellInfoTable.vue';
  import { useMessage } from 'naive-ui';
  import strategyDetailBar from '@/views/myDesk/components/PolicyPool/strategyDetailBar.vue';
  import { queryMatrixCellAnalysis } from '@/api/matrix/matrixApi';
  import { ValueRangeVO } from '@/models/common/utilModels';
  import { MatrixCellTableVO } from '@/models/matrix/matrixModels';
  import { MatrixCellAnaReq } from '@/models/matrix/matrixRequestModels';
  import { queryLevelStrategySecMatrixCell } from '@/api/levelStrategy/levelStrategyApi';

  const message = useMessage();

  interface Props {
    /** 横轴数据（分类数据） */
    xData?: string[] | null;
    /** 纵轴数据（分类数据） */
    yData?: string[] | null;
    matrixData?: Array<any>;
    height?: string;
    width?: string;
    showMatrixLoading?: boolean;
    parameters: MatrixCellAnaReq;
  }

  const props = defineProps<Props>();
  const queryRequest = reactive({ ...props.parameters });
  const showModal = ref(false);
  const showLoading = ref(false);
  const matrixCellSecNum = ref<number | null>(null);

  const levelXAxis = ref<(string | null)[]>([]);
  const levelYAxis = ref<(number | null)[]>([]);
  const ttmXAxis = ref<(string | null)[]>([]);
  const ttmYAxis = ref<(number | null)[]>([]);
  const financialScoreConst = ref<ValueRangeVO>({ maxValue: null, minValue: null });
  const incomeRangeConst = ref<ValueRangeVO>({
    maxValue: 0,
    minValue: 0,
  });

  const overallScoreConst = ref<ValueRangeVO>({ maxValue: null, minValue: null });
  const infoList = ref<MatrixCellTableVO[]>([]);

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  /**
   * 获取背景颜色
   * @param max
   * @param number
   */
  const getBackgroundColor = (max: number, number: number) => {
    let maxNumber = max; // 最大数字
    let minColor: number[] = []; // 最浅颜色的RGB值
    let maxColor: number[] = []; // 最深颜色的RGB值
    if (number <= 50) {
      maxNumber = 50;
      minColor = [30, 159, 255];
      maxColor = [161, 204, 236];
    } else if (number > 50 && number <= 100) {
      maxNumber = 100;
      minColor = [255, 176, 11];
      maxColor = [238, 221, 185];
    } else {
      maxNumber = max;
      minColor = [255, 0, 0];
      maxColor = [242, 182, 159];
    }

    // 计算颜色的RGB值
    const color = minColor.map((c, i) => {
      const diff = (maxColor[i] - minColor[i]) / (maxNumber - 0.5);
      return Math.round(maxColor[i] - diff * (number - 0.5));
    });

    // 转换为16进制颜色值
    return '#' + color.map((c) => c.toString(16).padStart(2, '0')).join('');
  };
  //渲染图
  const echartsDraw = () => {
    const myChart = getInstance();

    setOptions({
      title: {
        show: false, //false
        text: '',
      },
      tooltip: {
        backgroundColor: '#ffffff',
        confine: true,
        appendToBody: true,
        formatter: (params) => {
          return (
            '<div style="font-size:12px">' +
            params.value[1] +
            '<br>' +
            params.value[0].replace(/(^.*?,.*?),/, '$1<br/>') +
            '<br/>数量: ' +
            params.value[2] +
            '</div>'
          );
        },
      },

      xAxis3D: {
        type: 'category',

        axisLabel: {
          show: false,
          textStyle: {
            color: '#000',
            fontSize: '12',
          },
          formatter: function (params) {
            return params.length > 30 ? params.replace(/(^.*?,.*?),/, '$1\n') : params;
          },
        },
        data: props.xData,
        splitArea: {
          show: true,
          interval: 7,
          areaStyle: {
            color: ['#eee1bb', '#ecda84'],
          },
        },
      },
      yAxis3D: {
        type: 'category',

        data: props.yData,
      },
      zAxis3D: {
        type: 'value',
      },
      grid3D: {
        show: true,
        boxWidth: 200,
        boxDepth: 80,
        backgroundColor: 'transparent',
        light: {
          main: {
            opacity: 0.3,
            intensity: 0.7,
            // shadow: true
          },
          ambient: {
            intensity: 0.3,
          },
        },
        viewControl: {
          alpha: 50, //视角绕 x 轴，即上下旋转的角度(与beta一起控制视野成像效果)
          beta: -2, //视角绕 y 轴，即左右旋转的角度。
          animation: true,
          projection: 'perspective', // 先设置为这个perspective
          distance: 190, //默认缩放比例
        },

        shadowOffsetX: 140,
      },
      series: [
        {
          name: '详情',
          type: 'bar3D',
          data: props.matrixData.map((item) => {
            if (!item.value) {
              item['value'] = item;
            }
            return {
              value: [item.value[1], item.value[0], item.value[2]],
              itemStyle: {
                color:
                  item.flag == 1
                    ? '#fff200'
                    : item.value[2] == 0
                    ? '#fff'
                    : getBackgroundColor(
                        Math.max.apply(
                          Math,
                          props.matrixData.map((item) => {
                            if (!item.value) {
                              item['value'] = item;
                            }
                            return item.value[2];
                          })
                        ),
                        item.value[2]
                      ),
              },
            };
          }),
          shading: 'lambert',
          label: {
            fontSize: 16,
            borderWidth: 1,
            show: props.matrixData.length > 200 ? false : true, //开启显示
            formatter: (params) => {
              //柱状图最大值上方显示数值
              let max = Math.max.apply(
                Math,
                props.matrixData.map((item) => {
                  if (!item.value) {
                    item['value'] = item;
                  }
                  return item.value[2];
                })
              );
              return max == params.value[2] && max != 0 ? max : ' ';
            },
          },
          emphasis: {
            label: {
              fontSize: 20,
              color: '#900',
            },
            itemStyle: {
              color: '#900',
            },
          },
        },
      ],
    });
    //点击事件
    myChart?.off('click');
    myChart?.on('click', (params) => {
      showModal.value = true;
      queryRequest.across = params.value[0];
      queryRequest.vertical = params.value[1];
      matrixCellSecNum.value = params.value[2];
      nextTick(() => {
        query_MatrixCellAnalysis();
      });
      //刷新
      // myChart?.resize();
    });
  };

  const query_MatrixCellAnalysis = async () => {
    showLoading.value = true;
    let { code, data, msg } = queryRequest.levelConditionList
      ? await queryLevelStrategySecMatrixCell(queryRequest)
      : await queryMatrixCellAnalysis(queryRequest);
    showLoading.value = false;

    if (code === 200) {
      let { financialScore, income, info, overallScore, ttmAnalysis, level } = data;
      infoList.value = info;
      financialScoreConst.value = financialScore;
      overallScoreConst.value = overallScore;
      incomeRangeConst.value = income;
      ttmXAxis.value = ttmAnalysis.map(({ typeName }) => typeName);
      ttmYAxis.value = ttmAnalysis.map(({ count }) => count);
      levelXAxis.value = level.map(({ typeName }) => typeName);
      levelYAxis.value = level.map(({ count }) => count);
    } else {
      message.error(msg);
    }
  };
  onMounted(() => {
    echartsDraw();
  });
  watch(
    () => [props.parameters, props.xData, props.yData],
    () => {
      echartsDraw();
    },
    { deep: true }
  );
</script>

<style scoped></style>
