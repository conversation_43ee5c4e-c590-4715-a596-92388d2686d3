<template>
  <!--    矩阵通用弹框-->
  <n-spin :show="showMatrixLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { Ref, ref, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  interface Props {
    xdata?: Array<any>;
    ydata?: Array<any>;
    matrixData?: Array<any>;
    height?: string;
    width?: string;
    showMatrixLoading?: boolean;
  }
  const props = defineProps<Props>();

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const getBackgroundColor = (max, number) => {
    let maxNumber = max; // 最大数字
    let minColor = <any>[]; // 最浅颜色的RGB值
    let maxColor = <any>[]; // 最深颜色的RGB值
    if (number  <=  50) {
      maxNumber = 50;
      minColor = [30, 159, 255];
      maxColor = [161, 204, 236];
    } else if (number > 50 && number <= 100) {
      maxNumber = 100;
      minColor = [255, 176, 11];
      maxColor = [238, 221, 185];
    } else {
      maxNumber = max;
      minColor = [255, 0, 0];
      maxColor = [242, 182, 159];
    }
    // 计算颜色的RGB值
    const color = minColor.map((c, i) => {
      const diff = (maxColor[i] - minColor[i]) / (maxNumber - 0.5);
      return Math.round(maxColor[i] - diff * (number - 0.5));
    });

    // 转换为16进制颜色值
    return '#' + color.map((c) => c.toString(16).padStart(2, '0')).join('');
  };
  //渲染图
  const echartsDraw = () => {
    setOptions({
      title: {
        show: false, //false
        text: '',
      },
      tooltip: {
        backgroundColor: '#ffffff',
        confine: true,
        appendToBody: true,
        formatter: (params) => {
          return (
            '<div style="font-size:12px;background-color: #fff">' +
            params.value[1] +
            '<br>' +
            params.value[0].replace(/(^.*?,.*?),/, '$1<br/>') +
            '<br/>数量: ' +
            params.value[2] +
            '</div>'
          );
        },
      },

      xAxis3D: {
        type: 'category',

        axisLabel: {
          show: false,
          textStyle: {
            color: '#000',
            fontSize: '12',
          },
          formatter: function (params) {
            return params.length > 30 ? params.replace(/(^.*?,.*?),/, '$1\n') : params;
          },
        },
        data: props.xdata,
        splitArea: {
          show: true,
          interval: 7,
          areaStyle: {
            color: ['#f6f5f1', '#b6b5b2'],
          },
        },
      },
      yAxis3D: {
        type: 'category',

        data: props.ydata,
      },
      zAxis3D: {
        type: 'value',
      },
      grid3D: {
        show: true,
        boxWidth: 200,
        boxDepth: 80,
        backgroundColor: 'transparent',
        light: {
          main: {
            opacity: 0.3,
            intensity: 0.7,
            // shadow: true
          },
          ambient: {
            intensity: 0.3,
          },
        },
        viewControl: {
          alpha: 50, //视角绕 x 轴，即上下旋转的角度(与beta一起控制视野成像效果)
          beta: -2, //视角绕 y 轴，即左右旋转的角度。
          animation: true,
          projection: 'perspective', // 先设置为这个perspective
          distance: 170, //默认缩放比例
        },

        shadowOffsetX: 140,
      },
      series: [
        {
          name: '详情',
          type: 'bar3D',
          data: props.matrixData.map((item) => {
            if (!item.value) {
              item['value'] = item;
            }
            return {
              value: [item.value[1], item.value[0], item.value[2]],
              itemStyle: {
                color:
                  item.flag == 1
                    ? '#fff200'
                    : item.value[2] == 0
                    ? '#fff'
                    : getBackgroundColor(
                        Math.max.apply(
                          Math,
                          props.matrixData.map((item) => {
                            if (!item.value) {
                              item['value'] = item;
                            }
                            return item.value[2];
                          })
                        ),
                        item.value[2]
                      ),
              },
            };
          }),
          shading: 'lambert',
          label: {
            fontSize: 16,
            borderWidth: 1,
            show: props.matrixData.length>200?false: true, //开启显示
            formatter: (params) => {
              //柱状图最大值上方显示数值
              let max = Math.max.apply(
                Math,
                props.matrixData.map((item) => {
                  if (!item.value) {
                    item['value'] = item;
                  }
                  return item.value[2];
                })
              );
              return max == params.value[2] && max != 0 ? max : ' ';
            },
          },
          emphasis: {
            label: {
              fontSize: 20,
              color: '#900',
            },
            itemStyle: {
              color: '#900',
            },
          },
        },
      ],
    });
  };

  watch(
    () => [props.ydata],
    () => {
      echartsDraw();
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped></style>
