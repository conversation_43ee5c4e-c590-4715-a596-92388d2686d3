<!--  混淆矩阵图
matrixData:[
        [50, 10],  // [真阳性, 假阳性]
        [5, 35],   // [真阴性, 假阴性]
      ]

-->
<template>
  <n-spin :show="showLoading">
    <div ref="chartRef" :style="{ height, width }"></div>
  </n-spin>
</template>

<script setup lang="ts">
  import { nextTick, Ref, ref, watch } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';

  interface Props {
    xdata?: Array<any>;
    ydata?: Array<any>;
    matrixData?: Array<any>;
    maxNumber?: number;
    height?: string;
    width?: string;
    showLoading?: boolean;
  }
  const props = defineProps<Props>();
  const emit = defineEmits(['handleClick']);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  //渲染图
  const echartsDraw = () => {
    const myChart = getInstance();

    const heatmapData = [];
    if (props.matrixData) {
      for (let i = 0; i < props.matrixData.length; i++) {
        for (let j = 0; j < props.matrixData[i].length; j++) {
          heatmapData.push([j, i, props.matrixData[i][j]]);
        }
      }
    }

    setOptions({
      tooltip: {
        formatter: function (params) {
          const value = params.data[2]; // 单元格的值
          return `${value}`;
        },
      },
      grid: {
        top: '2%',
        left: '5%',
        right: '4%',
        bottom: '13%',
      },

      xAxis: {
        axisLabel: {
          show: false,
        },
        type: 'category',
        data: ['真阳性', '假阳性'],
      },
      yAxis: {
        axisLabel: {
          show: false,
        },
        type: 'category',
        data: ['真阴性', '假阴性'],
      },
      visualMap: {
        min: 0,
        max: props.maxNumber, // 根据数据范围设置
        show: false,
        calculable: false,
        orient: 'horizontal',
        left: 'center',
        bottom: '20%',
        inRange: {
          color: ['#ffffff', '#e30f0f'],
        },
      },
      series: [
        {
          name: '混淆矩阵',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            formatter: (params) => {
              console.log(params);
              const y = ['真阳性', '假阳性', '真阴性', '假阴性'];
              return `${y[params.dataIndex]}\n\n${params.value[2]}`; // 显示数值
            },
            textStyle: {
              fontSize: '20',
            },
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    });
    //点击事件
    myChart?.off('click');
    myChart?.on('click', (params) => {
      let val = params.value;
      let type = '';
      if (val[0] == 0 && val[1] == 1) {
        type = '真阴性';
      } else if (val[0] == 1 && val[1] == 0) {
        type = '假阳性';
      } else if (val[0] == 0 && val[1] == 0) {
        type = '真阳性';
      } else if (val[0] == 1 && val[1] == 1) {
        type = '假阴性';
      }
      emit('handleClick', type);
    });
  };

  watch(
    () => [props.showLoading],
    () => {
      echartsDraw();
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped></style>
