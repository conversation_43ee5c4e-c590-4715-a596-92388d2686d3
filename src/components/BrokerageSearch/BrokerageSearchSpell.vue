<template>
  <!--券商搜索通用弹框,返回选择的券商拼写-->
  <n-select
    v-model:value="selectedValues"
    :options="options"
    :render-label="renderLabel"
    :render-tag="renderTag"
    :style="{ width: width || '234px' }"
    filterable
    label-field="cnName"
    placeholder="请选择券商"
    value-field="enName"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { getPeerAgencyList } from '@/api/peerSec/peerApi';
  import { NIcon, SelectOption, SelectGroupOption, NText, useMessage } from 'naive-ui';
  import { setPeerLogo } from '@/utils/common/resource';

  interface Props {
    width?: string;
    noDefault?: boolean;
  }

  const props = defineProps<Props>();

  const selectedValues = ref<any>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);
  const message = useMessage();
  const get_PeerAgencyList = async () => {
    const { data, code, msg } = await getPeerAgencyList({});
    if (code === 200) {
      options.value = data;
      if (!props.noDefault) {
        selectedValues.value = data[0].enName;
        emit('getValue', selectedValues.value, data[0].cnName);
      }
    } else {
      message.error(msg);
    }
  };
  get_PeerAgencyList();

  const emit = defineEmits(['getValue']);

  // 自定义选项渲染函数 - 显示图标和券商名称
  const renderLabel = (option: any) => {
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        },
      },
      [
        h('img', {
          src: setPeerLogo(option.cnName),
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            objectFit: 'cover',
          },
          onError: (e: Event) => {
            // 如果图标加载失败，隐藏图片
            (e.target as HTMLImageElement).style.display = 'none';
          },
        }),
        h(
          'span',
          {
            style: {
              fontSize: '14px',
              fontWeight: '500',
            },
          },
          option.cnName
        ),
      ]
    );
  };

  // 自定义选中标签渲染函数 - 显示图标和券商名称
  const renderTag = ({ option }: { option: any }) => {
    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
        },
      },
      [
        h('img', {
          src: setPeerLogo(option.cnName),
          style: {
            width: '18px',
            height: '18px',
            borderRadius: '50%',
            objectFit: 'cover',
          },
          onError: (e: Event) => {
            (e.target as HTMLImageElement).style.display = 'none';
          },
        }),
        h(
          'span',
          {
            style: {
              fontSize: '13px',
              fontWeight: '500',
            },
          },
          option.cnName
        ),
      ]
    );
  };

  // 点击事件触发emit，
  const handleUpdateValue = () => {
    let optionsList = options.value.filter((item) => item.enName === selectedValues.value);
    let cnName = optionsList.length > 0 ? optionsList[0].cnName : '';

    emit('getValue', selectedValues.value, cnName);
  };
</script>

<style scoped></style>
