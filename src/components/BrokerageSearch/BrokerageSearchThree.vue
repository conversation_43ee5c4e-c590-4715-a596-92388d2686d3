<template>
  <!--券商搜索通用弹框 ，携带v的券商，默认选中第一个-->
  <n-select
    v-model:value="selectedValues"
    :options="options"
    :render-label="renderLabel"
    :render-tag="renderTag"
    :style="{ width: width || '234px' }"
    filterable
    label-field="cnName"
    placeholder="请选择券商"
    value-field="enName"
    @update:value="handleUpdateValue"
  />
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild } from 'vue';
  import { getPeerAgencyList } from '@/api/peerSec/peerApi';
  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  import { setPeerLogo } from '@/utils/common/resource';
  interface Props {
    width?: string;
  }
  defineProps<Props>();

  const selectedValues = ref<string | number | null>();
  const loading = ref<boolean>(false);
  const options = ref<Array<SelectOption | SelectGroupOption>>([]);

  const get_PeerAgencyList = async () => {
    const { data, code } = await getPeerAgencyList({ ifLevelMark: true });
    if (code === 200) {
      data.forEach((item) => {
        item.enName = item.cnName + ',' + item.enName;
      });
      options.value = data;
      selectedValues.value = data[0].enName;
      let arr = selectedValues.value?.split(',') as any;
      emit('getValue', arr[0].replace('√', ''), arr[1]);
    }
  };
  get_PeerAgencyList();

  const emit = defineEmits(['getValue']);

  // 自定义选项渲染函数 - 显示图标和券商名称（处理√标记）
  const renderLabel = (option: any) => {
    // 获取纯净的券商名称用于logo（去除√标记）
    const cleanName = option.cnName.replace('√', '').trim();

    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        },
      },
      [
        h('img', {
          src: setPeerLogo(cleanName),
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            objectFit: 'cover',
          },
          onError: (e: Event) => {
            // 如果图标加载失败，隐藏图片
            (e.target as HTMLImageElement).style.display = 'none';
          },
        }),
        h(
          'span',
          {
            style: {
              fontSize: '16px',
              fontWeight: '500',
            },
          },
          option.cnName
        ), // 显示原始名称（包含√标记）
      ]
    );
  };

  // 自定义选中标签渲染函数 - 显示图标和券商名称
  const renderTag = ({ option }: { option: any }) => {
    // 获取纯净的券商名称用于logo（去除√标记）
    const cleanName = option.cnName.replace('√', '').trim();

    return h(
      'div',
      {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
        },
      },
      [
        h('img', {
          src: setPeerLogo(cleanName),
          style: {
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            objectFit: 'cover',
          },
          onError: (e: Event) => {
            (e.target as HTMLImageElement).style.display = 'none';
          },
        }),
        h(
          'span',
          {
            style: {
              fontSize: '16px',
              fontWeight: '500',
            },
          },
          cleanName
        ), // 选中标签显示纯净名称
      ]
    );
  };

  // 点击事件触发emit，
  const handleUpdateValue = () => {
    let arr = selectedValues.value?.split(',') as any;
    emit('getValue', arr[0].replace('√', '').trim(), arr[1]);
  };
</script>

<style scoped></style>
