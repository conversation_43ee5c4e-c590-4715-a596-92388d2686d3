<template>
  <!--  通用营业部搜索封装-->
  <div class="w-full">
    <n-select
      :style="{ width: width || '100%' }"
      v-model:value="selectedValues"
      filterable
      :placeholder="placeholder || '请输入营业部代码或名称'"
      :options="options"
      label-field="orgname"
      value-field="orgid"
      :render-label="renderLabel"
      :loading="loading"
      clearable
      remote
      :clear-filter-after-select="false"
      @search="handleSearch"
      @update:value="handleUpdateValue"
    />
    <!--  <n-button @click="transValue"></n-button>-->
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, VNodeChild, watch } from 'vue';
  import { queryOrgInfo } from '@/views/hualong/api/backend.ts';
  import { NIcon, SelectOption, SelectGroupOption, NText } from 'naive-ui';
  interface Props {
    placeholder?: string;
    defaultValue?: string;
    width?: string;
  }
  const props = defineProps<Props>();

  const selectedValues = ref<string>();
  const loading = ref<boolean>(false);
  const options = ref<any>([]);
  const renderLabel = (option) => {
    return [
      option.orgname as string,
      h(
        NText,
        {
          depth: '3',
          style: {
            marginLeft: '8px',
          },
        },
        {
          default: () => option.orgid,
        }
      ),
    ];
  };
  //搜索
  const handleSearch = (val) => {
    query_OrgInfo({ orgid: val });
  };

  const query_OrgInfo = async (params) => {
    loading.value = true;
    let { data, code } = await queryOrgInfo(params);
    loading.value = false;
    if (code == 200) {
      options.value = data;
    }
  };
  watch(
    () => props.defaultValue,
    (newVal) => {
      //console.log(newVal);
      if (newVal && newVal[0]) {
        options.value = [{ orgname: newVal[0], orgid: newVal[1] }];
        selectedValues.value = newVal[1];
      }
    },
    { deep: true, immediate: true }
  );
  const emit = defineEmits(['getValue']);
  // 点击事件触发emit，去调用我们注册的自定义事件getValue,并传递value参数至父组件
  const handleUpdateValue = () => {
    let name = '';
    options.value.forEach((item) => {
      if (item.orgid == selectedValues.value) {
        name = item.orgname;
        return;
      }
    });

    //console.log(selectedValues.value);
    emit('getValue', selectedValues.value, name);
  };
</script>
