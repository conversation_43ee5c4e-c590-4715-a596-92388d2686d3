const personList = [
  { name: '<PERSON>', age: 25 },
  { name: '<PERSON>', age: 30 },
  { name: '<PERSON>', age: 40 },
];

// 测试map函数
const mappedResult = personList.map((Person) => {
  return Person.name + 'is' + Person.age + 'years old.';
});

//console.log(mappedResult); // ["Johnis25years old.", "Janeis30years old.", "Bobis40years old."]

// 测试filter函数
const filteredResult = personList.filter((Person) => {
  return Person.age > 30;
});

//console.log(filteredResult); // [{ name: '<PERSON>', age: 30 }, { name: '<PERSON>', age: 40 }]

// 测试reduce函数
const totalAges = personList.reduce((total, Person, index, array) => {
  return total + Person.age;
}, 0);
//console.log(totalAges); // 95

const totalAges1 = personList.reduce((total, Person) => {
  return total + Person.age;
}, 0);
//console.log(totalAges1); // 95

// 未设置初始值，则默认为数组的第一个元素
const totalAges2 = personList.reduce((total, Person) => {
  return total + Person.age;
});
//console.log(totalAges2); //[object Object]3040

const process = (personList) => {
  const filteredResult = personList
    .filter((item) => item.age)
    .map((person) => ({ ...person, createTime: new Date() }));
  const totalAges = filteredResult.reduce((total, person) => {
    return total + person.age;
  }, 0);
  //console.log(filteredResult, totalAges);
};

process(personList);
