{
  "compilerOptions": {
    // 指定要使用的 ECMAScript 目标版本。'esnext' 表示使用最新的 ECMAScript 特性。
    "target": "esnext",
    // 指定模块的生成格式。'esnext' 表示使用 ECMAScript 模块。
    "module": "esnext",
    // 使用 Node.js 模块解析策略，适用于基于 Node.js 的项目。
    "moduleResolution": "node",
    // 启用所有严格类型检查选项，以增强代码的类型安全性。
    "strict": true,
    // 强制文件名的大小写一致性，防止在大小写敏感的文件系统中出现问题。
    "forceConsistentCasingInFileNames": true,
    // 允许默认导入没有默认导出的模块，主要用于兼容 CommonJS 模块。
    "allowSyntheticDefaultImports": true,
    // 禁用函数参数的严格类型检查，减少类型检查的严格程度。
    "strictFunctionTypes": false,
    // 保留 JSX 的原始格式，适用于 React 项目。
    "jsx": "preserve",
    // 指定项目的根目录，用于解析非相对模块。
    "baseUrl": ".",
    // 允许编译 JavaScript 文件，便于逐步迁移到 TypeScript。
    "allowJs": true,
    // 生成 source map 文件，以便于调试。
    "sourceMap": false,
    // 启用对 ES 模块互操作性的支持，便于使用 CommonJS 模块。
    "esModuleInterop": true,
    // 允许从 JSON 文件中导入模块。
    "resolveJsonModule": true,
    // 报告未使用的局部变量，帮助保持代码的整洁。
    "noUnusedLocals": false,
    // 报告未使用的函数参数，帮助保持代码的整洁。
    "noUnusedParameters": false,
    // 启用对实验性装饰器的支持，适用于使用装饰器的框架（如 Angular）。
    "experimentalDecorators": true,
    // 指定要包含的库文件，'dom' 用于浏览器项目，'esnext' 用于使用最新 ECMAScript 特性的项目。
    "lib": [
      "dom",
      "esnext"
    ],
    // 指定类型声明文件的目录，用于查找第三方库的类型定义。
    "typeRoots": [
      "./node_modules/@types/",
      "./types"
    ],
    // 允许隐式的 any 类型，减少类型检查的严格程度。
    "noImplicitAny": false,
    // 跳过库文件的类型检查，加快编译速度。
    "skipLibCheck": true,
    // 配置路径别名，便于模块导入。
    "paths": {
      "@/*": [
        "src/*"
      ],
      "/#/*": [
        "types/*"
      ]
    },
    "emitDecoratorMetadata": true,
  },
  // 指定要包含在编译过程中的文件和文件夹。
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "mock/**/*.ts",
    "components.d.ts",
    "vite.config.ts"
  ],
  // 指定要排除在编译过程中的文件和文件夹。
  "exclude": [
    "node_modules",
    "dist",
    "**/*.js"
  ]
}

