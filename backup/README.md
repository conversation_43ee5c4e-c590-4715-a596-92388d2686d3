# 📁 备份文件说明

## 📋 备份文件列表

| 文件名 | 备份时间 | 大小 | 说明 |
|--------|----------|------|------|
| `package.json.backup` | 2025/6/20 21:36:16 | 4,770 字节 | package.json 历史备份 |
| `tsconfig.json.backup` | 2025/6/20 21:36:17 | 2,993 字节 | TypeScript 配置备份 |
| `yarn.lock.backup` | 2025/7/14 8:54:53 | 328,488 字节 | Yarn 锁定文件备份 |

## 🗂️ 备份目的

这些文件是在项目维护过程中创建的备份文件，用于：

1. **配置文件安全** - 保留重要配置文件的历史版本
2. **依赖管理** - 保留不同包管理器的锁定文件
3. **回滚支持** - 在需要时可以恢复到之前的配置

## 🔄 使用说明

### 恢复备份文件
如果需要恢复某个备份文件，可以执行：

```bash
# 恢复 package.json
cp backup/package.json.backup package.json

# 恢复 tsconfig.json  
cp backup/tsconfig.json.backup tsconfig.json

# 恢复 yarn.lock
cp backup/yarn.lock.backup yarn.lock
```

### 清理备份文件
如果确认不再需要这些备份文件，可以删除整个备份目录：

```bash
# 删除整个备份目录
rm -rf backup/
```

## ⚠️ 注意事项

- 这些备份文件可能包含过时的配置信息
- 恢复前请确认当前项目状态
- 建议在恢复备份前先备份当前文件

---

**创建时间**: 2025年7月15日  
**创建原因**: 项目清理和包管理器统一
