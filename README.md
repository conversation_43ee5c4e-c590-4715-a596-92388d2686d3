# 塔金风险管理平台 (Tarkin Risk Management Platform)

## 简  介

**塔金风险管理平台** 是一个专业的证券公司融资融券风险管理系统，基于 [Vue3.0](https://github.com/vuejs/vue-next)、[Vite](https://github.com/vitejs/vite)、[Naive UI](https://www.naiveui.com/)、[TypeScript](https://www.typescriptlang.org/) 构建的智能风险控制平台。

本系统专为证券公司融资融券部门设计，提供全面的风险评估、预警、控制和管理功能，包括评级策略管理、同业数据分析、风险预警、证券调整、压力测试等核心业务模块，帮助金融机构实现智能化、精细化的风险管理。

## 🎯 业务特性
- **智能风险评估** - 多维度风险指标分析和评级策略管理
- **实时风险预警** - 动态监控和预警机制，及时发现潜在风险
- **同业数据分析** - 全面的同业对比和市场分析功能
- **压力测试** - 多场景压力测试和风险承受能力评估
- **合规管理** - 完善的合规检查和监管报告功能

## 🚀 技术特性
- **现代化技术栈** - 基于 `Vue3`、`TypeScript`、`Pinia`、`Vite` 等前沿技术
- **高性能架构** - 响应式设计，支持大数据量处理和实时更新
- **安全可靠** - 多层级权限控制和数据安全保障
- **易于扩展** - 模块化设计，支持业务功能快速扩展
- **用户友好** - 专为金融业务人员设计的直观操作界面


## 预览
- [naive-ui-admin](https://jekip.github.io)

账号：admin，密码：123456（随意）

## 提示

如果开源版本的功能和组件，并不能满足您的需求，不妨看看，我们全新 `NaiveAdmin` 他或许能让您眼前一亮O(∩_∩)O哈哈~

[NaiveAdmin 官网](https://www.naiveadmin.com)

[NaiveAdmin 变更日志](https://www.yuque.com/u5825/zaqu0e)

[为什么选我们？](https://www.naiveadmin.com/choose/we)
### Plus

基于 `NaiveUi` 全新设计版本，新增众多特性，强烈推荐

[NaiveAdmin Plus 预览](https://plus.naiveadmin.com)

### Arco vue

智能设计体系，连接轻盈体验

[NaiveAdmin Arco 预览](https://arco.naiveadmin.com)

### Element Plus

面向设计师和开发者的组件库

[Element Plus Admin 预览](https://element.naiveadmin.com)

以上版本同时具备 `NaiveAdmin` 功能/组件/页面，一如既往、开箱即用，欢迎前往查看。

### Antd vue

新产品，如果您选的技术栈是 `Antd` 的话，不妨看看

[NaiveAdmin Antd 预览](https://antd.naiveadmin.com)

## 文档

[文档地址](https://jekip.github.io/docs)

## 准备

- [node](http://nodejs.org/) 和 [git](https://git-scm.com/) -项目开发环境
- [Vite](https://vitejs.dev/) - 熟悉 vite 特性
- [Vue3](https://v3.vuejs.org/) - 熟悉 Vue 基础语法
- [TypeScript](https://www.typescriptlang.org/) - 熟悉`TypeScript`基本语法
- [Es6+](http://es6.ruanyifeng.com/) - 熟悉 es6 基本语法
- [Vue-Router-Next](https://next.router.vuejs.org/) - 熟悉 vue-router 基本使用
- [Naive-ui-admin](https://www.naiveui.com/) - ui 基本使用
- [Mock.js](https://github.com/nuysoft/Mock) - mockjs 基本语法

## 使用

- 获取项目代码

```bash
git clone https://github.com/jekip/naive-ui-admin.git
```

- 安装依赖

```bash
cd naive-ui-admin

yarn install

```

- 运行

```bash
yarn dev
```

- 打包

```bash
yarn build
```

## 更新日志

[CHANGELOG](./CHANGELOG.md)


## 如何贡献

非常欢迎你的加入！[提一个 Issue](https://github.com/jekip/naive-ui-admin/issues) 或者提交一个 Pull Request。

**Pull Request:**

1. Fork 代码!
2. 创建自己的分支: `git checkout -b feat/xxxx`
3. 提交你的修改: `git commit -am 'feat(function): add xxxxx'`
4. 推送您的分支: `git push origin feat/xxxx`
5. 提交`pull request`

## Git 贡献提交规范

- 参考 [vue](https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md) 规范 ([Angular](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular))

  - `feat` 增加新功能
  - `fix` 修复问题/BUG
  - `style` 代码风格相关无影响运行结果的
  - `perf` 优化/性能提升
  - `refactor` 重构
  - `revert` 撤销修改
  - `test` 测试相关
  - `docs` 文档/注释
  - `chore` 依赖更新/脚手架配置修改等
  - `workflow` 工作流改进
  - `ci` 持续集成
  - `types` 类型定义文件更改
  - `wip` 开发中

## 浏览器支持

本地开发推荐使用`Chrome 80+` 浏览器

支持现代浏览器, 不支持 IE

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| :-: | :-: | :-: | :-: | :-: |
| not support | last 2 versions | last 2 versions | last 2 versions | last 2 versions |

## 维护者
[@Ah jung](https://github.com/jekip)

## 交流

`Naive Ui Admin` 使用或者其他问题，都可以在群内讨论或提问。

![160335146-c28dd205-4600-4d62-b2c6-6456034ab7b1](https://user-images.githubusercontent.com/19426584/217689718-407e6cb9-dd3b-4a11-a025-3c58834b52ff.jpg)

## 赞助
#### 如果你觉得这个项目帮助到了你，你可以帮作者买一杯果汁表示鼓励 🍹。

![donate](https://jekip.github.io/docs/images/sponsor.png)

[Paypal Me](https://www.paypal.com/paypalme/majunping)
