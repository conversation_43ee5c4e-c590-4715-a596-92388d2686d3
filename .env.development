# Rebuild Augment Index
# 只在开发模式中被载入
VITE_PORT = 8001

# 网站根目录
VITE_PUBLIC_PATH = /

# 是否开启mock
VITE_USE_MOCK = false

# 网站前缀
VITE_BASE_URL = /

# 是否删除console
VITE_DROP_CONSOLE = true

# 是否启用Token超时锁定功能
VITE_GLOB_ENABLE_TOKEN_TIMEOUT_CHECK = true

# 跨域代理，可以配置多个，请注意不要换行
#线上
#VITE_PROXY=[["/work","http://www.tarkindata.com:8887"],["/backend","http://lgblogs.nat300.top/backend"]]
#VITE_PROXY=[["/work","http://192.168.155.232:8888"]]



#本地
VITE_PROXY=[["/work","http://192.168.3.3:8888"]]
#VITE_PROXY=[["/work","http://192.168.221.232:8888"]]
#VITE_PROXY=[["/work","http://192.168.0.108:8888"]]



#内网穿透
#VITE_PROXY=[["/work","https://yo10al0838730.vicp.fun"]]
#VITE_PROXY=[["/work","https://2e27-240a-42b2-0-5bd1-1d05-d2a4-53ae-22bf.ngrok-free.app"]]

# API 接口地址
VITE_GLOB_API_URL =

# 图片上传地址
VITE_GLOB_UPLOAD_URL=

# 图片前缀地址
VITE_GLOB_IMG_URL=

# 接口前缀
VITE_GLOB_API_URL_PREFIX =
#VITE_GLOB_API_URL_PREFIX = /work
# 华龙李港接口前缀
VITE_GLOB_PREFIX_LG=/backend
VITE_GLOB_PREFIX_EXPORT_LG=/backend

#线上
#VITE_GLOB_PREFIX=/work/work/stocks-service
#VITE_GLOB_PREFIX_BASIC=/work/work/basic-service
#VITE_GLOB_PREFIX_EXPORT=/work/work/stocks-service
#VITE_GLOB_PREFIX_HUALONG=false




##测试
#VITE_GLOB_PREFIX=/backend
#VITE_GLOB_PREFIX_BASIC=/backend
#VITE_GLOB_PREFIX_EXPORT=/backend
#VITE_GLOB_PREFIX_BASIC=/backend
##VITE_GLOB_PREFIX_HUALONG=
#VITE_GLOB_PREFIX_HUALONG=



##本地
VITE_GLOB_PREFIX=/work/stocks-service
VITE_GLOB_PREFIX_BASIC=/work/basic-service
VITE_GLOB_PREFIX_EXPORT=/work/stocks-service
VITE_GLOB_PREFIX_HUALONG=false





##设置logo
## 为空默认塔金，   xingye :兴业证券
VITE_GLOB_SET_LOGO_NAME=
#VITE_GLOB_SET_LOGO_NAME=xingye
