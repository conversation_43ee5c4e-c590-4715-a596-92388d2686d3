/*!
 * 滚动条
 **/

.kf-editor-edit-scrollbar {
    width: 100%;
    height: 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 994;
    border-top: 1px solid #ddd;
}

.kf-editor-ui-left-button {
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: white;
}

.kf-editor-ui-right-button {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background: white;
}

.kf-editor-ui-track {
    position: absolute;
    top: 0;
    left: 5px;
    width: 0;
    height: 100%;
    background-image: url(../images/scrollbar/edit/bar-left.png), url(../images/scrollbar/edit/bar-right.png), url(../images/scrollbar/edit/bar-bg.png);
    background-repeat: no-repeat, no-repeat, repeat-x;
    background-position: 0 0, right 0, 0 0;
}

.kf-editor-ui-thumb {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
}

.kf-editor-ui-thumb-left {
    width: 5px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: url(../images/scrollbar/edit/thumb-left.png) no-repeat 0 0;
    z-index: 1;
}

.kf-editor-ui-thumb-right {
    width: 5px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: url(../images/scrollbar/edit/thumb-right.png) no-repeat 0 0;
}

.kf-editor-ui-thumb-body {
    position: absolute;
    top: 0;
    left: 5px;
    width: 0;
    height: 100%;
    background: url(../images/scrollbar/edit/thumb-bg.png) repeat-x 0 0;
}
