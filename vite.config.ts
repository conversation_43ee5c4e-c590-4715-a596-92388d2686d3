import type { ConfigEnv, UserConfig } from 'vite';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import { createProxy } from './build/vite/proxy';
import pkg from './package.json';
import { format } from 'date-fns';
import Components from 'unplugin-vue-components/vite';
import AutoImport from 'unplugin-auto-import/vite';

const { dependencies, devDependencies, name, version } = pkg;

const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
};

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

export default ({ command, mode }: ConfigEnv): UserConfig => {
  // 获取项目根目录
  const root = process.cwd();
  // 加载环境变量
  const env = loadEnv(mode, root);
  // 处理环境变量
  const viteEnv = wrapperEnv(env);
  const { VITE_PUBLIC_PATH, VITE_PORT, VITE_GLOB_PROD_MOCK, VITE_PROXY } = viteEnv;
  const prodMock = VITE_GLOB_PROD_MOCK;
  const isBuild = command === 'build';
  return {
    base: VITE_PUBLIC_PATH,
    esbuild: {},
    resolve: {
      alias: [
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: '@',
          replacement: pathResolve('src') + '/',
        },
      ],
      dedupe: ['vue'],
    },
    plugins: [
      // 加载项目自定义的 Vite 插件集合（包括 Vue、Mock、压缩等插件）
      ...createVitePlugins(viteEnv, isBuild, prodMock),

      // unplugin-auto-import 插件：自动导入 API 和常量
      AutoImport({
        imports: ['vue', 'vue-router'],
        // 自动扫描并导入指定目录下的所有导出
        dirs: ['src/constants/**'],
        dts: true, // 生成类型声明文件 auto-imports.d.ts
        eslintrc: {
          enabled: true, // 生成ESLint配置
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
        // 排除有问题的导入模式
        ignore: ['**/index.ts'],
      }),

      // unplugin-vue-components 插件：自动导入和注册 Vue 组件
      Components({
        // 🎯 默认只扫描这个目录！
        dirs: ['src/components'],
        //   // 🎯 默认只扫描这个目录！
        extensions: ['vue'],
        // 递归扫描子目录
        deep: true,
        // 自动生成 components.d.ts 类型声明文件，提供 TypeScript 支持
        dts: true,

        // 排除不需要自动注册的组件文件（通常是废弃或有问题的组件）
        exclude: [/src\/components\/Matrix\/deprecated\/echarts3DBarBackground2\.vue$/],
      }),
    ],
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    server: {
      host: true,
      port: VITE_PORT,
      proxy: createProxy(VITE_PROXY),
    },
    optimizeDeps: {
      include: [],
      exclude: ['vue-demi'],
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler', // 使用现代编译器 API
          silenceDeprecations: ['legacy-js-api'], // 暂时静默弃用警告
        },
        sass: {
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
    // 🎯 这里是构建（打包）相关的所有配置
    build: {
      target: 'es2015', // 编译目标：ES2015 语法，兼容较老浏览器
      cssTarget: 'chrome80', // CSS 目标：Chrome 80+ 浏览器特性
      outDir: OUTPUT_DIR, // 输出目录：构建文件存放位置 (dist)
      reportCompressedSize: false, // 禁用压缩大小报告：提升构建速度
      chunkSizeWarningLimit: 2000, // 代码块大小警告阈值：2MB
    },
  };
};
