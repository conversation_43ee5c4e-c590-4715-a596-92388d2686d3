/**
 * Prettier 配置 - 塔金风险管理平台
 * Prettier 配置文件，用于定义代码格式化规则
 * @type {import('prettier').Config}
 */

// 当前配置的优先级 > editorconfig > idea内置设置
// 在 settings > languages & frameworks > JavaScript > Prettier 中配置

module.exports = {
  /**
   * 指定 Prettier 打印的行宽，超出该宽度会自动换行。
   * 例如，设置为100意味着每行最多100个字符，超过则换行。
   * 适用于需要较长代码行但保持可读性的项目。
   */
  printWidth: 100,

  /**
   * 指定每个缩进级别的空格数。
   * 例如，设置为2表示每个缩进层级使用2个空格。
   * 常用于团队代码风格一致，提升代码可读性。
   */
  tabWidth: 2,

  /**
   * 使用空格代替制表符进行缩进。
   * - `false`: 使用空格（推荐，避免不同编辑器对Tab宽度解释不一致的问题）。
   * - `true`: 使用制表符（Tab）。
   * 例如，设置为false表示使用空格进行缩进。
   */
  useTabs: false,

  /**
   * 在语句末尾打印分号。
   * - `true`: 在每个语句末尾添加分号。
   * - `false`: 不添加分号。
   * 例如，设置为true可以避免自动分号插入带来的潜在问题。
   */
  semi: true,

  /**
   * 在 Vue 文件中的 `<script>` 和 `<style>` 标签内缩进代码。
   * - `true`: 缩进 `<script>` 和 `<style>` 内的代码，保持一致性。
   * - `false`: 不缩进。
   * 例如，设置为true可以确保 Vue 单文件组件内部代码整洁。
   */
  vueIndentScriptAndStyle: true,

  /**
   * 使用单引号代替双引号。
   * - `true`: 使用单引号（如 'Hello World'）。
   * - `false`: 使用双引号（如 "Hello World"）。
   * 例如，设置为true可以减少键盘切换次数，符合多数 JavaScript 社区的风格。
   */
  singleQuote: true,

  /**
   * 根据需要为对象属性添加引号。
   * - `'as-needed'`: 仅在必要时添加引号（例如属性名包含特殊字符时）。
   * - `'consistent'`: 要么所有属性名都添加引号，要么都不添加。
   * - `'preserve'`: 保留原有的引号设置。
   * 例如，设置为'as-needed'，`{ foo: "bar", "baz-qux": "quux" }`，只有"baz-qux"会被引号包裹。
   */
  quoteProps: 'as-needed',

  /**
   * 在对象字面量的大括号内打印空格。
   * - `true`: 在大括号内添加空格（如 `{ key: value }`）。
   * - `false`: 不添加空格（如 `{key: value}`）。
   * 例如，设置为true可以提升代码的可读性。
   */
  bracketSpacing: true,

  /**
   * 在多行对象或数组的最后一个元素后添加逗号。
   * - `'none'`: 不添加逗号。
   * - `'es5'`: 在ES5有效的地方添加逗号，如对象、数组、函数参数等。
   * - `'all'`: 在所有可能的地方添加逗号，包括函数参数。
   * 例如，设置为'es5'方便后续添加新元素，减少版本控制中的差异。
   */
  trailingComma: 'es5',

  /**
   * 在多行 JSX 元素的 `>` 放在最后一行的末尾，而不是单独一行。
   * - `true`: 将 `>` 放在最后一行的末尾。
   * - `false`: 将 `>` 放在新的一行。
   * 例如，设置为false有助于保持 JSX 结构的清晰和一致性。
   */
  jsxBracketSameLine: false,

  /**
   * 在 JSX 中使用双引号而不是单引号。
   * - `true`: 使用单引号（如 `<MyComponent prop='value' />`）。
   * - `false`: 使用双引号（如 `<MyComponent prop="value" />`）。
   * 例如，设置为false可以避免与 HTML 属性引号的冲突，符合多数 React 社区的风格。
   */
  jsxSingleQuote: false,

  /**
   * 总是为单参数箭头函数添加圆括号。
   * - `'always'`: 始终添加括号（如 `(x) => x + 1`）。
   * - `'avoid'`: 当只有一个参数时省略括号（如 `x => x + 1`）。
   * 例如，设置为'always'可以提高代码的可读性和一致性。
   */
  arrowParens: 'always',

  /**
   * 不在文件顶部添加特殊的 @format 标记，确保文件由 Prettier 格式化。
   * - `true`: 在文件顶部添加 `// @format` 标记。
   * - `false`: 不添加标记。
   * 例如，设置为false可以让 Prettier 自动格式化所有文件，无需手动添加标记。
   */
  insertPragma: false,

  /**
   * 不要求文件顶部存在特殊的 @format 标记才能格式化。
   * - `true`: 只有包含 `// @format` 标记的文件才会被格式化。
   * - `false`: 所有文件都会被格式化。
   * 例如，设置为false可以避免在每个文件顶部添加额外的注释，保持代码简洁。
   */
  requirePragma: false,

  /**
   * 不自动换行 Markdown 中的文本（如注释和文档）。
   * - `'always'`: 总是换行。
   * - `'never'`: 从不换行。
   * - `'preserve'`: 保留现有换行。
   * 例如，设置为'never'可以保持 Markdown 文本的原始格式，避免格式化工具对文档内容的干扰。
   */
  proseWrap: 'never',

  /**
   * 如何处理 HTML 文件中的空白字符。
   * - `'css'`: 根据 CSS display 属性决定。
   * - `'strict'`: 严格遵守空白字符规则。
   * - `'ignore'`: 忽略空白字符。
   * 例如，设置为'strict'可以确保 HTML 元素中的空白字符按预期显示，避免布局问题。
   */
  htmlWhitespaceSensitivity: 'strict',

  /**
   * 指定换行符的使用。
   * - `'auto'`: 根据操作系统自动选择（Windows使用CRLF，Unix/Linux和macOS使用LF）。
   * - `'lf'`: 使用换行符。
   * - `'crlf'`: 使用回车换行符。
   * - `'cr'`: 使用回车符。
   * 例如，设置为'auto'可以避免因不同操作系统换行符不一致而导致的版本控制差异。
   */
  endOfLine: 'auto',

  /**
   * 指定 Prettier 格式化的起始位置。
   * - 数字：指定从文件的哪个字符开始格式化。
   * - 例如，设置为0表示从文件的开头开始格式化。
   * - 适用于只想格式化文件的部分内容。
   */
  rangeStart: 0,
};
