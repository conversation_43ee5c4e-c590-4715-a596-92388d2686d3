import type { PermissionModeType } from '@/enums/base/permissionEnum';

export interface ProjectSettingState {
  //导航模式
  navMode: string;
  //导航风格
  navTheme: string;
  //顶部设置
  headerSetting: object;
  //页脚
  showFooter: boolean;
  //菜单设置
  menuSetting: object;
  //多标签
  multiTabsSetting: object;
  //面包屑
  crumbsSetting: object;
  //权限模式
  permissionMode: PermissionModeType;
}

export interface IBodySetting {
  fixed: boolean;
}

export interface IHeaderSetting {
  bgColor: string;
  fixed: boolean;
  isReload: boolean;
}

export interface IMenuSetting {
  minMenuWidth: number;
  menuWidth: number;
  fixed: boolean;
  mixMenu: boolean;
  collapsed: boolean;
  mobileWidth: number;
}

export interface ICrumbsSetting {
  show: boolean;
  showIcon: boolean;
}

export interface IMultiTabsSetting {
  bgColor: string;
  fixed: boolean;
  show: boolean;
}
export interface GlobConfig {
  title: string;
  apiUrl: string;
  shortName: string;
  urlPrefix?: string;
  uploadUrl?: string;
  prodMock: boolean;
  imgUrl?: string;
  // 是否启用Token超时锁定功能
  enableTokenTimeoutCheck?: boolean;
  prefix?: string;
  prefixModels?: string;
  // 基础服务前缀
  basicPrefix?: string;
  prefixExport?: string;
  prefixBasic?: string;
  lg_prefix?: string;
  setLogoName?: string;
  lg_prefixExport?: string;
  prefixHualong?: boolean;
}

export interface GlobEnvConfig {
  // 标题
  VITE_GLOB_APP_TITLE: string;
  // 接口地址
  VITE_GLOB_API_URL: string;
  // 接口前缀
  VITE_GLOB_API_URL_PREFIX?: string;
  // Project abbreviation
  VITE_GLOB_APP_SHORT_NAME: string;
  // 图片上传地址
  VITE_GLOB_UPLOAD_URL?: string;
  //图片前缀地址
  VITE_GLOB_IMG_URL?: string;
  //生产环境开启mock
  VITE_GLOB_PROD_MOCK: boolean;
  // 是否启用Token超时锁定功能
  VITE_GLOB_ENABLE_TOKEN_TIMEOUT_CHECK?: boolean;

  VITE_GLOB_PREFIX?: string;
  VITE_GLOB_PREFIX_BASIC?: string;
  VITE_GLOB_PREFIX_EXPORT?: string;
  VITE_GLOB_PREFIX_LG?: string;
  VITE_GLOB_PREFIX_EXPORT_LG?: string;
  VITE_GLOB_PREFIX_HUALONG?: boolean;
  VITE_GLOB_SET_LOGO_NAME?: string;
}
