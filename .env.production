# 是否开启mock
VITE_USE_MOCK = true

# 网站根目录
VITE_PUBLIC_PATH = /

# 网站前缀
VITE_BASE_URL = /

# 是否删除console
VITE_DROP_CONSOLE = true

# 是否启用Token超时锁定功能 (生产环境默认开启)
VITE_GLOB_ENABLE_TOKEN_TIMEOUT_CHECK = false

# API
VITE_GLOB_API_URL =

# 图片上传地址
VITE_GLOB_UPLOAD_URL=

# 图片前缀地址
VITE_GLOB_IMG_URL=



# 是否启用gzip压缩或brotli压缩
# 可选: gzip | brotli | none
# 如果你需要多种形式，你可以用','来分隔
VITE_BUILD_COMPRESS = 'none'
# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

##自己服务器发包线上
VITE_GLOB_PREFIX=/work/stocks-service
VITE_GLOB_PREFIX_BASIC=/work/basic-service
VITE_GLOB_PREFIX_EXPORT=/work/stocks-service
VITE_GLOB_PREFIX_LG=
VITE_GLOB_PREFIX_EXPORT_LG=/work
VITE_GLOB_PREFIX_HUALONG=true
 #接口前缀
VITE_GLOB_API_URL_PREFIX =



##设置logo
## 为空默认塔金，   xingye :兴业证券
#VITE_GLOB_SET_LOGO_NAME=
VITE_GLOB_SET_LOGO_NAME=xingye

