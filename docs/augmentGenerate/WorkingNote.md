## <font color="red">Working Note</font>

## <font color="purple">JavaScript</font>

### Promise

#### Introduction to Promise
A Promise is **an object** that represents a value that may not be available yet, but will be in the future. It is used to handle asynchronous operations in a more elegant way than callbacks.

A Promise can be in one of three states:

- **Pending**: The initial state of a Promise.
- **Fulfilled**: The state of a Promise when it has a value.
- **Rejected**: The state of a Promise when it has an error.

Promise<T> is **a generic type** that represents the type of the value that the Promise will hold.


#### Creating a Promise
To create a Promise, we use the `new` keyword followed by the `Promise` constructor. The constructor takes a single argument, which is a function that takes two arguments: `resolve` and `reject`.

The `resolve` function is used to resolve the Promise with a value, and the `reject` function is used to reject the Promise with an error.

Here is an example of how to create a Promise:

```javascript
const myPromise = new Promise((resolve, reject) => {
  // Do some asynchronous operation here
  setTimeout(() => {
    resolve("Success!");
  }, 2000);
});
```

## <font color="purple">Node.js</font>
### 项目结构
- `bin` - 存放可执行文件的目录
- `build` - 存放构建脚本的目录
- `config` - 存放配置文件的目录
- `dist` - 存放编译后的代码的目录
- `docs` - 存放文档的目录
- `lib` - 存放项目的主要代码的目录
- `node_modules` - 存放项目依赖的模块的目录
- `public` - 存放静态资源的目录
- `src` - 存放源代码的目录
- `test` - 存放测试脚本的目录
- `README.md` - 项目的说明文档
- `package.json` - 项目的配置文件
- `.gitignore` - 项目的Git忽略文件
- `.eslintrc.json` - 项目的ESLint配置文件
- `.prettierrc` - 项目的Prettier配置文件
- `jest.config.js` - 项目的Jest配置文件
- `webpack.config.js` - 项目的Webpack配置文件
- `tsconfig.json` - 项目的TypeScript配置文件
- `tslint.json` - 项目的TSLint配置文件
- `LICENSE` - 项目的许可证文件
- `package-lock.json` - 项目的依赖锁定文件
- `yarn.lock` - 项目的Yarn锁定文件
- `Dockerfile` - 项目的Dockerfile文件
- `docker-compose.yml` - 项目的Docker Compose配置文件
- `README.md` - 项目的说明文档
#### package.json
- Node.js 项目中的核心文件，用于定义项目的基本信息和依赖关系。它包含了项目的元数据、脚本命令、依赖项以及其他配置。
- 项目的名称、版本、描述、作者、许可证、关键字、仓库地址、bugs反馈地址、主页地址、作者邮箱、作者用户名、项目依赖等信息都在这里定义。
- 项目的依赖关系通过 dependencies、devDependencies、peerDependencies、optionalDependencies 字段定义。
- 项目的脚本命令通过 scripts 字段定义。
```json
"scripts": {
"start": "node index.js",
"test": "jest"
}
```


## <font color="purple">JSDoc</font>

### Introduction to JSDoc
JSDoc is a documentation tool used to generate documentation for JavaScript code. It allows developers to add comments to their code that can be used to generate documentation for that code.

Here is an example of how to use JSDoc:

```javascript
/**
 * This is a function that takes in two numbers and returns their sum.
 *
 * @param {number} num1 - The first number.
 * @param {number} num2 - The second number.
 *
 * @returns {number} The sum of the two numbers.
 */
function addNumbers(num1, num2) {
  return num1 + num2;
}
```
### Tags
JSDoc uses tags to provide additional information about the code. Here are some of the most commonly used tags:

- `@param` - Used to describe the parameters of a function or method.
- `@returns` - Used to describe the return value of a function or method.
- `@description` - Used to provide a general description of a function or method.
- `@example` - Used to provide an example of how to use a function or method.
- `@see` - Used to provide a link to related documentation.
- `@throws` - Used to describe any exceptions that a function or method can throw.
- `@deprecated` - Used to indicate that a function or method is no longer recommended for use.
- `@author` - Used to provide information about the author of a function or method.
- `@version` - Used to provide the version number of a function or method.
- `@license` - Used to provide information about the license under which a function or method is distributed.
- `@class` - Used to describe a class.
- `@constructor` - Used to describe the constructor of a class.
- `@method` - Used to describe a method of a class.
- `@property` - Used to describe a property of a class.
- `@module` - Used to describe a module.
- `@global` - Used to indicate that a variable or function is global.
- `@ignore` - Used to indicate that a function or method should be ignored by the documentation generator.
- `@todo` - Used to indicate that a function or method needs to be implemented.
- `@fixme` - Used to indicate that a function or method needs to be fixed.
- `@requires` - Used to indicate that a function or method requires a certain library or module.
- `@exports` - Used to indicate that a module exports a certain value.
- `@extends` - Used to indicate that a class extends another class.
- `@implements` - Used to indicate that a class implements an interface.
- `@default` - Used to indicate the default value of a parameter.

## <font color="purple">VUE3</font>
### 命名规范
#### Kebab-case 短横线命名法
1. 组件标签命名
   在模板（template）中，组件标签使用小写字母和短横线（kebab-case）。
   原因：HTML 对标签名不区分大小写，使用 kebab-case 可避免潜在的问题。
```vue
   <template>
   <user-profile></user-profile>
   </template>
```
2. 属性（Props）命名
   在模板中，属性名使用小写字母和短横线（kebab-case）。
   原因：与组件标签命名规则一致，增强可读性。
```vue
   <user-card :user-id="userId" :is-active="isActive"></user-card>
```

### Reactive VS Ref
#### Difference
1. <strong> 可接受的数据类型</strong>
- `ref` 可接受基本类型数据，如字符串、数字、布尔值等。也可以接受对象
  - ref是通过一个中间对象RefImpl持有数据，并通过重写它的set和get方法实现数据劫持的
  - 本质上依旧是通过Object.defineProperty()对RefImpl的value属性进行劫持。
```javascript
const count = ref(0)
const name = ref('John')
const person = ref({ name: 'John', age: 25 })
```
- `reactive` 只接受对象，不能接受基本类型数据。
  - reactive则是通过Proxy进行劫持的。Proxy无法对基本数据类型进行操作，进而导致reactive在面对基本数据类型时的束手无策。
```javascript
const state = reactive({ count: 0, name: 'John', person: { name: 'John', age: 25 } })
```

2. <strong> 返回值数据类型</strong>
- `ref` 返回一个包含数据的对象(一个持有原始数据的RefImpl实例)，可以通过`.value`属性获取数据。
```javascript
const count = ref(0)
//console.log(count.value) // 0
```
- `reactive` 返回的类型则是原始数据的代理Proxy实例。
```javascript
const state = reactive({ count: 0, name: 'John', person: { name: 'John', age: 25 } })
//console.log(state.count) // 0
state.count = 1
//console.log(state.count) // 1
```

3. <strong> 定义数据类型</strong>
```javascript
interface Count {
  num:number
}
const countRef:Ref<number> = ref(0)
const countReactive: Count = reactive({num:1})
```

4. <strong> 访问数据方式</strong>
- `ref()`
  - `ref()` 返回的是RefImpl的一个实例对象，该对象通过_value私有变量持有原始数据，并重写了value的get方法。
  - 当想要访问原始对象的时候，需要通过xxx.value的方式触发get函数获取数据。
  - 在修改数据时，也要通过xxx.value = yyy的方式触发set函数。
```javascript
const count = ref(0)
//console.log(count.value) // 0
count.value = 1
//console.log(count.value) // 1
```
- `reactive()`
  - `reactive()` 返回的是原始对象的代理，代理对象具有和原始对象相同的属性，因此我们可以直接通过.xxx的方式访问数据
  - 当想要访问原始对象的时候，直接通过xxx.属性名的方式即可。
  - 在修改数据时，也直接通过xxx.属性名 = yyy的方式即可。
```javascript
const state = reactive({ count: 0, name: 'John', person: { name: 'John', age: 25 } })
//console.log(state.count) // 0
state.count = 1
//console.log(state.count) // 1
```
- <strong> 如果`reactive()`中包含的对象中有`ref`类型的数据，则`reactive()`会将`ref`类型的数据自动展开转换为普通对象。</strong>`
- 例如：`const state = reactive({ count: 0, name: 'John', person: ref({ name: 'John', age: 25 }) })`
- 则`state.person`的值为`{ name: 'John', age: 25 }`。

5. <strong> 原始对象的可变性</strong>
- `ref` 可以给`ref`的值重新分配一个新的对象，依然能监听属性值的变化
- `reactive` 无法修改对象，只能修改当前代理对象的属性值

6. <strong> 侦听属性变化</strong>
- `ref` 无法侦听对象内部属性值的变化，只能监听整个对象的变化。
- `reactive` 可以侦听属性值的变化。
```javascript
let refCount = ref({count:0})
watch(refCount,() => {
  //console.log(`refCount数据变化了`)
})
refCount.value.count = 1

let reactiveCount = reactive({count:0})
watch(reactiveCount,() => {
  //console.log(`reactiveCount数据变化了`)
})
reactiveCount.count = 1
//输出结果
//reactiveCount数据变化了
```
- <strong> 如果需要侦听`ref`对象内部属性的变化，需要深度侦听</strong>
```javascript
let refObj = ref({count:0})
watch(refObj,() => {
  //console.log(`refObj数据变化了`)
},{deep:true})
refObj.value.count = 1
//输出结果
//refObj数据变化了
```

### 🛠️ **Props**

#### 📝 **defineProps**
`defineProps` 是一个编译器宏，用于在 `<script setup>` 中声明组件接收的 props。
```
<script setup lang="ts">
const props = defineProps<{
  propA: string;
  propB?: number;
}>();
</script>
```

#### 📋 **Props 的类型定义**
- **类型定义的好处**：
  - 帮助开发者更好地理解组件的用法，**提高代码的可读性**。
  - 提供更好的代码提示，**提升开发效率**。
  - 让 TypeScript 编译器**检查代码的类型安全**。
```
<script setup lang="ts">
import { PropType } from 'vue';

interface User {
  id: number;
  name: string;
}

const props = defineProps<{
  user: User;
  isActive?: boolean;
  tags?: string[];
  settings?: {
    theme: string;
    notifications: boolean;
  };
}>();
</script>
```

#### 🛠️ **Props 定义复杂类型**

| **字段**  | **类型**       | **说明**               |
|-----------|----------------|------------------------|
| `user`    | `Object`       | 必填，用户对象         |
| `tags`    | `Array`        | 可选，标签数组，默认空 |

```
<script setup lang="ts">
import { PropType } from 'vue';

interface User {
  id: number;
  name: string;
}

const props = defineProps({
  user: {
    type: Object as PropType<User>,
    required: true,
  },
  tags: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});
</script>
```

#### 🎯 **Props 的默认值**
```
<script setup lang="ts">
import { PropType } from 'vue';

const props = withDefaults(defineProps<{
  propA: string;
  propB: number;
}>(), {
  propB: 100,
});
</script>
```
- 引用类型默认值必须使用函数形式
```
<script setup lang="ts">
import { PropType } from 'vue';

const props = withDefaults(defineProps<{
  propA: string;
  propB: {
    name: string;
    age: number;
  };
}>(), {
  propB: () => ({
    name: 'John',
    age: 25,
  }),
});
</script>
```
