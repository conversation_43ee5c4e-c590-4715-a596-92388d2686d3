# Tarkin Risk Management Platform 依赖版本分析报告

**生成时间**: 2024年12月18日  
**分析范围**: 全项目依赖（dependencies + devDependencies）  
**分析工具**: npm outdated + npm audit + 手动调研  

## 📊 总体概况

- **总依赖数量**: 28 个生产依赖 + 50 个开发依赖
- **需要升级的包**: 54 个
- **安全漏洞**: 21 个（1个低危，11个中危，8个高危，1个严重）
- **主要框架版本**: Vue 3.4.15 → 3.5.16，Vite 4.5.0 → 6.3.5

---

## 🚨 高优先级升级（安全漏洞修复）

### 严重安全漏洞

| 包名 | 当前版本 | 建议版本 | 漏洞描述 | 升级命令 |
|------|----------|----------|----------|----------|
| **gh-pages** | 4.0.0 | 6.3.0 | 原型污染漏洞 (CVE) | `npm install gh-pages@6.3.0` |

### 高危安全漏洞

| 包名 | 当前版本 | 建议版本 | 漏洞描述 | 升级命令 |
|------|----------|----------|----------|----------|
| **braces** | <3.0.3 | 3.0.3+ | 资源消耗攻击 | `npm audit fix` |
| **canvg** | <3.0.11 | 3.0.11+ | 原型污染漏洞 | `npm audit fix` |
| **cross-spawn** | 7.0.0-7.0.4 | 7.0.5+ | 正则表达式拒绝服务 | `npm audit fix` |
| **mockjs** | 任意版本 | 替换方案 | 原型污染漏洞 | 考虑替换为 MSW |
| **path-to-regexp** | 4.0.0-6.2.2 | 6.2.3+ | 回溯正则表达式 | `npm audit fix` |

### 核心框架安全更新

| 包名 | 当前版本 | 建议版本 | 升级原因 | 升级命令 |
|------|----------|----------|----------|----------|
| **axios** | 1.8.2 | 1.10.0 | 修复 CVE-2024-39338 SSRF 漏洞 | `npm install axios@1.10.0` |
| **vite** | 4.5.0 | 6.3.5 | 修复开发服务器安全漏洞 | `npm install vite@6.3.5` |

---

## ⚡ 中优先级升级（功能改进与兼容性）

### Vue 3 生态系统

| 包名 | 当前版本 | 建议版本 | 升级原因 | 兼容性风险 |
|------|----------|----------|----------|------------|
| **vue** | 3.4.15 | 3.5.16 | Vue 3.5 新特性，性能提升 | 低 |
| **@vue/compiler-sfc** | 3.4.15 | 3.5.16 | 与 Vue 版本保持一致 | 低 |
| **vue-router** | 4.2.5 | 4.5.1 | Bug 修复，新特性 | 低 |
| **pinia** | 2.1.7 | 3.0.3 | 主版本升级，新特性 | 中 |

### TypeScript 工具链

| 包名 | 当前版本 | 建议版本 | 升级原因 | 兼容性风险 |
|------|----------|----------|----------|------------|
| **typescript** | 5.3.3 | 5.8.3 | 最新稳定版，性能改进 | 低 |
| **@typescript-eslint/eslint-plugin** | 6.0.0 | 8.34.1 | 支持 TS 5.8，新规则 | 中 |
| **@typescript-eslint/parser** | 6.0.0 | 8.34.1 | 与插件版本保持一致 | 中 |

### 构建工具链

| 包名 | 当前版本 | 建议版本 | 升级原因 | 兼容性风险 |
|------|----------|----------|----------|------------|
| **@vitejs/plugin-vue** | 4.0.0 | 5.2.4 | 支持 Vue 3.5，Vite 6 | 中 |
| **@vitejs/plugin-vue-jsx** | 3.0.0 | 4.2.0 | 与主插件版本保持一致 | 中 |
| **tailwindcss** | 3.4.1 | 4.1.10 | 主版本升级，新特性 | 高 |

### UI 组件库

| 包名 | 当前版本 | 建议版本 | 升级原因 | 兼容性风险 |
|------|----------|----------|----------|------------|
| **naive-ui** | 2.38.2 | 2.42.0 | Bug 修复，新组件 | 低 |
| **echarts** | 5.4.3 | 5.6.0 | 性能优化，新图表类型 | 低 |

---

## 🔧 低优先级升级（非关键依赖）

### 开发工具

| 包名 | 当前版本 | 建议版本 | 升级原因 |
|------|----------|----------|----------|
| **eslint** | 8.56.0 | 9.29.0 | 主版本升级，新配置格式 |
| **prettier** | 2.8.8 | 3.5.3 | 主版本升级，性能改进 |
| **stylelint** | 14.16.1 | 16.20.0 | 主版本升级，新规则 |

### 工具库

| 包名 | 当前版本 | 建议版本 | 升级原因 |
|------|----------|----------|----------|
| **@vueuse/core** | 9.13.0 | 13.3.0 | 新组合式函数，性能优化 |
| **date-fns** | 2.30.0 | 4.1.0 | 主版本升级，Tree-shaking 改进 |
| **lodash-es** | 4.17.21 | 4.17.21 | 已是最新版本 |

---

## 🚧 需要特别注意的升级

### 1. **Tailwind CSS 3.x → 4.x**
```bash
# 需要重大配置更改
npm install tailwindcss@4.1.10
```
**风险**: 配置文件格式变更，可能需要重写样式

### 2. **ESLint 8.x → 9.x**
```bash
# 需要更新配置格式
npm install eslint@9.29.0
```
**风险**: 配置文件从 `.eslintrc.js` 迁移到 `eslint.config.js`

### 3. **Pinia 2.x → 3.x**
```bash
npm install pinia@3.0.3
```
**风险**: API 变更，可能需要更新状态管理代码

### 4. **Vite 4.x → 6.x**
```bash
npm install vite@6.3.5
```
**风险**: 插件兼容性，构建配置可能需要调整

---

## 📋 推荐升级策略

### 阶段一：安全漏洞修复（立即执行）
```bash
# 修复自动可修复的漏洞
npm audit fix

# 手动修复严重漏洞
npm install gh-pages@6.3.0
npm install axios@1.10.0

# 替换有问题的依赖
# 考虑用 MSW 替换 mockjs
npm uninstall mockjs vite-plugin-mock
npm install msw@latest
```

### 阶段二：核心框架升级（1-2周内）
```bash
# Vue 生态系统升级
npm install vue@3.5.16 @vue/compiler-sfc@3.5.16
npm install vue-router@4.5.1
npm install naive-ui@2.42.0

# TypeScript 升级
npm install typescript@5.8.3
```

### 阶段三：构建工具升级（2-4周内）
```bash
# Vite 升级（需要测试）
npm install vite@6.3.5
npm install @vitejs/plugin-vue@5.2.4
npm install @vitejs/plugin-vue-jsx@4.2.0
```

### 阶段四：开发工具升级（按需）
```bash
# ESLint 升级（需要配置迁移）
npm install eslint@9.29.0
npm install @typescript-eslint/eslint-plugin@8.34.1
npm install @typescript-eslint/parser@8.34.1
```

---

## ⚠️ 升级前准备工作

1. **备份当前代码**
   ```bash
   git add .
   git commit -m "backup before dependency upgrade"
   ```

2. **创建升级分支**
   ```bash
   git checkout -b feature/dependency-upgrade
   ```

3. **更新 Node.js 版本**
   - 当前要求: `>=16`
   - 建议升级到: `>=18` (LTS)

4. **测试环境准备**
   - 确保所有测试用例通过
   - 准备回滚方案

---

## 🔍 升级后验证清单

- [ ] 项目能正常启动 (`npm run dev`)
- [ ] 构建成功 (`npm run build`)
- [ ] 所有页面正常渲染
- [ ] Naive UI 组件功能正常
- [ ] ECharts 图表显示正常
- [ ] TypeScript 类型检查通过
- [ ] ESLint 检查通过
- [ ] 单元测试通过

---

## 📞 技术支持

如果在升级过程中遇到问题，建议：

1. 查看各包的 CHANGELOG 和迁移指南
2. 在测试环境先进行小范围验证
3. 逐步升级，避免一次性升级所有依赖
4. 保持与 Vue 3 + Naive UI 生态的兼容性

**总结**: 建议优先处理安全漏洞，然后逐步升级核心框架，最后处理开发工具。整个升级过程预计需要 2-4 周时间。

---

## 📈 详细依赖列表

### 生产依赖 (dependencies)

| 包名 | 当前版本 | 最新版本 | 状态 | 优先级 |
|------|----------|----------|------|--------|
| @tinymce/tinymce-vue | 6.0.1 | 6.2.0 | 需要升级 | 中 |
| @vicons/antd | 0.12.0 | 0.13.0 | 需要升级 | 低 |
| @vicons/ionicons5 | 0.12.0 | 0.13.0 | 需要升级 | 低 |
| @vueup/vue-quill | 1.2.0 | 1.2.0 | 最新 | - |
| @vueuse/core | 9.13.0 | 13.3.0 | 需要升级 | 中 |
| @vueuse/motion | 2.2.3 | 3.0.3 | 需要升级 | 中 |
| axios | 1.8.2 | 1.10.0 | **安全更新** | 高 |
| blueimp-md5 | 2.19.0 | 2.19.0 | 最新 | - |
| date-fns | 2.30.0 | 4.1.0 | 需要升级 | 低 |
| echarts | 5.4.3 | 5.6.0 | 需要升级 | 中 |
| echarts-gl | 2.0.9 | 2.0.9 | 最新 | - |
| element-resize-detector | 1.2.4 | 1.2.4 | 最新 | - |
| html2canvas | 1.0.0-alpha.12 | 1.4.1 | 需要升级 | 中 |
| jspdf | 2.5.2 | 3.0.1 | 需要升级 | 中 |
| lodash-es | 4.17.21 | 4.17.21 | 最新 | - |
| mitt | 3.0.1 | 3.0.1 | 最新 | - |
| mockjs | 1.1.0 | - | **安全风险** | 高 |
| naive-ui | 2.38.2 | 2.42.0 | 需要升级 | 中 |
| pinia | 2.1.7 | 3.0.3 | 需要升级 | 中 |
| qs | 6.11.2 | 6.14.0 | 需要升级 | 中 |
| sortablejs | 1.15.2 | 1.15.6 | 需要升级 | 低 |
| tinymce | 7.2.1 | 7.9.1 | 需要升级 | 中 |
| vfonts | 0.0.3 | 0.0.3 | 最新 | - |
| vue | 3.4.15 | 3.5.16 | 需要升级 | 高 |
| vue-router | 4.2.5 | 4.5.1 | 需要升级 | 中 |
| vue-slider-component | 4.1.0-beta.7 | 3.2.24 | 版本回退 | 低 |
| vue-types | 4.2.1 | 6.0.0 | 需要升级 | 低 |

### 开发依赖 (devDependencies)

| 包名 | 当前版本 | 最新版本 | 状态 | 优先级 |
|------|----------|----------|------|--------|
| @commitlint/cli | 17.8.1 | 19.8.1 | 需要升级 | 低 |
| @commitlint/config-conventional | 17.8.1 | 19.8.1 | 需要升级 | 低 |
| @types/lodash | 4.14.202 | 4.17.18 | 需要升级 | 低 |
| @types/node | 20.0.0 | 24.0.3 | 需要升级 | 中 |
| @types/sortablejs | 1.15.8 | 1.15.8 | 最新 | - |
| @typescript-eslint/eslint-plugin | 6.0.0 | 8.34.1 | 需要升级 | 中 |
| @typescript-eslint/parser | 6.0.0 | 8.34.1 | 需要升级 | 中 |
| @vitejs/plugin-vue | 4.0.0 | 5.2.4 | 需要升级 | 中 |
| @vitejs/plugin-vue-jsx | 3.0.0 | 4.2.0 | 需要升级 | 中 |
| @vue/compiler-sfc | 3.4.15 | 3.5.16 | 需要升级 | 高 |
| @vue/eslint-config-typescript | 11.0.3 | 14.5.0 | 需要升级 | 中 |
| autoprefixer | 10.4.17 | 10.4.21 | 需要升级 | 低 |
| commitizen | 4.3.0 | 4.3.1 | 需要升级 | 低 |
| core-js | 3.35.1 | 3.43.0 | 需要升级 | 低 |
| cross-env | 7.0.3 | 7.0.3 | 最新 | - |
| dotenv | 16.4.5 | 16.5.0 | 需要升级 | 低 |
| eslint | 8.56.0 | 9.29.0 | 需要升级 | 中 |
| eslint-config-prettier | 8.10.0 | 10.1.5 | 需要升级 | 低 |
| eslint-define-config | 1.12.0 | 2.1.0 | 需要升级 | 低 |
| eslint-plugin-jest | 27.6.3 | 29.0.0 | 需要升级 | 低 |
| eslint-plugin-prettier | 4.2.1 | 5.5.0 | 需要升级 | 低 |
| eslint-plugin-vue | 9.21.1 | 10.2.0 | 需要升级 | 中 |
| esno | 0.16.3 | 4.8.0 | 需要升级 | 低 |
| gh-pages | 4.0.0 | 6.3.0 | **安全更新** | 高 |
| husky | 8.0.3 | 9.1.7 | 需要升级 | 低 |
| jest | 29.7.0 | 30.0.0 | 需要升级 | 低 |
| less | 4.2.0 | 4.3.0 | 需要升级 | 低 |
| less-loader | 11.1.4 | 12.3.0 | 需要升级 | 低 |
| lint-staged | 13.3.0 | 16.1.2 | 需要升级 | 低 |
| postcss | 8.4.33 | 8.5.6 | 需要升级 | 低 |
| prettier | 2.8.8 | 3.5.3 | 需要升级 | 中 |
| pretty-quick | 3.3.1 | 4.2.2 | 需要升级 | 低 |
| rimraf | 3.0.2 | 6.0.1 | 需要升级 | 低 |
| stylelint | 14.16.1 | 16.20.0 | 需要升级 | 中 |
| stylelint-config-prettier | 9.0.5 | 9.0.5 | 最新 | - |
| stylelint-config-standard | 29.0.0 | 38.0.0 | 需要升级 | 低 |
| stylelint-order | 5.0.0 | 7.0.0 | 需要升级 | 低 |
| stylelint-scss | 4.7.0 | 6.12.1 | 需要升级 | 低 |
| tailwindcss | 3.4.1 | 4.1.10 | 需要升级 | 高 |
| typescript | 5.3.3 | 5.8.3 | 需要升级 | 中 |
| unplugin-vue-components | 0.22.12 | 28.7.0 | 需要升级 | 中 |
| vite | 4.5.0 | 6.3.5 | **安全更新** | 高 |
| vite-plugin-compression | 0.5.1 | 0.5.1 | 最新 | - |
| vite-plugin-html | 3.2.0 | 3.2.0 | 最新 | - |
| vite-plugin-mock | 2.9.8 | 3.0.2 | 需要升级 | 中 |
| vite-plugin-style-import | 2.0.0 | 2.0.0 | 最新 | - |
| vue-demi | 0.13.11 | 0.14.10 | 需要升级 | 低 |
| vue-draggable-next | 2.2.1 | 2.2.1 | 最新 | - |
| vue-eslint-parser | 9.4.2 | 10.1.3 | 需要升级 | 中 |
| vuedraggable | 4.1.0 | 2.24.3 | 版本回退 | 低 |

---

## 🎯 升级优先级说明

- **高优先级**: 安全漏洞修复、核心框架升级
- **中优先级**: 功能改进、兼容性提升、开发体验改进
- **低优先级**: 非关键依赖的小版本更新

---

## 📝 升级记录

| 日期 | 操作 | 包名 | 版本变更 | 状态 |
|------|------|------|----------|------|
| 2024-12-18 | 分析 | 全部依赖 | - | ✅ 完成 |
| 2024-12-18 | 升级 | vue | 3.4.15 → 3.5.16 | ✅ 完成 |
| 2024-12-18 | 升级 | @vue/compiler-sfc | 3.4.15 → 3.5.16 | ✅ 完成 |
| 2024-12-18 | 升级 | vue-router | 4.2.5 → 4.5.1 | ✅ 完成 |
| 2024-12-18 | 升级 | pinia | 2.1.7 → 2.3.1 | ✅ 完成 (回滚到兼容版本) |
| 2024-12-18 | 升级 | typescript | 5.3.3 → 5.8.3 | ✅ 完成 |
| 2024-12-18 | 升级 | @typescript-eslint/eslint-plugin | 6.0.0 → 8.34.1 | ✅ 完成 |
| 2024-12-18 | 升级 | @typescript-eslint/parser | 6.0.0 → 8.34.1 | ✅ 完成 |
| 2024-12-18 | 升级 | eslint-plugin-jest | 27.6.3 → 29.0.0 | ✅ 完成 |
| 2024-12-18 | 升级 | @vitejs/plugin-vue | 4.0.0 → 4.6.2 | ✅ 完成 |
| 2024-12-18 | 升级 | @vitejs/plugin-vue-jsx | 3.0.0 → 3.1.0 | ✅ 完成 |
| 2024-12-18 | 升级 | naive-ui | 2.38.2 → 2.38.2 | ✅ 保持原版本 (兼容性) |
| 2024-12-18 | 升级 | echarts | 5.4.3 → 5.6.0 | ✅ 完成 |
| 2024-12-18 | 升级 | @vueuse/core | 9.13.0 → 13.3.0 | ✅ 完成 |
| 2024-12-18 | 升级 | @vueuse/motion | 2.2.3 → 3.0.3 | ✅ 完成 |
| 2024-12-18 | 跳过 | date-fns | 2.30.0 → 4.1.0 | ❌ 跳过 (兼容性问题) |

---

*本报告由 Augment Agent 自动生成，建议定期更新以保持依赖的安全性和最新性。*
