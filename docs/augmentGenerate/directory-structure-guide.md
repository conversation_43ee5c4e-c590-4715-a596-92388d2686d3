# 📁 项目目录结构规范指南

## 概述

本文档详细说明了 `src/components` 和 `src/views` 两个核心目录的职责划分、文件组织原则和最佳实践，旨在帮助开发团队保持代码结构的一致性和可维护性。

---

## 🧩 `src/components` - 组件目录

### 📋 职责定义

`src/components` 目录专门存放**可复用的 Vue 组件**，这些组件具有以下特征：

- ✅ **可复用性**：能在多个页面或场景中使用
- ✅ **独立性**：不依赖特定的页面上下文
- ✅ **通用性**：解决通用的 UI 或业务问题
- ✅ **封装性**：具有明确的输入输出接口

### 🗂️ 目录结构

```
src/components/
├── base/                    # 基础 UI 组件
│   ├── Button/
│   ├── Input/
│   └── Modal/
├── business/                # 业务组件
│   ├── LabelCascader.vue
│   ├── LabelTable.vue
│   └── SecuritySearch.vue
├── form/                    # 表单相关组件
│   ├── FormBuilder/
│   └── FormValidator/
├── chart/                   # 图表组件
│   ├── LineChart.vue
│   └── BarChart.vue
├── layout/                  # 布局组件
│   ├── PageHeader.vue
│   └── Sidebar.vue
└── common/                  # 通用工具组件
    ├── Loading.vue
    └── Empty.vue
```

### 📝 组件分类详解

#### 1. **基础组件 (`base/`)**
- **用途**：封装基础 UI 元素，提供统一的设计规范
- **特点**：高度通用，不包含业务逻辑
- **示例**：
  ```vue
  <!-- CustomButton.vue -->
  <template>
    <n-button :type="type" :size="size" @click="handleClick">
      <slot />
    </n-button>
  </template>
  ```

#### 2. **业务组件 (`business/`)**
- **用途**：封装特定业务领域的功能组件
- **特点**：包含业务逻辑，但可在多个页面复用
- **示例**：
  ```vue
  <!-- LabelCascader.vue - 标签级联选择器 -->
  <!-- SecuritySearch.vue - 证券搜索组件 -->
  <!-- RiskIndicator.vue - 风险指标组件 -->
  ```

#### 3. **表单组件 (`form/`)**
- **用途**：处理表单相关的通用功能
- **特点**：专注于数据输入、验证和提交
- **示例**：动态表单构建器、表单验证器

#### 4. **图表组件 (`chart/`)**
- **用途**：数据可视化相关组件
- **特点**：封装图表库，提供统一的图表接口
- **示例**：折线图、柱状图、饼图组件

### 🎯 组件命名规范

- **文件命名**：使用 PascalCase，如 `LabelCascader.vue`
- **组件名称**：与文件名保持一致
- **目录命名**：使用 camelCase，如 `businessComponents/`

---

## 📄 `src/views` - 页面目录

### 📋 职责定义

`src/views` 目录专门存放**页面级组件**，这些组件具有以下特征：

- ✅ **页面性**：对应具体的路由页面
- ✅ **业务完整性**：实现完整的业务功能
- ✅ **上下文相关**：依赖特定的业务上下文
- ✅ **组合性**：组合多个组件实现页面功能

### 🗂️ 目录结构

```
src/views/
├── dashboard/               # 仪表板模块
│   └── workplace/
│       ├── workplace.vue    # 主页面
│       ├── SecDetails.vue   # 证券详情页
│       └── components/      # 页面专用组件
├── myDesk/                  # 我的工作台模块
│   ├── ratingStrategyModels/
│   │   ├── index.vue
│   │   └── components/
│   └── portfolioManagement/
├── auditManage/             # 审核管理模块
│   ├── concentrationAudit/
│   └── collateralAudit/
└── tarkinResearch/          # 塔金研究模块
    ├── labelStrategy/
    └── riskAnalysis/
```

### 📝 页面组织原则

#### 1. **按业务模块分组**
```
src/views/
├── 业务模块A/
├── 业务模块B/
└── 业务模块C/
```

#### 2. **页面内部结构**
```
具体页面目录/
├── index.vue               # 主页面文件
├── components/             # 页面专用组件
│   ├── DataTable.vue
│   └── FilterForm.vue
├── composables/            # 页面专用逻辑
│   └── usePageData.ts
└── types/                  # 页面专用类型
    └── index.ts
```

#### 3. **页面专用组件**
- **位置**：`页面目录/components/`
- **特点**：只在当前页面使用，不具备通用性
- **命名**：描述具体功能，如 `UserDataTable.vue`

---

## 🔄 组件复用策略

### 📊 复用级别判断

| 复用范围 | 存放位置 | 示例 |
|---------|----------|------|
| 全项目复用 | `src/components/` | 通用搜索框、数据表格 |
| 模块内复用 | `src/views/模块/components/` | 模块特定的表单组件 |
| 页面内复用 | `src/views/页面/components/` | 页面内的卡片组件 |
| 单次使用 | 直接写在页面内 | 简单的展示组件 |

### 🎯 提升复用性的方法

1. **抽象通用逻辑**：将业务逻辑抽象为 props 和 events
2. **插槽设计**：提供灵活的内容插槽
3. **配置化**：通过配置对象控制组件行为
4. **组合式设计**：将复杂组件拆分为多个小组件

---

## 📋 最佳实践

### ✅ 推荐做法

1. **组件职责单一**：每个组件只负责一个明确的功能
2. **接口设计清晰**：明确定义 props、events 和 slots
3. **文档完善**：为复用组件编写详细的使用文档
4. **类型安全**：使用 TypeScript 确保类型安全
5. **测试覆盖**：为通用组件编写单元测试

### ❌ 避免做法

1. **过度抽象**：不要为了复用而强行抽象
2. **职责混乱**：避免在组件中混合多种职责
3. **硬编码**：避免在通用组件中硬编码业务逻辑
4. **循环依赖**：避免组件之间的循环引用
5. **命名冲突**：避免组件名称与原生 HTML 元素冲突

---

## 🔍 实际案例分析

### 案例 1：标签选择器

**当前状态**：
```
src/components/labelComponent/LabelCascader.vue  ❌ 目录命名不规范
```

**建议优化**：
```
src/components/business/LabelCascader.vue        ✅ 按功能分类
```

### 案例 2：页面组件

**正确示例**：
```
src/views/myDesk/ratingStrategyModels/
├── index.vue                                   ✅ 主页面
└── StrategyConfigWorkspace/
    ├── StrategyConditionBuilder.vue            ✅ 页面级组件
    └── StrategyConditionBuilder/
        ├── LabelTypeSelector.vue               ✅ 页面专用组件
        └── IndustrySelector.vue
```

---

## 📚 相关文档

- [Vue 3 组件开发指南](./vue3-component-guide.md)
- [TypeScript 类型定义规范](./typescript-guide.md)
- [代码审查清单](./code-review-checklist.md)

---

**文档版本**：v1.0  
**最后更新**：2024-12-19  
**维护者**：前端开发团队
