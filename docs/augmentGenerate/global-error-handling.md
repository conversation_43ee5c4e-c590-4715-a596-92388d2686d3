# 全局HTTP错误处理功能

## 功能概述

为了解决接口报错时页面没有提示的问题，我们实现了一个全局的HTTP错误处理机制。当任何后端接口返回非200状态码（如403、500等）时，系统会自动在页面上弹窗显示错误信息。

## 实现原理

### 1. 双重拦截机制

我们在两个层面实现了错误拦截：

#### 业务层面拦截（transformRequestData）
- 处理后端返回的业务错误码（如code不为200的情况）
- 优先显示后端返回的具体错误信息
- 支持自定义错误信息显示

#### HTTP状态码拦截（responseInterceptorsCatch）
- 处理HTTP协议层面的错误（如500、403、404等）
- 确保所有网络错误都能被捕获和显示
- 增强了原有的checkStatus函数

### 2. 错误信息优先级

错误信息显示遵循以下优先级：
1. **后端返回的具体错误信息** - response.data.msg 或 response.data.message
2. **默认状态码错误信息** - 根据HTTP状态码显示相应的默认错误信息
3. **通用错误信息** - 当无法获取具体信息时显示通用错误

## 支持的错误类型

### HTTP状态码错误
- **400** - 请求参数错误
- **401** - 用户没有权限（令牌、用户名、密码错误）
- **403** - 用户得到授权，但是访问是被禁止的
- **404** - 网络请求错误，未找到该资源
- **405** - 网络请求错误，请求方法未允许
- **408** - 网络请求超时
- **500** - 服务器错误（会优先显示后端返回的具体错误信息）
- **501** - 网络未实现
- **502** - 网络错误
- **503** - 服务不可用，服务器暂时过载或维护
- **504** - 网络超时
- **505** - HTTP版本不支持该请求

### 业务错误码
- **-1** - 请求失败
- **403** - 权限错误
- **10042** - 请求超时
- **10043** - Token过期

## 修改的文件

### 1. src/utils/http/axios/index.ts
- 增强了 `transformRequestData` 函数，添加了全局错误处理逻辑
- 增强了 `responseInterceptorsCatch` 函数，确保HTTP错误能被正确显示

### 2. src/utils/http/axios/checkStatus.ts
- 修改了 `checkStatus` 函数，优先显示后端返回的具体错误信息
- 添加了 `getDefaultErrorMessage` 函数，提供默认错误信息

## 使用方法

### 自动处理
所有通过 `http` 实例发起的请求都会自动应用全局错误处理，无需额外配置。

```typescript
import { http } from '@/utils/http/axios';

// 这个请求如果返回500错误，会自动显示错误信息
const result = await http.get('/api/some-endpoint');
```

### 禁用错误提示
如果某个接口不需要显示错误提示，可以通过配置禁用：

```typescript
const result = await http.get('/api/some-endpoint', {
  isShowMessage: false  // 禁用错误信息显示
});
```

## 测试功能

我们提供了测试文件来验证全局错误处理功能：

### 开发环境测试
在开发环境下，可以在浏览器控制台中使用以下命令测试：

```javascript
// 运行所有测试
window.testGlobalErrorHandling.runAllTests();

// 单独测试500错误
window.testGlobalErrorHandling.test500Error();

// 单独测试403错误
window.testGlobalErrorHandling.test403Error();

// 单独测试404错误
window.testGlobalErrorHandling.test404Error();
```

## 注意事项

1. **Token过期处理** - 403错误会优先检查是否为token过期，如果是则执行相应的登录跳转逻辑
2. **网络错误** - 网络连接错误会显示专门的对话框提示
3. **请求取消** - 被取消的请求不会显示错误信息
4. **错误信息格式** - 后端返回的错误信息应该在 `response.data.msg` 或 `response.data.message` 字段中

## 效果

实现后的效果：
- ✅ 500服务器错误会立即弹窗显示具体错误信息
- ✅ 403权限错误会显示相应提示
- ✅ 404未找到错误会显示提示
- ✅ 所有其他HTTP错误都会有相应的错误提示
- ✅ 开发工程师能够及时发现接口问题
- ✅ 用户体验得到改善，不会出现静默失败的情况

这个全局错误处理机制确保了所有接口错误都能被及时发现和处理，大大提高了系统的可维护性和用户体验。
