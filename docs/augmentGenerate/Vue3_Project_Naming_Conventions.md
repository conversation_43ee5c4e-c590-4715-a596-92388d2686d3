
# 企业级Vue 3项目命名规范指南

## 目录
1. [文件和文件夹命名](#文件和文件夹命名)
2. [组件命名](#组件命名)

## 文件和文件夹命名

### PascalCase（大驼峰命名）
- **组件文件**：所有组件文件应使用PascalCase命名，例如`MyComponent.vue`。这种命名方式可以帮助区分组件文件与其他类型的文件。
- **页面文件**：表示页面的Vue文件也建议使用PascalCase命名，例如`UserProfile.vue`。

### kebab-case（短横线命名）
- **路由组件**：路由组件通常使用kebab-case命名，例如`user-profile.vue`。这种命名方式便于区分路由组件和其他文件。
- **非组件文件**：对于独立的样式文件或其他非组件的Vue文件，也建议使用kebab-case命名。

### 模块化命名
- **命名空间**：如果项目较大，可以使用文件夹和命名空间。例如 `user/Profile.vue` 和 `admin/Dashboard.vue`，这样不仅可以保持文件结构清晰，还可以避免文件名冲突。

## 组件命名

### PascalCase（大驼峰命名）
- **全局组件**：建议所有全局注册的组件使用PascalCase命名，方便在整个项目中引用。
- **局部组件**：对于局部注册的组件，也应遵循PascalCase命名规范。

### 组件文件名和组件名称一致
- **一致性**：组件文件名应与组件名称保持一致，这样可以提高代码的可读性和可维护性。

### 组件名称前缀
- **命名空间前缀**：对于特定功能模块的组件，可以使用前缀以表示它们属于同一模块。例如，所有用户管理相关的组件可以使用`User`前缀，如`UserList.vue`、`UserProfile.vue`。

