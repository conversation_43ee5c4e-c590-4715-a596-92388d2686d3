# 🧩 Composables 目录规范指南

## 📋 概述

本文档详细说明了 `composables` 目录的作用、组织规范和最佳实践，旨在帮助开发团队正确使用 Vue 3 Composition API 进行逻辑复用和代码组织。

---

## 🎯 什么是 Composables

### 📖 **定义**
Composables 是基于 Vue 3 Composition API 的**可组合函数**，用于封装和复用**有状态的逻辑**。它们是 Vue 3 中实现逻辑复用的主要方式，类似于 Vue 2 中的 mixins，但更加灵活和类型安全。

### ✨ **核心特征**
- **响应式状态管理**：使用 `ref`、`reactive` 等响应式 API
- **生命周期集成**：可以使用 `onMounted`、`onUnmounted` 等生命周期钩子
- **逻辑封装**：将相关的状态和方法组织在一起
- **可复用性**：可以在多个组件中重复使用
- **类型安全**：完整的 TypeScript 支持

---

## 📁 目录结构规范

### 🌍 **全局 Composables**
```
src/composables/
├── options/                    # 选项数据相关
│   └── index.ts               # 下拉选项、配置选项等
├── handleSorter.ts            # 排序处理逻辑
├── useSorter.js              # 排序功能（待重构为 TS）
├── useAuth.ts                # 认证相关逻辑
├── usePermission.ts          # 权限管理逻辑
├── useTheme.ts               # 主题切换逻辑
└── useRequest.ts             # 请求封装逻辑
```

### 🏠 **页面级 Composables**
```
src/views/页面目录/composables/
├── usePageData.ts            # 页面数据管理
├── usePageActions.ts         # 页面操作逻辑
├── usePageForm.ts            # 表单处理逻辑
├── usePageTable.ts           # 表格相关逻辑
└── usePageValidation.ts      # 验证逻辑
```

### 🧩 **组件级 Composables**
```
src/views/页面目录/组件目录/composables/
├── useComponentLogic.ts      # 组件特定逻辑
└── useComponentState.ts      # 组件状态管理
```

---

## 📝 Composables 文件类型分类

### 1. **数据管理类** (`useData*`)
**用途**：管理页面或组件的数据状态、API 调用、数据转换

**示例**：
```typescript
// usePageData.ts
import { ref, onMounted } from 'vue'
import { getUserList } from '@/api/user'

export function usePageData() {
  const loading = ref(false)
  const userList = ref([])
  
  const fetchUserList = async () => {
    loading.value = true
    try {
      const res = await getUserList()
      userList.value = res.data
    } finally {
      loading.value = false
    }
  }
  
  onMounted(() => {
    fetchUserList()
  })
  
  return {
    loading,
    userList,
    fetchUserList,
    refreshData: fetchUserList
  }
}
```

### 2. **表单处理类** (`useForm*`)
**用途**：处理表单状态、验证、提交等逻辑

**示例**：
```typescript
// useStrategyForm.ts
import { reactive, ref } from 'vue'

export interface StrategyFormData {
  name: string
  type: string
  conditions: any[]
}

export function useStrategyForm() {
  const formData = reactive<StrategyFormData>({
    name: '',
    type: '',
    conditions: []
  })
  
  const formRef = ref()
  const loading = ref(false)
  
  const validateForm = () => {
    return formRef.value?.validate()
  }
  
  const resetForm = () => {
    Object.assign(formData, {
      name: '',
      type: '',
      conditions: []
    })
  }
  
  const submitForm = async () => {
    loading.value = true
    try {
      await validateForm()
      // 提交逻辑
    } finally {
      loading.value = false
    }
  }
  
  return {
    formData,
    formRef,
    loading,
    validateForm,
    resetForm,
    submitForm
  }
}
```

### 3. **操作处理类** (`useActions*` / `useHandler*`)
**用途**：封装用户操作、事件处理、业务逻辑

**示例**：
```typescript
// useStrategyConditionHandler.ts
import { useMessage } from 'naive-ui'

export function useStrategyConditionHandler() {
  const message = useMessage()
  
  const handleAddCondition = (type: string) => {
    // 添加条件逻辑
    message.success('条件添加成功')
  }
  
  const handleDeleteCondition = (index: number) => {
    // 删除条件逻辑
    message.success('条件删除成功')
  }
  
  const handleUpdateCondition = (index: number, data: any) => {
    // 更新条件逻辑
    message.success('条件更新成功')
  }
  
  return {
    handleAddCondition,
    handleDeleteCondition,
    handleUpdateCondition
  }
}
```

### 4. **状态管理类** (`useState*`)
**用途**：管理组件或页面的复杂状态

**示例**：
```typescript
// useRatingStrategySidebar.ts
import { ref, computed } from 'vue'

export function useRatingStrategySidebar(modelId: number) {
  const levelProgressData = ref({
    regi: { total: 0, leveled: 0 },
    nonRegi: { total: 0, leveled: 0 }
  })
  
  const totalProgress = computed(() => {
    const total = levelProgressData.value.regi.total + levelProgressData.value.nonRegi.total
    const leveled = levelProgressData.value.regi.leveled + levelProgressData.value.nonRegi.leveled
    return total > 0 ? (leveled / total * 100).toFixed(2) : '0'
  })
  
  return {
    levelProgressData,
    totalProgress
  }
}
```

### 5. **工具函数类** (`useUtils*` / `use*Utils`)
**用途**：封装通用的工具函数和辅助逻辑

**示例**：
```typescript
// handleSorter.ts
import { PageRequest } from '@/models/common/baseRequest'

export function handleSorterChange(pageRequest: PageRequest, column: any) {
  if (column.columnKey) {
    if (!column.order) {
      pageRequest.ascOrDesc = ''
      pageRequest.orderBy = ''
    } else {
      pageRequest.ascOrDesc = column.order === 'ascend' ? 'asc' : 'desc'
      pageRequest.orderBy = column.columnKey
    }
    pageRequest.current = 1
  }
}
```

### 6. **选项配置类** (`useOptions*` / `options/*`)
**用途**：管理下拉选项、配置数据等

**示例**：
```typescript
// options/index.ts
import { ref, Ref } from 'vue'
import { SelectOption } from 'naive-ui'
import { getStockType } from '@/api/sec/secInfoApi'

export const fetchStockTypeOptions = (
  stockTypeOptions: Ref<SelectOption[]>,
  includeRegOrNonReg: boolean = false
) => {
  getStockType()
    .then(res => {
      if (res.code === 200) {
        const data = res.data.map(row => ({ label: row, value: row }))
        if (includeRegOrNonReg) {
          data.push(
            { label: '注册制', value: '注册制' },
            { label: '非注册制', value: '非注册制' }
          )
        }
        stockTypeOptions.value = data
      }
    })
    .catch(error => {
      console.log('获取股票类型失败:', error)
    })
}
```

---

## 🎯 命名规范

### **文件命名规则**

| 类型 | 命名模式 | 示例 | 说明 |
|------|----------|------|------|
| **数据管理** | `usePageData.ts` | `useUserData.ts` | 页面数据相关逻辑 |
| **表单处理** | `usePageForm.ts` | `useStrategyForm.ts` | 表单状态和验证 |
| **操作处理** | `usePageActions.ts` | `useConditionHandler.ts` | 用户操作和事件处理 |
| **状态管理** | `usePageState.ts` | `useSidebarState.ts` | 复杂状态管理 |
| **工具函数** | `usePageUtils.ts` | `handleSorter.ts` | 工具和辅助函数 |
| **选项配置** | `useOptions.ts` | `options/index.ts` | 配置和选项数据 |

### **函数命名规则**

| 前缀 | 用途 | 示例 |
|------|------|------|
| `use*` | 组合式函数 | `useUserData()` |
| `handle*` | 事件处理函数 | `handleSubmit()` |
| `fetch*` | 数据获取函数 | `fetchUserList()` |
| `validate*` | 验证函数 | `validateForm()` |
| `reset*` | 重置函数 | `resetForm()` |
| `toggle*` | 切换函数 | `toggleVisible()` |

---

## 📊 使用场景对照表

| 复用范围 | 存放位置 | 文件类型 | 示例 |
|----------|----------|----------|------|
| **全项目复用** | `src/composables/` | 通用逻辑 | `useAuth.ts`、`usePermission.ts` |
| **模块内复用** | `src/views/模块/composables/` | 模块特定逻辑 | `useStrategyData.ts` |
| **页面内复用** | `src/views/页面/composables/` | 页面特定逻辑 | `usePageForm.ts` |
| **组件内使用** | `src/views/页面/组件/composables/` | 组件特定逻辑 | `useComponentState.ts` |

---

## ✅ 最佳实践

### **推荐做法**
- ✅ 使用 `use` 前缀命名组合式函数
- ✅ 保持单一职责，一个文件专注一个功能领域
- ✅ 提供完整的 TypeScript 类型定义
- ✅ 返回对象解构，便于按需使用
- ✅ 添加详细的 JSDoc 注释
- ✅ 合理使用响应式 API（ref、reactive、computed）

### **避免做法**
- ❌ 在 composables 中直接操作 DOM
- ❌ 混合多种不相关的逻辑
- ❌ 过度抽象简单的逻辑
- ❌ 忽略错误处理和边界情况
- ❌ 缺少类型定义和文档说明

---

## 🔧 项目实际案例

### **全局 Composables 示例**
- `src/composables/options/index.ts` - 全局选项配置
- `src/composables/handleSorter.ts` - 通用排序处理

### **页面级 Composables 示例**
- `src/views/myDesk/ratingStrategyModels/ratingStartegy/StrategyConfigWorkspace/composables/`
  - `useStrategyForm.ts` - 策略表单管理
  - `useStrategyConditionHandler.ts` - 策略条件处理
  - `useStrategyConditionRestore.ts` - 策略条件数据回填

### **组件级 Composables 示例**
- `src/views/myDesk/ratingStrategyModels/ratingStartegy/composables/useRatingStrategySidebar.ts` - 侧边栏逻辑

---

## 📚 相关文档

- [Views 目录组件路径规范指南](./Views目录组件路径规范指南.md)
- [前端代码规范文档](../ai-rules/FrontEndRule.md)
- [Vue 3 Composition API 官方文档](https://vuejs.org/guide/reusability/composables.html)

---

**文档版本**: v1.0  
**更新日期**: 2025-01-16  
**适用项目**: tarkin-risk-management-platform
