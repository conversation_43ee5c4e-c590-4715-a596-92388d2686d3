# 按钮设计规范文档

## 目录
- [1. 概述](#1-概述)
- [2. 按钮样式规范](#2-按钮样式规范)
- [3. 按钮类型与颜色规范](#3-按钮类型与颜色规范)
- [4. 交互状态规范](#4-交互状态规范)
- [5. 尺寸规范](#5-尺寸规范)
- [6. 图标使用规范](#6-图标使用规范)
- [7. CSS代码示例](#7-css代码示例)
- [8. 使用指南](#8-使用指南)
- [9. 响应式设计](#9-响应式设计)

## 1. 概述

本文档基于 `src/views/fiveCategories/convRateModel/ModelCalculateConfig.vue` 文件中的按钮设计，制定了项目统一的按钮设计规范。旨在确保整个项目的视觉风格一致性和用户体验的统一性。

### 设计原则
- **一致性**：所有按钮遵循统一的视觉风格
- **可识别性**：不同功能的按钮有明确的视觉区分
- **可访问性**：确保良好的对比度和可读性
- **现代化**：采用渐变背景、圆角、阴影等现代设计元素

## 2. 按钮样式规范

### 2.1 基础样式特点
- **圆角设计**：8px 圆角，营造现代感
- **渐变背景**：135度线性渐变，增加视觉层次
- **阴影效果**：轻微阴影提升立体感
- **字体加粗**：font-weight: 500，确保文字清晰
- **白色文字**：统一使用白色文字，保证对比度

### 2.2 视觉层次
```css
/* 基础样式 */
border-radius: 8px;
font-weight: 500;
transition: all 0.3s ease;
box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
color: #ffffff !important;
```

### 2.3 悬停效果
```css
/* 悬停状态 */
box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
```

## 3. 按钮类型与颜色规范

### 3.1 Primary 按钮（主要操作）
- **用途**：主要操作、重要功能
- **背景色**：深蓝色渐变 `#007bff → #0056b3`
- **文字色**：白色 `#ffffff`
- **语义**：重要的主要操作

### 3.2 Success 按钮（成功操作）
- **用途**：确认操作、保存操作、成功状态
- **背景色**：深绿色渐变 `#28a745 → #1e7e34`
- **文字色**：白色 `#ffffff`
- **语义**：积极的、成功的操作

### 3.3 Warning 按钮（警告操作）
- **用途**：需要注意的操作、进行中状态
- **背景色**：深黄色渐变 `#ffc107 → #e0a800`
- **文字色**：白色 `#ffffff`
- **语义**：需要谨慎的操作或进行中状态

### 3.4 Info 按钮（信息操作）
- **用途**：查看信息、辅助操作
- **背景色**：深青色渐变 `#17a2b8 → #138496`
- **文字色**：白色 `#ffffff`
- **语义**：信息查看、辅助功能

### 3.5 Disabled 按钮（禁用状态）
- **用途**：不可操作的按钮
- **背景色**：深灰色渐变 `#6c757d → #5a6268`
- **文字色**：白色 `#ffffff`
- **透明度**：0.6
- **语义**：当前不可用的功能

### 3.6 Loading 按钮（加载状态）
- **用途**：操作进行中的状态
- **背景色**：深黄色渐变 `#ffc107 → #e0a800`
- **文字色**：白色 `#ffffff`
- **语义**：操作正在执行中

## 4. 交互状态规范

### 4.1 状态优先级
1. **Loading** > Disabled > Normal
2. 加载状态优先于禁用状态
3. 禁用状态优先于正常状态

### 4.2 状态切换规则
- **正常 → 加载**：背景色变为 Warning 色调
- **正常 → 禁用**：背景色变为灰色，降低透明度
- **加载 → 正常**：恢复原始背景色
- **禁用 → 正常**：恢复原始背景色和透明度

## 5. 尺寸规范

### 5.1 按钮尺寸等级
- **Large**：高度 40px，用于重要操作
- **Medium**：高度 36px，用于常规操作
- **Small**：高度 32px，用于次要操作

### 5.2 宽度规范
- **主要操作按钮**：200px（如查看结果、计算模型）
- **保存类按钮**：140px
- **功能标识按钮**：120px（如计算项按钮）
- **状态标识按钮**：100px（如日期类型按钮）

### 5.3 间距规范
- **按钮组间距**：12-16px
- **按钮内边距**：由 Naive UI 控制
- **最小点击区域**：44px × 44px（移动端友好）

## 6. 图标使用规范

### 6.1 图标位置
- **统一位置**：按钮文字左侧
- **实现方式**：使用 `<template #icon>` 插槽

### 6.2 图标大小
- **默认大小**：16px
- **大按钮**：可适当增大到 18px
- **小按钮**：保持 14px

### 6.3 图标选择原则
- **功能相关**：图标应直观表达按钮功能
- **风格统一**：使用同一图标库（推荐 @vicons/ionicons5）
- **语义明确**：避免使用含义模糊的图标

### 6.4 常用图标映射
- **查看/眼睛**：`Eye` - 查看信息
- **设置/齿轮**：`Settings` - 配置操作
- **计算器**：`Calculator` - 计算操作
- **保存/磁盘**：`Save` - 保存操作
- **日历**：`Calendar` - 日期相关
- **趋势图**：`TrendingUp` - 数据分析

## 7. CSS代码示例

### 7.1 基础按钮样式
```css
/* 按钮通用样式 */
:deep(.n-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 悬停效果 */
:deep(.n-button:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### 7.2 按钮类型样式
```css
/* Primary 按钮 */
:deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: #ffffff !important;
}

/* Success 按钮 */
:deep(.n-button--success-type) {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: #ffffff !important;
}

/* Warning 按钮 */
:deep(.n-button--warning-type) {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #ffffff !important;
}

/* Info 按钮 */
:deep(.n-button--info-type) {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: #ffffff !important;
}

/* Disabled 按钮 */
:deep(.n-button--disabled) {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: #ffffff !important;
  opacity: 0.6;
}

/* Loading 按钮 */
:deep(.n-button--loading) {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #ffffff !important;
}
```

### 7.3 Vue 组件使用示例
```vue
<template>
  <!-- 主要操作按钮 -->
  <n-button 
    type="primary" 
    size="large"
    style="width: 200px; height: 40px;"
    @click="handlePrimaryAction"
  >
    <template #icon>
      <n-icon :component="Eye" />
    </template>
    查看结果
  </n-button>

  <!-- 保存按钮 -->
  <n-button 
    :loading="saveLoading"
    :type="saveLoading ? 'warning' : 'success'"
    size="large"
    style="width: 140px; height: 36px;"
    @click="handleSave"
  >
    <template #icon>
      <n-icon :component="Save" />
    </template>
    {{ saveLoading ? '保存中' : '保存配置' }}
  </n-button>
</template>
```

## 8. 使用指南

### 8.1 按钮选择指南

| 场景 | 推荐类型 | 示例 |
|------|----------|------|
| 主要操作 | Primary | 提交表单、确认操作 |
| 保存操作 | Success | 保存配置、确认修改 |
| 查看信息 | Info | 查看详情、查看结果 |
| 需要注意的操作 | Warning | 删除操作、重置操作 |
| 进行中状态 | Warning + Loading | 计算中、保存中 |
| 不可用功能 | Disabled | 条件不满足的操作 |

### 8.2 按钮组合最佳实践

#### 8.2.1 主次关系
- **主要操作**：使用 Primary 或 Success
- **次要操作**：使用 Info 或 Warning
- **危险操作**：使用 Warning 并添加确认

#### 8.2.2 布局原则
- **重要操作在右侧**：符合用户阅读习惯
- **相关操作分组**：使用 `n-space` 组织
- **合理间距**：12-16px 的按钮间距

#### 8.2.3 状态管理
```vue
<!-- 动态状态示例 -->
<n-button
  :disabled="isCalculating"
  :type="isCalculating ? 'warning' : 'primary'"
  :loading="isCalculating"
>
  <template #icon>
    <n-icon :component="Calculator" />
  </template>
  {{ isCalculating ? '计算中' : '开始计算' }}
</n-button>
```


**注意事项：**
1. 本规范基于 Naive UI 组件库制定
2. 所有样式使用 `:deep()` 选择器确保样式穿透
3. 颜色值可根据项目主题色进行微调
4. 建议在项目中创建按钮组件库，统一管理样式
