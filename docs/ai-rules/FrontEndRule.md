# 🚀 塔金风险管理平台 - 前端代码规范文档

## 📋 项目概述

### 项目信息
- **项目名称**: tarkin-risk-management-platform（塔金风险管理平台）
- **版本**: v1.9.0
- **功能定位**: 专为证券公司融资融券部门设计的智能风险管理系统
- **业务目的**: 为业务人员提供日常高频场景下的风险控制和管理工具
- **目标用户**: 证券公司融资融券部门业务人员、风险管理人员

### 主要技术栈
- **前端框架**: Vue 3.5.16 + Composition API
- **开发语言**: TypeScript 5.8.3
- **UI组件库**: Naive UI 2.38.2
- **状态管理**: Pinia 2.3.1
- **路由管理**: Vue Router 4.5.1
- **构建工具**: Vite 4.5.0
- **样式方案**: Tailwind CSS 3.3.3 + Less 4.2.0 + Sass 1.89.2
- **图表库**: ECharts 5.6.0 + ECharts GL 2.0.9
- **网络请求**: Axios 1.8.2
- **工具库**: Lodash-es 4.17.21, VueUse 13.3.0
- **代码规范**: ESLint 8.57.0 + Prettier 2.8.8 + Stylelint 14.16.1

## ⚠️ 重要：代码风格配置优先原则

### 🚨 **永远先检查项目配置，再进行代码修改**

在进行任何代码风格修改之前，**必须**先检查项目的配置文件，而不是按照个人习惯或假设进行修改。

#### **关键配置文件检查顺序**
1. **`prettier.config.js`** - Prettier 代码格式化配置
2. **`.eslintrc.js`** - ESLint 代码质量和风格检查配置
3. **`tsconfig.json`** - TypeScript 编译配置
4. **`.editorconfig`** - 编辑器配置
5. **`package.json`** - 项目依赖和脚本配置

#### **Prettier 配置示例分析**
```javascript
// prettier.config.js
module.exports = {
  semi: true,           // ✅ 使用分号 - 不要盲目移除！
  singleQuote: true,    // ✅ 使用单引号
  tabWidth: 2,          // ✅ 缩进2个空格
  printWidth: 100,      // ✅ 行宽100字符
  trailingComma: 'es5', // ✅ ES5兼容的尾随逗号
}
```

#### **❌ 错误示例：盲目修改代码风格**
```typescript
// ❌ 错误：未检查配置就移除分号
const variable = value  // 违反了 semi: true 配置

// ❌ 错误：使用双引号
const message = "Hello World"  // 违反了 singleQuote: true 配置
```

#### **✅ 正确示例：遵循项目配置**
```typescript
// ✅ 正确：遵循 semi: true 配置
const variable = value;

// ✅ 正确：遵循 singleQuote: true 配置
const message = 'Hello World';
```

#### **🎯 最佳实践**
1. **修改前必查配置** - 任何代码风格修改前，先查看 `prettier.config.js`
2. **使用自动格式化** - 依赖 Prettier 自动格式化，而不是手动调整
3. **团队配置统一** - 所有团队成员使用相同的编辑器配置
4. **配置文件优先级** - 理解配置文件的优先级关系

#### **🚨 经验教训**
> **真实案例**: 在 ServiceManage.vue 重构过程中，曾错误地移除了所有分号，但项目配置 `prettier.config.js` 中明确设置了 `semi: true`。这导致了代码风格不一致的问题。
>
> **教训**: 永远不要假设项目的代码风格，必须先检查配置文件！

## 🎯 技术规范

### Vue 3 Composition API 规范

#### 1. `<script setup>` 语法规范
```vue
<script setup lang="ts">
// ✅ 推荐的代码结构顺序
// 1. 导入语句（按类型分组）
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import type { FormInst } from 'naive-ui'
import { someApi } from '@/api/module/someApi'
import type { SomeInterface } from '@/models/module/types'

// 2. 类型声明
interface Props {
  title: string
  data?: SomeInterface[]
}

interface Emits {
  (e: 'update', value: string): void
  (e: 'close', reason: string): void
}

// 3. Props 和 Emits 定义
const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

const emit = defineEmits<Emits>()

// 4. 组件注册和依赖注入
const router = useRouter()
const message = useMessage()

// 5. 响应式状态
const loading = ref(false)
const formData = reactive<SomeInterface>({
  // 初始化数据
})

// 6. 计算属性
const computedValue = computed(() => {
  return props.data.length > 0
})

// 7. 监听器
watch(() => props.data, (newVal) => {
  // 处理逻辑
}, { immediate: true })

// 8. 生命周期钩子
onMounted(() => {
  initializeData()
})

// 9. 业务方法
const initializeData = async () => {
  try {
    loading.value = true
    // 业务逻辑
  } catch (error) {
    message.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 10. 导出（如需要）
defineExpose({
  initializeData
})
</script>
```

#### 2. TypeScript 强制使用规范

##### 🔒 **强制类型定义要求**

```typescript
// ✅ 所有变量必须有明确类型定义
const userName: string = 'admin'
const userAge: number = 25
const isActive: boolean = true
const userList: UserInfo[] = []

// ✅ 函数参数和返回值必须有类型定义
function getUserInfo(userId: number): Promise<UserInfo> {
  return api.getUserById(userId)
}

// ✅ 对象属性必须有类型定义
const userConfig: {
  theme: string
  language: string
  notifications: boolean
} = {
  theme: 'dark',
  language: 'zh-CN',
  notifications: true
}

// ❌ 禁止使用 any 类型
const userData: any = {}           // 错误
const result: any = api.getData()  // 错误

// ✅ 特殊情况下的 any 使用（需要注释说明）
const legacyData: any = window.legacySystem // 兼容旧系统，待重构
```

##### 📝 **TSDoc 注释规范**

```typescript
/**
 * 用户信息接口
 * @interface UserInfo
 * @description 定义用户基本信息的数据结构
 */
export interface UserInfo {
  /**
   * 用户ID
   * @type {number}
   * @description 用户的唯一标识符，由系统自动生成
   */
  id: number

  /**
   * 用户名称
   * @type {string}
   * @description 用户的显示名称，长度限制2-20个字符
   */
  name: string

  /**
   * 用户角色
   * @type {UserRole}
   * @description 用户在系统中的角色类型
   */
  role: UserRole

  /**
   * 创建时间
   * @type {string}
   * @description 用户账户创建时间，ISO 8601 格式
   */
  createTime: string

  /**
   * 可选属性：最后登录时间
   * @type {string | null}
   * @description 用户最后一次登录的时间，未登录过则为 null
   */
  lastLoginTime?: string | null
}
```

##### 🏷️ **类型定义标准模板**

```typescript
// ✅ 接口定义 - 存放在 src/models 目录，具体放在哪个文件中需要结合业务逻辑
export interface UserInfo {
  /** 用户ID */
  id: number
  /** 用户名称 */
  name: string
  /** 用户角色 */
  role: UserRole
  /** 创建时间 */
  createTime: string
}

// ✅ 枚举定义 - 存放在 src/enums 目录，具体放在哪个文件中需要结合业务逻辑
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// ✅ 联合类型定义
export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended'


// ✅ 工厂函数 - 用于初始化复杂类型
export const createUserInfo = (): UserInfo => ({
  id: 0,
  name: '',
  role: UserRole.USER,
  createTime: ''
})

// ✅ 类型守卫函数
export function isUserInfo(obj: unknown): obj is UserInfo {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as UserInfo).id === 'number' &&
    typeof (obj as UserInfo).name === 'string'
  )
}
```

##### ⚠️ **禁止使用的类型定义**

```typescript
// ❌ 禁止使用 any
const data: any = {}

// ❌ 禁止使用 object
const config: object = {}

// ❌ 禁止使用 Function
const callback: Function = () => {}

// ❌ 禁止使用空接口
interface EmptyInterface {}

// ✅ 正确的替代方案
const data: Record<string, unknown> = {}
const config: { [key: string]: string | number | boolean } = {}
const callback: () => void = () => {}
const callback2: (param: string) => number = (param) => param.length
```

### 组件设计模式

#### 1. 组件通信规范
```vue
<!-- ✅ 父组件 -->
<template>
  <ChildComponent 
    v-model:formData="formData"
    @update="handleUpdate"
    @close="handleClose"
  />
</template>

<script setup lang="ts">
// 使用 emit 进行事件通信
const handleUpdate = (value: string) => {
  // 处理更新逻辑
}

const handleClose = (reason: string) => {
  // 明确区分关闭原因
  if (reason === 'save') {
    // 保存后关闭
  } else if (reason === 'cancel') {
    // 取消关闭
  }
}
</script>
```

```vue
<!-- ✅ 子组件 -->
<script setup lang="ts">
interface Props {
  formData: FormData
}

interface Emits {
  (e: 'update:formData', value: FormData): void
  (e: 'update', value: string): void
  (e: 'close', reason: 'save' | 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 双向绑定处理
const localFormData = computed({
  get: () => props.formData,
  set: (value) => emit('update:formData', value)
})
</script>
```

#### 2. 权限控制规范
```vue
<template>
  <!-- ✅ 权限控制示例 -->
  <n-button 
    v-if="!ifHasEditPermission"
    @click="handleEdit"
  >
    编辑
  </n-button>
  
  <n-button 
    v-if="!ifModifyPermission"
    @click="handleModify"
  >
    修改
  </n-button>
</template>

<script setup lang="ts">
// 权限字段：1 = 无权限，0 = 有权限
const ifHasEditPermission = ref(0)
const ifModifyPermission = ref(0)
</script>
```

## 🎨 UI/UX 设计原则

### UI 交互组件图标规范

#### 1. 强制要求
**所有用户交互组件（按钮、链接、表单控件、菜单项等）必须配置相应的图标**

#### 2. 图标类型优先级
```vue
<!-- ✅ 图标选择优先级 -->
<template>
  <!-- 1. 优先使用项目中其他组件的一致图标 -->
  <n-button type="primary">
    <template #icon>
      <PlusIcon />  <!-- 与项目其他新增按钮保持一致 -->
    </template>
    新增
  </n-button>

  <!-- 2. 优先使用 Naive UI 内置图标 -->
  <n-button type="error">
    <template #icon>
      <DeleteIcon />  <!-- Naive UI 内置图标 -->
    </template>
    删除
  </n-button>

  <!-- 4. 自定义 SVG 图标（需符合设计规范） -->
  <n-button>
    <template #icon>
      <CustomSvgIcon />  <!-- 自定义图标，需要设计团队审核 -->
    </template>
    特殊操作
  </n-button>
</template>
```

#### 3. 应用场景规范

##### **按钮组件图标规范**
```vue
<template>
  <!-- ✅ 操作按钮语义化图标 -->
  <n-space>
    <!-- 新增操作 -->
    <n-button type="primary">
      <template #icon>
        <PlusIcon />
      </template>
      新增
    </n-button>

    <!-- 删除操作 -->
    <n-button type="error">
      <template #icon>
        <TrashIcon />
      </template>
      删除
    </n-button>

    <!-- 编辑操作 -->
    <n-button type="info">
      <template #icon>
        <EditIcon />
      </template>
      编辑
    </n-button>

    <!-- 查询操作 -->
    <n-button type="primary">
      <template #icon>
        <SearchIcon />
      </template>
      查询
    </n-button>

    <!-- 重置操作 -->
    <n-button type="warning">
      <template #icon>
        <RefreshIcon />
      </template>
      重置
    </n-button>

    <!-- 导出操作 -->
    <n-button>
      <template #icon>
        <DownloadIcon />
      </template>
      导出
    </n-button>

    <!-- 导入操作 -->
    <n-button>
      <template #icon>
        <UploadIcon />
      </template>
      导入
    </n-button>

    <!-- 保存操作 -->
    <n-button type="success">
      <template #icon>
        <SaveIcon />
      </template>
      保存
    </n-button>
  </n-space>
</template>
```

##### **表单控件图标规范**
```vue
<template>
  <!-- ✅ 输入框前缀/后缀图标 -->
  <n-form>
    <!-- 用户名输入框 -->
    <n-form-item label="用户名">
      <n-input v-model:value="username" placeholder="请输入用户名">
        <template #prefix>
          <UserIcon />
        </template>
      </n-input>
    </n-form-item>

    <!-- 密码输入框 -->
    <n-form-item label="密码">
      <n-input
        v-model:value="password"
        type="password"
        placeholder="请输入密码"
        show-password-on="click"
      >
        <template #prefix>
          <LockIcon />
        </template>
      </n-input>
    </n-form-item>

    <!-- 邮箱输入框 -->
    <n-form-item label="邮箱">
      <n-input v-model:value="email" placeholder="请输入邮箱">
        <template #prefix>
          <MailIcon />
        </template>
      </n-input>
    </n-form-item>

    <!-- 日期选择器 -->
    <n-form-item label="日期">
      <n-date-picker v-model:value="date" placeholder="请选择日期">
        <template #prefix>
          <CalendarIcon />
        </template>
      </n-date-picker>
    </n-form-item>

    <!-- 下拉选择器 -->
    <n-form-item label="状态">
      <n-select
        v-model:value="status"
        :options="statusOptions"
        placeholder="请选择状态"
      >
        <template #prefix>
          <StatusIcon />
        </template>
      </n-select>
    </n-form-item>
  </n-form>
</template>
```

##### **状态指示图标规范**
```vue
<template>
  <!-- ✅ 状态指示图标 -->
  <n-space vertical>
    <!-- 成功状态 -->
    <n-alert type="success">
      <template #icon>
        <CheckCircleIcon />
      </template>
      操作成功完成
    </n-alert>

    <!-- 警告状态 -->
    <n-alert type="warning">
      <template #icon>
        <WarningIcon />
      </template>
      请注意相关风险
    </n-alert>

    <!-- 错误状态 -->
    <n-alert type="error">
      <template #icon>
        <ErrorIcon />
      </template>
      操作失败，请重试
    </n-alert>

    <!-- 信息状态 -->
    <n-alert type="info">
      <template #icon>
        <InfoIcon />
      </template>
      系统维护通知
    </n-alert>
  </n-space>

  <!-- ✅ 表格状态列图标 -->
  <n-data-table :columns="columns" :data="data" />
</template>

<script setup lang="ts">
// 表格列配置示例
const columns = [
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusConfig = {
        'active': { icon: CheckCircleIcon, color: 'success', text: '激活' },
        'inactive': { icon: StopIcon, color: 'error', text: '停用' },
        'pending': { icon: ClockIcon, color: 'warning', text: '待审核' }
      }

      const config = statusConfig[row.status]
      return h(NTag, { type: config.color }, {
        icon: () => h(config.icon),
        default: () => config.text
      })
    }
  }
]
</script>
```

#### 4. 标准图标映射表

| 操作类型 | 推荐图标 | Naive UI | Iconify | 说明 |
|----------|----------|----------|---------|------|
| **新增** | `PlusIcon` | `AddIcon` | `mdi:plus` | 添加、创建、新建 |
| **删除** | `TrashIcon` | `DeleteIcon` | `mdi:delete` | 删除、移除 |
| **编辑** | `EditIcon` | `EditIcon` | `mdi:pencil` | 编辑、修改 |
| **查询** | `SearchIcon` | `SearchIcon` | `mdi:magnify` | 搜索、查找 |
| **重置** | `RefreshIcon` | `RefreshIcon` | `mdi:refresh` | 重置、刷新 |
| **保存** | `SaveIcon` | `SaveIcon` | `mdi:content-save` | 保存、确认 |
| **取消** | `CloseIcon` | `CloseIcon` | `mdi:close` | 取消、关闭 |
| **导出** | `DownloadIcon` | `DownloadIcon` | `mdi:download` | 导出、下载 |
| **导入** | `UploadIcon` | `UploadIcon` | `mdi:upload` | 导入、上传 |
| **设置** | `SettingsIcon` | `SettingsIcon` | `mdi:cog` | 配置、设置 |
| **用户** | `UserIcon` | `PersonIcon` | `mdi:account` | 用户、个人 |
| **密码** | `LockIcon` | `LockIcon` | `mdi:lock` | 密码、安全 |
| **邮箱** | `MailIcon` | `MailIcon` | `mdi:email` | 邮件、通知 |
| **日期** | `CalendarIcon` | `CalendarIcon` | `mdi:calendar` | 日期、时间 |
| **成功** | `CheckCircleIcon` | `CheckmarkIcon` | `mdi:check-circle` | 成功、完成 |
| **警告** | `WarningIcon` | `WarningIcon` | `mdi:alert` | 警告、注意 |
| **错误** | `ErrorIcon` | `CloseCircleIcon` | `mdi:alert-circle` | 错误、失败 |
| **信息** | `InfoIcon` | `InformationIcon` | `mdi:information` | 信息、提示 |

#### 5. 实施目标
- **提升页面视觉层次感**：通过图标增强界面的视觉引导
- **增强用户体验**：图标提供直观的操作提示，减少用户认知负担
- **提高界面可读性**：图标与文字结合，提升信息传达效率
- **增强专业度**：统一的图标规范体现系统的专业性和一致性

#### 6. 例外情况
```vue
<!-- ✅ 允许的例外情况 -->
<template>
  <!-- 纯文本链接（导航类） -->
  <n-breadcrumb>
    <n-breadcrumb-item>首页</n-breadcrumb-item>
    <n-breadcrumb-item>系统管理</n-breadcrumb-item>
    <n-breadcrumb-item>用户管理</n-breadcrumb-item>
  </n-breadcrumb>

  <!-- 特殊设计需求（需要设计团队确认） -->
  <n-button text type="primary" class="minimal-design">
    查看详情
  </n-button>

  <!-- 空间极度受限的场景 -->
  <n-button size="tiny" quaternary>
    确定
  </n-button>
</template>

<!-- ❌ 不允许的情况 -->
<template>
  <!-- 主要操作按钮缺少图标 -->
  <n-button type="primary">新增用户</n-button>  <!-- 错误：缺少图标 -->

  <!-- 表单输入框缺少语义图标 -->
  <n-input placeholder="请输入用户名" />  <!-- 错误：缺少用户图标 -->
</template>
```

#### 7. 图标一致性检查清单
- [ ] **操作按钮**：所有主要操作按钮都配置了语义化图标
- [ ] **表单控件**：输入框、选择器等配置了前缀图标
- [ ] **状态指示**：成功、警告、错误状态都有对应图标
- [ ] **图标统一性**：同类操作在不同页面使用相同图标
- [ ] **图标尺寸**：图标大小与组件尺寸协调一致
- [ ] **图标颜色**：图标颜色与组件主题色彩搭配合理
- [ ] **加载状态**：异步操作有加载图标或动画
- [ ] **响应式适配**：图标在不同屏幕尺寸下显示正常

## 🚀 自动导入规范

### 自动导入机制说明

项目使用 `unplugin-auto-import` 和 `unplugin-vue-components` 插件实现自动导入，减少手动导入语句，提升开发效率。

#### 1. Vue API 自动导入

**✅ 无需手动导入的 Vue API**
```typescript
// ❌ 不需要手动导入
// import { ref, computed, onMounted, watch, reactive } from 'vue';
// import { useRoute, useRouter } from 'vue-router';

// ✅ 直接使用
<script setup lang="ts">
  const count = ref<number>(0);
  const doubleCount = computed(() => count.value * 2);
  const route = useRoute();
  const router = useRouter();

  onMounted(() => {
    console.log('组件已挂载');
  });

  watch(count, (newVal) => {
    console.log('count 变化:', newVal);
  });
</script>
```

**自动导入的 Vue API 列表**：
- **响应式 API**: `ref`, `reactive`, `computed`, `readonly`, `shallowRef`, `shallowReactive`, `toRef`, `toRefs`, `unref`, `isRef`, `isReactive`
- **生命周期钩子**: `onMounted`, `onUnmounted`, `onBeforeMount`, `onBeforeUnmount`, `onActivated`, `onDeactivated`, `onUpdated`, `onBeforeUpdate`
- **工具函数**: `nextTick`, `h`, `markRaw`, `toRaw`, `getCurrentInstance`, `inject`, `provide`
- **监听器**: `watch`, `watchEffect`, `watchPostEffect`, `watchSyncEffect`
- **路由 API**: `useRoute`, `useRouter`, `onBeforeRouteLeave`, `onBeforeRouteUpdate`

#### 2. 组件自动导入

**✅ 无需手动导入的组件**

##### **Naive UI 组件**
```vue
<template>
  <!-- ❌ 不需要导入 Naive UI 组件 -->
  <!-- import { NButton, NInput, NCard } from 'naive-ui'; -->

  <!-- ✅ 直接使用 -->
  <n-card title="用户信息">
    <n-form>
      <n-form-item label="用户名">
        <n-input v-model:value="username" placeholder="请输入用户名" />
      </n-form-item>
      <n-form-item>
        <n-button type="primary" @click="handleSubmit">
          提交
        </n-button>
      </n-form-item>
    </n-form>
  </n-card>
</template>
```

##### **项目自定义组件（src/components 目录下）**
```vue
<template>
  <!-- ❌ 不需要手动导入 -->
  <!-- import SecurityInfoSearch from '@/components/SecuritySearch/SecurityInfoSearch.vue'; -->
  <!-- import SecPeerDataTable from '@/components/Table/peer/SecPeerDataTable.vue'; -->

  <!-- ✅ 直接使用 -->
  <div>
    <SecurityInfoSearch @get-value="handleSecuritySelect" />
    <SecPeerDataTable :sec-code="secCode" :sec-name="secName" />
    <CommonStockTable :data="stockData" />
    <BasicModal v-model:show="showModal" title="详情">
      <BasicForm :form-config="formConfig" />
    </BasicModal>
  </div>
</template>
```

**自动导入的组件类别**：
- **业务组件**: `SecurityInfoSearch`, `SecPeerDataTable`, `CommonStockTable`, `IndustryCascader`, `LevelSelect`
- **表单组件**: `BasicForm`, `BasicModal`, `BasicUpload`, `PasswordChangeForm`
- **图表组件**: `BarGraph`, `LineGraph`, `EchartsPie`, `FinancialBarChart`
- **表格组件**: `Table`, `MatrixCellInfoTable`, `WarningListTable`
- **搜索组件**: `BusinessSearch`, `CustSearch`, `UserNameSearch`, `RoleNameSearch`

#### 3. 常量自动导入

**✅ 无需手动导入的常量（src/constants 目录下）**
```typescript
// ❌ 不需要手动导入
// import { STORAGE_KEYS } from '@/constants/base/storageKeys';
// import { LOG_CONSTANTS } from '@/constants/index';

// ✅ 直接使用
<script setup lang="ts">
  const saveUserInfo = (userInfo: UserInfo) => {
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
    console.log(LOG_CONSTANTS.USER_LOGIN);
  };

  const handleServiceOperation = (operation: string) => {
    if (isDangerousOperation(operation)) {
      // 危险操作处理
    }
    const statusText = getStatusText(operation);
    const statusType = getStatusType(operation);
  };
</script>
```

**自动导入的常量类别**：
- **存储键**: `STORAGE_KEYS`
- **日志常量**: `LOG_CONSTANTS`, `LOG_LEVEL_ERROR`, `LOG_LEVEL_INFO`
- **服务管理**: `SERVICE_STATUS_TEXT_MAP`, `CLIENT_STATUS_TYPE_MAP`, `MQ_OPERATION_TEXT_MAP`
- **业务常量**: `CONDITION_IDENTIFIERS`, `FINANCE_PERIOD_OPTIONS`, `GROWTH_RATE_TYPE_OPTIONS`
- **工具函数**: `formatDateTime`, `formatDuration`, `getStatusText`, `isDangerousOperation`

### 代码生成规则

#### 1. 导入语句编写规则

**✅ 需要手动导入的情况**
```typescript
<script setup lang="ts">
  // ✅ 类型导入（必须手动导入）
  import type { Ref, ComputedRef } from 'vue';
  import type { DataTableColumns, MessageApi } from 'naive-ui';
  import type { PageRequest } from '@/models/common/baseRequest';
  import type { UserInfo } from '@/models/user/userModels';

  // ✅ 第三方库导入（必须手动导入）
  import { format } from 'date-fns';
  import { debounce } from 'lodash-es';
  import axios from 'axios';

  // ✅ 工具函数导入（必须手动导入）
  import { openStockArchives } from '@/utils/goToArchives';
  import { setLevelColor } from '@/utils/ui/color/LevelColor';
  import { renderIcon } from '@/utils';

  // ✅ API 导入（必须手动导入）
  import { getUserInfo, updateUserInfo } from '@/api/user/userApi';
  import { queryStockData } from '@/api/stock/stockApi';

  // ✅ 枚举导入（必须手动导入）
  import { UserStatus, UserRole } from '@/enums/userEnum';
  import { CommonStatus } from '@/enums/baseEnum';

  // ✅ 非 src/components 目录的组件（必须手动导入）
  import SomeExternalComponent from 'external-library/component';
  import CustomComponent from '../../../other/path/CustomComponent.vue';
</script>
```

**❌ 不需要手动导入的情况**
```typescript
<script setup lang="ts">
  // ❌ Vue API（自动导入）
  // import { ref, computed, onMounted, watch } from 'vue';

  // ❌ Vue Router API（自动导入）
  // import { useRoute, useRouter } from 'vue-router';

  // ❌ Naive UI 组件（自动导入）
  // import { NButton, NInput, NCard } from 'naive-ui';

  // ❌ src/components 目录下的组件（自动导入）
  // import SecurityInfoSearch from '@/components/SecuritySearch/SecurityInfoSearch.vue';

  // ❌ src/constants 目录下的常量（自动导入）
  // import { STORAGE_KEYS } from '@/constants/base/storageKeys';
</script>
```

#### 2. 组件使用规则

**✅ 标准组件使用模式**
```vue
<template>
  <!-- 自动导入的组件直接使用 -->
  <n-card title="数据查询">
    <SecurityInfoSearch
      :sec-type="SecType.STOCK"
      @get-value="handleSecuritySelect"
    />

    <n-data-table
      :columns="columns"
      :data="tableData"
      :loading="loading"
    />

    <SecPeerDataTable
      :sec-code="selectedSecCode"
      :sec-name="selectedSecName"
    />
  </n-card>
</template>

<script setup lang="ts">
  // 只导入必要的类型和工具
  import type { DataTableColumns } from 'naive-ui';
  import { SecType } from '@/enums/secEnum';

  // 直接使用自动导入的 API
  const loading = ref<boolean>(false);
  const tableData = ref([]);
  const selectedSecCode = ref<string>('');

  const handleSecuritySelect = (secCode: string) => {
    selectedSecCode.value = secCode;
  };
</script>
```

#### 3. 自动导入检查清单

**开发时检查项**
- [ ] **Vue API**: 确认 `ref`, `computed`, `onMounted` 等无需导入
- [ ] **路由 API**: 确认 `useRoute`, `useRouter` 无需导入
- [ ] **Naive UI 组件**: 确认所有 `N*` 组件无需导入
- [ ] **项目组件**: 确认 `src/components` 下的组件无需导入
- [ ] **常量**: 确认 `src/constants` 下的常量无需导入
- [ ] **类型导入**: 确认使用 `import type` 语法
- [ ] **第三方库**: 确认第三方库需要手动导入
- [ ] **工具函数**: 确认 `src/utils` 下的函数需要手动导入

**代码审查检查项**
- [ ] **无冗余导入**: 检查是否有不必要的 Vue API 导入
- [ ] **类型导入正确**: 检查类型导入是否使用 `import type`
- [ ] **组件导入一致**: 检查组件导入是否符合自动导入规则
- [ ] **常量使用正确**: 检查常量是否正确使用自动导入
- [ ] **IDE 提示正常**: 确认自动导入的 API 有正确的类型提示

#### 4. 故障排除指南

**常见问题及解决方案**

##### **问题1: 自动导入的 API 没有类型提示**
```typescript
// 解决方案：检查 auto-imports.d.ts 文件是否存在
// 如果不存在，运行以下命令重新生成
// npm run dev 或 yarn dev
```

##### **问题2: 组件无法自动导入**
```typescript
// 解决方案：确认组件位置是否正确
// 1. 组件必须在 src/components 目录下
// 2. 组件文件名必须是 .vue 扩展名
// 3. 检查 components.d.ts 文件中是否包含该组件
```

##### **问题3: 常量无法自动导入**
```typescript
// 解决方案：确认常量导出方式
// 1. 常量必须在 src/constants 目录下
// 2. 必须使用 export const 导出
// 3. 不能在 index.ts 文件中导出（被排除）

// ✅ 正确的常量导出
export const STORAGE_KEYS = {
  USER_INFO: 'user_info',
  TOKEN: 'access_token'
};

// ❌ 错误的导出方式（在 index.ts 中）
// export * from './storageKeys';
```

##### **问题4: ESLint 报告未定义变量错误**
```typescript
// 解决方案：确认 .eslintrc-auto-import.json 文件
// 1. 检查文件是否存在
// 2. 检查 .eslintrc.js 中是否正确引用
// 3. 重启 IDE 或 ESLint 服务
```

### 企业级金融系统界面标准

#### 1. 字体和尺寸规范
```css
/* ✅ 字体规范 */
.title-large {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
}

.title-medium {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.text-content {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
}

/* ✅ 控件尺寸规范 */
.control-standard {
  height: 48px;
  min-height: 40px;
  padding: 0 16px;
  font-size: 16px;
  font-weight: 500;
}

.control-large {
  height: 52px;
  padding: 0 20px;
  font-size: 18px;
  font-weight: bold;
}
```

#### 2. 布局规范
```vue
<template>
  <!-- ✅ 单行水平布局 - 过滤组件 -->
  <div class="filter-container">
    <n-space :size="24" align="center">
      <n-select 
        v-model:value="filterValue"
        :style="{ width: '280px', height: '48px' }"
        placeholder="请选择"
      />
      <n-date-picker 
        v-model:value="dateRange"
        type="daterange"
        :style="{ width: '320px', height: '48px' }"
      />
      <n-button 
        type="primary"
        :style="{ height: '48px', padding: '0 24px' }"
        @click="handleSearch"
      >
        <template #icon>
          <SearchIcon />
        </template>
        查询
      </n-button>
    </n-space>
  </div>
</template>

<style scoped>
.filter-container {
  padding: 16px 24px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
}
</style>
```

## 📁 目录结构说明

### 核心目录规范
```
src/
├── api/                    # API接口定义
│   ├── system/            # 系统管理相关API
│   ├── business/          # 业务相关API
│   └── common/            # 通用API
├── models/                # TypeScript类型定义
│   ├── common/            # 通用类型定义
│   │   ├── baseRequest.ts     # 基础请求类型
│   │   ├── baseResponse.ts    # 基础响应类型
│   │   ├── basic/             # 基础业务类型
│   │   │   ├── userModels.ts      # 用户相关类型
│   │   │   ├── roleModels.ts      # 角色相关类型
│   │   │   └── commonModels.ts    # 通用业务类型
│   │   ├── chart.ts           # 图表相关类型
│   │   └── utilModels.ts      # 工具类型
│   ├── systemManage/      # 系统管理类型
│   │   ├── userConfigurationModels.ts  # 用户配置
│   │   ├── menuManageModels.ts         # 菜单管理
│   │   ├── logModels.ts                # 日志管理
│   │   └── sysManageModels.ts          # 系统管理
│   ├── marginTrading/     # 融资融券类型
│   │   ├── collateralModels.ts         # 担保品类型
│   │   ├── mrgTrdBusinessModel.ts      # 融资融券业务
│   │   └── underlying/                 # 标的证券类型
│   ├── sec/               # 证券相关类型
│   │   ├── secInfoModels.ts            # 证券信息
│   │   ├── secProfileModels.ts         # 证券档案
│   │   └── themeRiskModels.ts          # 主题风险
│   ├── financial/         # 财务相关类型
│   ├── stressTest/        # 压力测试类型
│   ├── marketData/        # 市场数据类型
│   ├── backTest/          # 回测相关类型
│   └── peerData/          # 同业数据类型
├── components/            # 可复用组件
│   ├── business/          # 业务组件
│   ├── common/            # 通用组件
│   └── ui/                # UI组件
├── views/                 # 页面组件
│   ├── dashboard/         # 仪表板
│   ├── business/          # 业务页面
│   └── system/            # 系统管理页面
├── utils/                 # 工具函数
│   ├── common/            # 通用工具
│   ├── business/          # 业务工具
│   └── http/              # 网络请求工具
├── constants/             # 常量定义
│   ├── base/              # 基础常量
│   ├── business/          # 业务常量
│   └── api/               # API常量
├── enums/                 # 枚举定义
├── hooks/                 # 组合式函数
├── store/                 # 状态管理
├── router/                # 路由配置
└── styles/                # 样式文件
```

### TypeScript 类型定义文件组织规范

#### 1. 类型定义文件归属规则

| 业务领域 | 目录位置 | 文件命名规范 | 适用场景 |
|----------|----------|--------------|----------|
| **通用基础** | `src/models/common/` | `baseRequest.ts`, `baseResponse.ts` | HTTP请求响应、分页、通用接口 |
| **基础业务** | `src/models/common/basic/` | `userModels.ts`, `roleModels.ts` | 用户、角色、权限等基础业务 |
| **系统管理** | `src/models/systemManage/` | `[功能]Models.ts` | 系统配置、日志、菜单管理 |
| **融资融券** | `src/models/marginTrading/` | `[业务]Models.ts` | 担保品、标的证券、风险控制 |
| **证券管理** | `src/models/sec/` | `[证券类型]Models.ts` | 证券信息、档案、评级 |
| **财务管理** | `src/models/financial/` | `financialModels.ts` | 财务数据、报表、分析 |
| **压力测试** | `src/models/stressTest/` | `[测试类型]Models.ts` | 压力测试相关数据结构 |
| **市场数据** | `src/models/marketData/` | `[数据源]Models.ts` | 外部市场数据接口 |
| **回测分析** | `src/models/backTest/` | `backTestingModels.ts` | 回测报告、分析结果 |
| **同业数据** | `src/models/peerData/` | `[同业类型]Models.ts` | 同业对比、分析数据 |

#### 3. 新文件创建决策流程

```typescript
// ✅ 类型定义文件创建决策流程
function determineTypeFileLocation(feature: string): string {
  // 1. 判断是否为通用基础类型
  if (isCommonBaseType(feature)) {
    return 'src/models/common/'
  }

  // 2. 判断是否为基础业务类型
  if (isBasicBusinessType(feature)) {
    return 'src/models/common/basic/'
  }

  // 3. 判断业务领域归属
  const businessDomain = getBusinessDomain(feature)
  return `src/models/${businessDomain}/`
}

// 示例：判断逻辑
const examples = {
  '用户权限管理': 'src/models/common/basic/userModels.ts',
  '系统日志查询': 'src/models/systemManage/logModels.ts',
  '担保品管理': 'src/models/marginTrading/collateralModels.ts',
  '证券信息查询': 'src/models/sec/secInfoModels.ts',
  '压力测试报告': 'src/models/stressTest/stressTestModels.ts'
}
```

### 文件命名规范
```typescript
// ✅ 文件命名规范
// 组件文件：PascalCase
UserManagement.vue
DataTable.vue
SearchForm.vue

// API文件：camelCase + Api后缀
userManagementApi.ts
dataTableApi.ts

// 类型文件：camelCase + Models/Types后缀
userManagementModels.ts
dataTableTypes.ts

// 工具文件：camelCase + Utils后缀
dateUtils.ts
formatUtils.ts

// 常量文件：camelCase + Constants后缀
apiConstants.ts
businessConstants.ts

// 枚举文件：PascalCase + Enum后缀
UserRoleEnum.ts
StatusEnum.ts
```

## 💻 代码风格规范

### 命名规范
```typescript
// ✅ 语义化命名 - 避免模糊名称
// 推荐
const userAccountBalance = ref(0)
const calculateMonthlyInterest = () => {}
const handleUserFormSubmission = () => {}
const isUserDataLoading = ref(false)

// ❌ 避免使用
const val = ref(0)
const calcStart = () => {}
const getValue = () => {}
const hiddenYC = ref(false)
```

### Vue SFC 代码结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入语句（分组排序）
// - Vue 相关导入
// - 第三方库导入  
// - 项目内部导入
// - 类型导入

// 2. 类型声明

// 3. Props 和 Emits 定义

// 4. 组件注册和依赖注入

// 5. 响应式状态

// 6. 计算属性

// 7. 监听器

// 8. 生命周期钩子

// 9. 业务方法

// 10. 导出
</script>

<style scoped>
/* 样式内容 */
</style>
```

### 错误处理规范
```typescript
// ✅ 统一错误处理
const handleApiRequest = async () => {
  try {
    loading.value = true
    const response = await someApi()
    
    // 检查响应状态
    if (response.status !== 200) {
      message.error(response.msg || '请求失败')
      return
    }
    
    // 处理成功逻辑
    data.value = response.data
    message.success('操作成功')
    
  } catch (error) {
    console.error('API请求错误:', error)
    message.error('网络请求失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// ✅ 全局错误处理 - 在 axios 拦截器中
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 403) {
      // 处理权限错误，自动登出
      handleLogout()
    }
    return Promise.reject(error)
  }
)
```

## 🔧 开发实践

### TypeScript 类型定义重复检查机制

#### 1. 类型定义创建前检查流程

```typescript
// ✅ 创建新类型定义前的检查步骤

// 步骤1: 搜索现有类型定义
function searchExistingTypes(typeName: string): string[] {
  const searchPaths = [
    'src/models/**/*.ts',
    'src/types/**/*.ts',
    'src/api/**/*.ts'
  ]

  // 使用 VS Code 全局搜索或命令行工具
  // grep -r "interface ${typeName}" src/models/
  // grep -r "type ${typeName}" src/models/

  return foundFiles
}

// 步骤2: 检查类型相似性
function checkTypeSimilarity(newType: TypeDefinition, existingTypes: TypeDefinition[]): boolean {
  return existingTypes.some(existing =>
    isSimilarStructure(newType, existing) ||
    hasOverlappingFields(newType, existing)
  )
}

// 步骤3: 决策创建或复用
function decideTypeCreation(typeName: string, typeStructure: TypeDefinition): TypeCreationDecision {
  const existingTypes = searchExistingTypes(typeName)

  if (existingTypes.length === 0) {
    return { action: 'create', reason: '无现有类型定义' }
  }

  const similarTypes = checkTypeSimilarity(typeStructure, existingTypes)

  if (similarTypes) {
    return {
      action: 'extend',
      baseType: similarTypes[0],
      reason: '发现相似类型，建议扩展现有类型'
    }
  }

  return { action: 'create', reason: '结构差异较大，创建新类型' }
}
```

#### 2. 类型定义复用和扩展规范

```typescript
// ✅ 类型扩展规范

// 基础类型定义
export interface BaseUserInfo {
  id: number
  username: string
  displayName: string
  createTime: string
}

// 扩展类型定义 - 继承基础类型
export interface DetailedUserInfo extends BaseUserInfo {
  email: string | null
  phone: string | null
  lastLoginTime: string | null
  loginCount: number
}

// 扩展类型定义 - 组合多个类型
export interface UserWithPermissions extends BaseUserInfo {
  permissions: PermissionInfo[]
  roles: RoleInfo[]
}

// ✅ 泛型类型复用
export interface ApiListResponse<T> extends BasicResponseModel<T[]> {
  total: number
  pageNum: number
  pageSize: number
}

// 使用泛型类型
export type UserListResponse = ApiListResponse<UserInfoDTO>
export type RoleListResponse = ApiListResponse<RoleInfoDTO>

// ✅ 工具类型复用
export type PartialUpdate<T> = Partial<Omit<T, 'id' | 'createTime'>>
export type CreateRequest<T> = Omit<T, 'id' | 'createTime' | 'updateTime'>

// 使用工具类型
export type UserUpdateRequest = PartialUpdate<UserInfoDTO>
export type UserCreateRequest = CreateRequest<UserInfoDTO>
```

#### 3. 类型定义搜索和引用最佳实践

```typescript
// ✅ 类型定义文件的导入规范

// 1. 按业务领域分组导入
import type {
  BasicResponseModel,
  PageResponseModel,
  PageRequest
} from '@/models/common/baseResponse'

import type {
  UserInfoDTO,
  UserCreateRequest,
  UserUpdateRequest,
  UserQueryParams
} from '@/models/common/basic/userModels'

import type {
  RoleInfoDTO,
  PermissionInfoDTO
} from '@/models/common/basic/roleModels'

// 2. 使用类型别名简化复杂类型
export type UserApiResponse = BasicResponseModel<UserInfoDTO>
export type UserListApiResponse = PageResponseModel<UserInfoDTO>

// 3. 创建类型索引文件
// src/models/index.ts
export * from './common/baseResponse'
export * from './common/baseRequest'
export * from './common/basic/userModels'
export * from './common/basic/roleModels'
export * from './systemManage/sysManageModels'
// ... 其他导出

// 4. 统一导入方式
import type {
  UserInfoDTO,
  RoleInfoDTO,
  BasicResponseModel
} from '@/models'
```

#### 4. 类型定义检查工具和命令

```bash
# ✅ 搜索现有类型定义的命令

# 1. 搜索接口定义
grep -r "interface.*User" src/models/
grep -r "type.*User" src/models/

# 2. 搜索特定字段
grep -r "username.*:" src/models/
grep -r "email.*:" src/models/

# 3. 使用 VS Code 搜索
# Ctrl+Shift+F 全局搜索
# 搜索模式: interface.*User|type.*User

# 4. 使用 TypeScript 编译器检查
npx tsc --noEmit --skipLibCheck

# 5. 使用 ESLint 检查重复定义
npx eslint src/models/ --ext .ts
```

### 依赖管理规范
```bash
# ✅ 使用包管理器安装依赖
npm install package-name
npm install --save-dev dev-package-name

# ✅ 更新依赖
npm update package-name

# ✅ 移除依赖
npm uninstall package-name

# ❌ 禁止手动编辑 package.json
```

### 性能优化原则
```typescript
// ✅ 路由懒加载
const UserManagement = defineAsyncComponent(() => import('@/views/UserManagement.vue'))

// ✅ 组件懒加载
const HeavyComponent = defineAsyncComponent(() => import('@/components/HeavyComponent.vue'))

// ✅ 分页和虚拟滚动
const tableConfig = {
  pagination: {
    pageSize: 20,
    showSizePicker: true
  },
  virtualScroll: true // 大数据量时启用
}

// ✅ 防抖和节流
import { debounce } from 'lodash-es'

const handleSearch = debounce((keyword: string) => {
  // 搜索逻辑
}, 300)
```

### 安全性考虑
```typescript
// ✅ 输入验证
const validateInput = (input: string): boolean => {
  // XSS 防护
  const sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  return sanitized === input
}

// ✅ 权限验证
const checkPermission = (permission: string): boolean => {
  const userPermissions = store.getters.userPermissions
  return userPermissions.includes(permission)
}

// ✅ Token 管理
const TOKEN_EXPIRE_TIME = 30 * 60 * 1000 // 30分钟
const checkTokenExpiration = () => {
  const tokenTime = localStorage.getItem('tokenTime')
  if (tokenTime && Date.now() - Number(tokenTime) > TOKEN_EXPIRE_TIME) {
    handleLogout()
  }
}
```

## 📝 特殊规范说明

### 1. 常量管理
```typescript
// ✅ 集中管理 localStorage 键名
// src/constants/storageConstants.ts
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  THEME_SETTING: 'theme_setting'
} as const

// ✅ 业务常量枚举
// src/enums/BusinessEnum.ts
export enum BusinessStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

export const BUSINESS_STATUS_LABELS = {
  [BusinessStatus.ACTIVE]: '激活',
  [BusinessStatus.INACTIVE]: '未激活', 
  [BusinessStatus.PENDING]: '待处理'
} as const
```

### 2. 组件设计模式
```vue
<!-- ✅ 表单数据传递模式 -->
<template>
  <ChildForm v-model:form-data="formData" />
</template>

<script setup lang="ts">
// 直接传递整个表单对象，子组件内部处理双向绑定
const formData = reactive({
  name: '',
  email: '',
  phone: ''
})
</script>
```

### 3. Naive UI 特殊规范
```vue
<template>
  <!-- ✅ n-grid 中的组件必须包装在 n-grid-item 中 -->
  <n-grid :cols="3" :x-gap="16">
    <n-grid-item>
      <CustomComponent />
    </n-grid-item>
    <n-grid-item>
      <AnotherComponent />
    </n-grid-item>
  </n-grid>
  
  <!-- ❌ 错误：n-grid-item 不能封装在自定义组件中 -->
</template>
```

## 🚫 禁止事项

1. **❌ 禁止手动编辑包配置文件** (package.json)
2. **❌ 禁止修改全局样式** (可能破坏整个项目)
3. **❌ 禁止使用模糊的函数/变量名** (如 val, calcStart, getValue)
4. **❌ 禁止在组件中直接修改 props**
5. **❌ 禁止忽略错误处理**
6. **❌ 禁止在生产环境中使用 console.log**
7. **❌ 禁止绕过权限检查**
8. **❌ 禁止硬编码业务常量**

## 🌐 API 接口规范

### API 文件组织规范

#### 1. 目录结构映射规则

```
src/api/
├── system/           # 系统管理相关接口
│   ├── userApi.ts           # 用户管理
│   ├── roleApi.ts           # 角色管理
│   ├── MenuApi.ts           # 菜单管理
│   ├── serviceManageApi.ts  # 服务管理
│   └── sysManageApi.ts      # 系统配置
├── business/         # 核心业务接口
│   ├── dataApi.ts           # 业务数据
│   ├── reportApi.ts         # 报表相关
│   └── analysisApi.ts       # 分析功能
├── marginTrading/    # 融资融券业务
│   ├── collateral/          # 担保品管理
│   ├── concentration/       # 集中度管理
│   └── underlying/          # 标的证券
├── sec/              # 证券相关
│   ├── secInfoApi.ts        # 证券信息
│   ├── secProfileApi.ts     # 证券档案
│   └── stockMarketApi.ts    # 股票市场
├── financial/        # 财务相关
├── audit/            # 审计相关
├── stressTest/       # 压力测试
├── marketData/       # 市场数据
└── peerSec/          # 同业证券
```

#### 2. 业务模块判断规则

| 业务类型 | 目录位置 | 文件命名示例 | 说明 |
|----------|----------|--------------|------|
| **系统管理** | `src/api/system/` | `userApi.ts`, `roleApi.ts` | 用户、角色、权限、系统配置 |
| **核心业务** | `src/api/business/` | `dataApi.ts`, `reportApi.ts` | 主要业务逻辑和数据处理 |
| **融资融券** | `src/api/marginTrading/` | `collateralApi.ts` | 融资融券相关业务 |
| **证券管理** | `src/api/sec/` | `secInfoApi.ts` | 证券信息、档案、市场数据 |
| **财务管理** | `src/api/financial/` | `financialApi.ts` | 财务数据、报表、分析 |
| **审计管理** | `src/api/audit/` | `auditApi.ts` | 审计流程、记录、报告 |
| **压力测试** | `src/api/stressTest/` | `stressTestApi.ts` | 压力测试相关功能 |
| **市场数据** | `src/api/marketData/` | `marketDataApi.ts` | 外部市场数据接口 |

#### 3. 新文件创建规则

```typescript
// ✅ 创建新API文件的判断流程
// 1. 确定业务归属
const businessType = determineBusiness(feature)

// 2. 检查现有文件
const existingFile = checkExistingApiFile(businessType)

// 3. 决策逻辑
if (existingFile && relatedFunctions < 10) {
  // 添加到现有文件
  addToExistingFile(existingFile)
} else {
  // 创建新文件
  createNewApiFile(`${businessType}Api.ts`)
}
```

### API 文件格式规范

#### 1. 标准文件模板

```typescript
/**
 * @file: businessModuleApi
 * @description: [业务模块]相关接口
 * @date: YYYY/MM/DD
 * @author: [作者名称]
 */

// ==================== 导入语句 ====================
import { http } from '@/utils/http/axios'
import { BasicResponseModel, PageResponseModel } from '@/models/common/baseResponse'
import { useGlobSetting } from '@/hooks/setting'
import type {
  BusinessDataDTO,
  BusinessQueryParams,
  BusinessCreateRequest
} from '@/models/business/businessModels'

// ==================== API前缀配置 ====================
const globSetting = useGlobSetting()
let { prefix } = globSetting
prefix += '/api/business'

/**
 * 分页查询业务数据
 * @description 根据条件分页查询业务数据列表
 * @param params 查询参数
 * @returns Promise<PageResponseModel<BusinessItemDTO>>
 */
export function getBusinessList(
  params: BusinessQueryParams
): Promise<PageResponseModel<BusinessItemDTO>> {
  return http.request<PageResponseModel<BusinessItemDTO>>({
    url: prefix + '/list',
    method: 'GET',
    params
  })
}
```

#### 2. 函数命名规范

```typescript
// ❌ 避免使用模糊命名
export function getData()             // 太模糊
export function doSomething()         // 无意义
export function handleRequest()       // 不明确
```

#### 3. 参数和返回值规范

```typescript
// ✅ 返回值类型定义
// 单个数据返回
Promise<BasicResponseModel<EntityDTO>>

// 列表数据返回
Promise<PageResponseModel<EntityDTO>>

// 无数据返回
Promise<BasicResponseModel<void>>
```

### 具体示例

#### 1. 业务归属判断示例

```typescript
// 场景：需要创建用户权限管理接口

// ✅ 正确判断
业务类型: 系统管理
目录位置: src/api/system/
文件名称: userPermissionApi.ts
原因: 用户权限属于系统管理范畴

// 场景：需要创建融资融券风险监控接口

// ✅ 正确判断
业务类型: 融资融券业务
目录位置: src/api/marginTrading/
文件名称: riskMonitorApi.ts
原因: 风险监控是融资融券的核心功能
```

#### 2. 完整API文件创建示例

```typescript
/**
 * @file: userPermissionApi
 * @description: 用户权限管理相关接口
 * @date: 2025/07/15
 * @author: AI Assistant
 */

import { http } from '@/utils/http/axios'
import { BasicResponseModel, PageResponseModel } from '@/models/common/baseResponse'
import { useGlobSetting } from '@/hooks/setting'

// 类型定义
export interface UserPermissionDTO {
  /** 权限ID */
  id: number
  /** 用户ID */
  userId: number
  /** 权限代码 */
  permissionCode: string
  /** 权限名称 */
  permissionName: string
  /** 授权时间 */
  grantTime: string
  /** 过期时间 */
  expireTime: string
  /** 状态 */
  status: PermissionStatus
}

export interface PermissionQueryParams {
  pageNum?: number
  pageSize?: number
  userId?: number
  permissionCode?: string
  status?: PermissionStatus
}

export enum PermissionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED'
}

// API前缀
const globSetting = useGlobSetting()
let { prefix } = globSetting
prefix += '/api/system/permission'

// 接口函数
export function getUserPermissionList(
  params: PermissionQueryParams
): Promise<PageResponseModel<UserPermissionDTO>> {
  return http.request<PageResponseModel<UserPermissionDTO>>({
    url: prefix + '/list',
    method: 'GET',
    params
  })
}

export function grantUserPermission(
  userId: number,
  permissionCodes: string[]
): Promise<BasicResponseModel<void>> {
  return http.request<BasicResponseModel<void>>({
    url: prefix + '/grant',
    method: 'POST',
    data: { userId, permissionCodes }
  })
}

export function revokeUserPermission(
  userId: number,
  permissionCodes: string[]
): Promise<BasicResponseModel<void>> {
  return http.request<BasicResponseModel<void>>({
    url: prefix + '/revoke',
    method: 'POST',
    data: { userId, permissionCodes }
  })
}
```

#### 3. 错误处理最佳实践

```typescript
// ✅ 在组件中使用API的标准模式
const handleApiCall = async () => {
  try {
    loading.value = true
    const response = await getUserPermissionList(queryParams.value)

    // 检查响应状态
    if (response.code !== 0) {
      message.error(response.msg || '请求失败')
      return
    }

    // 处理成功数据
    dataList.value = response.data.records
    total.value = response.data.total

  } catch (error) {
    console.error('API调用失败:', error)
    message.error('网络请求失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
```

## ✅ 最佳实践检查清单

### 🚨 **代码风格配置检查（最高优先级）**
- [ ] **检查 `prettier.config.js` 配置** - 确认分号、引号、缩进等设置
- [ ] **检查 `.eslintrc.js` 配置** - 确认代码质量规则
- [ ] **检查 `tsconfig.json` 配置** - 确认 TypeScript 编译选项
- [ ] **使用项目配置的格式化工具** - 不要手动调整代码风格
- [ ] **团队配置一致性** - 确保所有开发者使用相同配置

### 🎯 **基础开发规范**
- [ ] 使用语义化命名
- [ ] 遵循 Vue SFC 代码结构顺序
- [ ] 实现完整的错误处理
- [ ] 使用 emit 进行组件通信
- [ ] 实现权限控制
- [ ] 优化性能（懒加载、防抖等）
- [ ] 遵循 UI/UX 设计规范
- [ ] 使用包管理器管理依赖
- [ ] 编写必要的注释和文档

### 🎨 **UI 交互组件图标规范检查**
- [ ] **所有主要操作按钮都配置了语义化图标**
- [ ] **表单输入控件配置了前缀或后缀图标**
- [ ] **状态指示组件使用了对应的状态图标**
- [ ] **图标选择遵循优先级：项目一致性 > Naive UI > Iconify > 自定义**
- [ ] **同类操作在不同页面使用相同图标**
- [ ] **图标与组件尺寸、颜色协调一致**
- [ ] **异步操作有加载图标或动画**
- [ ] **图标在不同屏幕尺寸下显示正常**
- [ ] **例外情况（纯文本链接等）有明确的设计理由**

### 🚀 **自动导入规范检查**
- [ ] **Vue API 无冗余导入**：确认 `ref`, `computed`, `onMounted` 等无手动导入
- [ ] **Vue Router API 无冗余导入**：确认 `useRoute`, `useRouter` 无手动导入
- [ ] **Naive UI 组件无冗余导入**：确认所有 `N*` 组件无手动导入
- [ ] **项目组件正确使用**：确认 `src/components` 下组件无手动导入
- [ ] **常量正确使用**：确认 `src/constants` 下常量无手动导入
- [ ] **类型导入规范**：确认使用 `import type` 语法导入类型
- [ ] **第三方库手动导入**：确认第三方库组件和函数手动导入
- [ ] **工具函数手动导入**：确认 `src/utils` 下函数手动导入
- [ ] **API 函数手动导入**：确认 `src/api` 下函数手动导入
- [ ] **枚举手动导入**：确认 `src/enums` 下枚举手动导入
- [ ] **IDE 类型提示正常**：确认自动导入的 API 有正确类型提示
- [ ] **构建无错误**：确认自动导入配置不影响项目构建

### 🔷 **TypeScript 规范检查**
- [ ] **所有变量都有明确的类型定义**
- [ ] **函数参数和返回值都有类型注解**
- [ ] **禁止使用 any 类型（除特殊情况）**
- [ ] **接口和类型定义包含完整的 JSDoc 注释**
- [ ] **类型定义文件放置在正确的目录中**
- [ ] **创建新类型前检查是否已存在相似类型**
- [ ] **优先扩展现有类型而非重复创建**
- [ ] **使用泛型和工具类型提高复用性**
- [ ] **HTTP 接口的请求和响应都有对应的类型定义**
- [ ] **枚举和联合类型使用恰当**

### 🌐 **API 接口规范检查**
- [ ] **正确组织API文件结构**
- [ ] **使用标准API文件模板**
- [ ] **API 文件放置在正确的业务目录中**
- [ ] **函数命名遵循语义化规范**
- [ ] **包含完整的错误处理逻辑**
- [ ] **API 响应类型与后端接口文档一致**

### 📁 **文件组织规范检查**
- [ ] **类型定义文件按业务领域正确分类**
- [ ] **新建文件前检查现有文件结构**
- [ ] **文件命名遵循项目约定**
- [ ] **导入语句按规范分组和排序**
- [ ] **避免循环依赖**

---

## 🎓 重要经验教训

### 📚 **ServiceManage.vue 重构经验总结**

#### **🚨 关键教训：配置优先原则**

在 2025年7月15日的 ServiceManage.vue 重构过程中，我们学到了一个重要教训：

**❌ 错误做法**：
- 未检查项目配置就盲目修改代码风格
- 假设项目使用某种代码风格（如不使用分号）
- 手动调整代码格式而不依赖工具

**✅ 正确做法**：
1. **首先检查 `prettier.config.js`** - 确认项目的代码格式化配置
2. **遵循现有配置** - 项目配置 `semi: true` 就应该使用分号
3. **使用自动化工具** - 依赖 Prettier 和 ESLint 进行格式化
4. **团队配置统一** - 确保所有开发者使用相同的配置

#### **🔧 具体案例分析**

**问题场景**：
```javascript
// prettier.config.js 中的配置
module.exports = {
  semi: true,           // 项目要求使用分号
  singleQuote: true,    // 项目要求使用单引号
  tabWidth: 2,          // 项目要求2个空格缩进
}
```

**错误操作**：
```typescript
// ❌ 错误：盲目移除分号
const serviceStatus = ref<T2SdkServiceStatusDTO>(createDefaultServiceStatus())
const clientsStatus = ref<Record<string, T2SdkClientStatusDTO>>({})
```

**正确操作**：
```typescript
// ✅ 正确：遵循项目配置使用分号
const serviceStatus = ref<T2SdkServiceStatusDTO>(createDefaultServiceStatus());
const clientsStatus = ref<Record<string, T2SdkClientStatusDTO>>({});
```

#### **🎯 预防措施**

1. **开发前检查清单**：
   - [ ] 查看 `prettier.config.js` 配置
   - [ ] 查看 `.eslintrc.js` 规则
   - [ ] 确认编辑器配置与项目一致
   - [ ] 运行 `npm run lint` 检查代码风格

2. **团队协作规范**：
   - [ ] 新成员入职时统一配置开发环境
   - [ ] 定期同步项目配置文件
   - [ ] 代码审查时检查格式化一致性
   - [ ] 使用 pre-commit hooks 自动检查

3. **工具化支持**：
   - [ ] 配置 VS Code 自动格式化
   - [ ] 设置保存时自动运行 Prettier
   - [ ] 使用 ESLint 插件实时检查
   - [ ] CI/CD 中加入代码风格检查

#### **💡 核心原则**

> **永远不要假设项目的代码风格配置，必须先检查配置文件！**
>
> 项目配置是团队协作的基础，个人习惯必须服从项目规范。

---

**📌 注意**: 此规范文档是 AI 代码生成的标准参考指南，所有代码生成都应严格遵循以上规范。如有疑问或需要补充，请及时更新此文档。
