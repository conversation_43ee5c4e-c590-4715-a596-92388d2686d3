Vite 环境变量加载优先级（从高到低）：
1. `.env.[mode].local` - 本地环境特定配置（被 git 忽略）
2. `.env.local` - 本地配置（被 git 忽略）
3. `.env.[mode]` - 环境特定配置
4. `.env` - 基础配置

**覆盖规则**：
- 高优先级文件中的变量会覆盖低优先级文件中的同名变量
- 开发环境加载：`.env` → `.env.development`
- 生产环境加载：`.env` → `.env.production`



# port 应用启动端口
VITE_PORT = 8001

# spa-title 应用标题
VITE_GLOB_APP_TITLE = AdminPro

# spa shortname 应用简称
VITE_GLOB_APP_SHORT_NAME = AdminPro

# 生产环境 Mock 数据开关
VITE_GLOB_PROD_MOCK = false
